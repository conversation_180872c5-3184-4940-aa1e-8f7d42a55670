{"applinks": {"apps": [], "details": [{"appIDs": ["26P7QRN49V.com.get.popla.app", "26P7QRN49V.com.get.popla.app.staging", "26P7QRN49V.com.get.popla.app.development"], "components": [{"/": "/user/*", "comment": "Matches any URL with a path that starts with /user/."}, {"/": "/users/*", "comment": "Matches any URL with a path that starts with /users/."}, {"/": "/event/*", "comment": "Matches any URL with a path that starts with /event/."}, {"/": "/events/*", "comment": "Matches any URL with a path that starts with /events/."}, {"/": "/session/*", "comment": "Matches any URL with a path that starts with /session/."}, {"/": "/change-password/*", "?": {"userId": "*", "token": "*"}, "comment": "Matches any URL with a path that starts with /change-password and has query parameters 'userId' and 'token' with any value."}, {"/": "/chats/*", "?": {"userId": "*", "username": "*", "userAvatar": "*"}, "comment": "Matches any URL with a path that starts with /chats and has query parameters 'userId', 'username' and 'userAvatar' with any value."}, {"/": "/policy/*", "exclude": true, "comment": "Matches any URL with a path that starts with /policy/ and instructs the system not to open it as a universal link."}]}]}, "webcredentials": {"apps": ["26P7QRN49V.com.get.popla.app"]}}