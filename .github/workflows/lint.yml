name: Code Check
on:
  push:
    branches:
      - main
  pull_request: {}

concurrency:
  group: ${{ github.job }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  lint:
    name: ⬣ ESLint, ʦ TypeScript, 💅 Prettier, and 🃏 Test
    runs-on: ubuntu-latest
    steps:
      - name: ⬇️ Checkout repo
        uses: actions/checkout@v2

      - name: 🤌 Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9.1.4

      - name: ⎔ Setup node
        uses: actions/setup-node@v3
        with:
          node-version: 22
          cache: 'pnpm'

      - name: 📥 Download deps
        run: pnpm install --frozen-lockfile

      - name: 🔬 Lint
        run: pnpm run lint:strict

      - name: 🔎 Type check
        run: pnpm run typecheck

      - name: 💅 Prettier check
        run: pnpm run format:check

      - name: 🃏 Run jest
        run: pnpm run test
