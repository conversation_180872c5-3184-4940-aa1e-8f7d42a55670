name: release-please
on:
  # !STARTERCONF Choose your preferred event
  # !Option 1: Manual Trigger from GitHub
  workflow_dispatch:
  # !Option 2: Release on every push on main branch
  # push:
  #   branches:
  #     - main
jobs:
  release-please:
    runs-on: ubuntu-latest
    steps:
      - uses: google-github-actions/release-please-action@v3
        with:
          release-type: node
          package-name: release-please-action
