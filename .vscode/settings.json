{
  "css.validate": false,
  "editor.formatOnSave": true,
  "editor.tabSize": 2,
  "editor.codeActionsOnSave": {
    "source.fixAll": "explicit"
  },
  "[css]": { "editor.defaultFormatter": "esbenp.prettier-vscode" },
  // Tailwind CSS Autocomplete, add more if used in projects
  "tailwindCSS.classAttributes": [
    "class",
    "className",
    "classNames",
    "containerClassName"
  ],
  "typescript.preferences.importModuleSpecifier": "non-relative"
}
