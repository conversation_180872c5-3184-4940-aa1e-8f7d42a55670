{"name": "popla-webapp", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "eslint src --fix && pnpm format", "lint:strict": "eslint --max-warnings=0 src", "typecheck": "tsc --noEmit --incremental false", "test:watch": "jest --watch", "test": "jest", "format": "prettier -w .", "format:check": "prettier -c .", "postbuild": "next-sitemap --config next-sitemap.config.js"}, "dependencies": {"@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.2.1", "@internationalized/date": "^3.8.2", "@paystack/inline-js": "^2.22.7", "@radix-ui/react-accordion": "^1.2.12", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-collapsible": "^1.1.12", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-primitive": "^2.1.3", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-toggle": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.8", "@radix-ui/react-use-controllable-state": "^1.2.2", "@react-google-maps/api": "^2.20.7", "@stripe/react-stripe-js": "^3.9.0", "@stripe/stripe-js": "^7.8.0", "@t3-oss/env-nextjs": "^0.13.8", "@tanstack/react-query": "^5.85.3", "@testing-library/dom": "^10.4.1", "@vis.gl/react-google-maps": "^1.5.5", "axios": "^1.11.0", "class-variance-authority": "^0.7.1", "classnames": "^2.5.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.23.12", "generate-password-ts": "^1.6.5", "html-to-image": "^1.11.13", "html2canvas": "^1.4.1", "jsonwebtoken": "^9.0.2", "jwt-decode": "^4.0.0", "ky": "^1.8.2", "libphonenumber-js": "^1.12.12", "lodash": "^4.17.21", "lodash.merge": "^4.6.2", "lucide-react": "^0.539.0", "moment": "^2.30.1", "next": "^15.4.6", "next-auth": "^4.24.11", "next-safe-action": "^8.0.10", "next-themes": "^0.4.6", "rc-steps": "^6.0.1", "rc-tabs": "^15.7.0", "react": "^19.1.1", "react-apple-login": "^1.1.6", "react-apple-signin-auth": "^1.1.2", "react-aria-components": "^1.11.0", "react-collapsed": "^4.2.0", "react-currency-input-field": "^3.10.0", "react-datepicker": "^8.5.0", "react-day-picker": "^9.9.0", "react-dom": "^19.1.1", "react-dropzone": "^14.3.8", "react-google-places-autocomplete": "^4.1.0", "react-hook-form": "^7.62.0", "react-hot-toast": "^2.6.0", "react-icons": "^5.5.0", "react-image-crop": "^11.0.10", "react-modal": "^3.16.3", "react-multi-carousel": "^2.8.6", "react-phone-input-2": "^2.15.1", "react-phone-number-input": "^3.4.12", "react-qr-code": "^2.0.18", "react-query-kit": "^3.3.2", "react-scroll": "^1.9.3", "react-tabs": "^6.1.0", "recharts": "^3.1.2", "stripe": "^18.4.0", "tailwind-merge": "^3.3.1", "tailwind-variants": "^2.1.0", "tailwindcss-animate": "^1.0.7", "universal-cookie": "^8.0.1", "use-debounce": "^10.0.5", "uuid": "^11.1.0", "validator": "^13.15.15", "vaul": "^1.1.2", "zod": "^4.0.17"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@svgr/webpack": "^8.1.0", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.1.12", "@tanstack/eslint-plugin-query": "^5.83.1", "@tanstack/react-query-devtools": "^5.85.3", "@testing-library/jest-dom": "^6.7.0", "@testing-library/react": "^16.3.0", "@types/lodash": "^4.17.20", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@types/react-modal": "^3.16.3", "@types/react-scroll": "^1.8.10", "@types/validator": "^13.15.2", "@typescript-eslint/eslint-plugin": "^8.39.1", "@typescript-eslint/parser": "^8.39.1", "eslint": "^9.33.0", "eslint-config-next": "^15.4.6", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.4", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-unused-imports": "^4.2.0", "husky": "^9.1.7", "jest": "^30.0.5", "lint-staged": "^16.1.5", "next-router-mock": "^1.0.2", "next-sitemap": "^4.2.3", "postcss": "^8.5.6", "prettier": "^3.6.2", "tailwindcss": "^4.1.12", "typescript": "^5.9.2"}, "lint-staged": {"**/*.{js,jsx,ts,tsx}": ["eslint", "prettier -w"], "**/*.{json,css,scss,md,webmanifest}": ["prettier -w"]}, "packageManager": "pnpm@9.1.4"}