import { withAuth } from 'next-auth/middleware';

export default withAuth(
  function middleware() {
    // Add any custom middleware logic here
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        const { pathname } = req.nextUrl;

        const publicRoutes = [
          '/',
          '/register',
          '/password',
          '/onboarding',
          '/login',
          '/forgot-password',
        ];

        if (publicRoutes.includes(pathname)) {
          return true;
        }

        return !!token;
      },
    },
  }
);

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public files (public folder)
     * - auth routes (login, register, etc.)
     */
    '/((?!api|_next/static|_next/image|images|favicon.ico|public|auth).*)',
  ],
};
