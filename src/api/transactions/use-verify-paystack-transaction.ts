import type { AxiosError } from 'axios';
import axios from 'axios';
import { createMutation } from 'react-query-kit';

import { HTTPS_BASE } from '../../api/common';
import type { ErrorResponse } from '../../api/common/types';
import { VERIFY_PAYSTACK_TRANSACTION_URL } from './constants';
import {
  type VerifyPaystackTransactionPayload,
  type VerifyPaystackTransactionResponse,
} from './types';

export const useVerifyPaystackTransaction = createMutation<
  VerifyPaystackTransactionResponse,
  VerifyPaystackTransactionPayload,
  Error
>({
  mutationFn: async ({ transactionRef }) =>
    HTTPS_BASE({
      url: VERIFY_PAYSTACK_TRANSACTION_URL(transactionRef),
      method: 'GET',
    })
      .then((response) => response.data.data)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      }),
});
