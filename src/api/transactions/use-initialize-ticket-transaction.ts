import type { AxiosError } from 'axios';
import axios from 'axios';
import { createMutation } from 'react-query-kit';

import { HTTPS_BASE } from '../../api/common';
import type { ErrorResponse } from '../../api/common/types';
import { INITIALIZE_TRANSACTION_URL } from './constants';
import {
  type InitializeTicketTransactionPayload,
  type InitializeTransactionResponse,
} from './types';

export const useInitializeTicketTransaction = createMutation<
  InitializeTransactionResponse,
  InitializeTicketTransactionPayload,
  Error
>({
  mutationFn: async (data) =>
    HTTPS_BASE({
      url: INITIALIZE_TRANSACTION_URL,
      method: 'POST',
      data,
    })
      .then((response) => response.data.data)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      }),
});
