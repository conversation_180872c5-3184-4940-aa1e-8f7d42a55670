import type { AxiosError } from 'axios';
import axios from 'axios';
import { createMutation } from 'react-query-kit';

import { HTTPS_BASE } from '../../api/common';
import type { ErrorResponse } from '../../api/common/types';
import { SAVE_PAYOUT_INFO_URL } from './constants';
import {
  type PayoutInformationPayload,
  type SaveAccountDetailsResponse,
} from './types';

export const useSavePayoutDetails = createMutation<
  SaveAccountDetailsResponse,
  PayoutInformationPayload,
  Error
>({
  mutationFn: async (data) =>
    HTTPS_BASE({
      url: SAVE_PAYOUT_INFO_URL,
      method: 'POST',
      data,
    })
      .then((response) => response.data.data)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      }),
});
