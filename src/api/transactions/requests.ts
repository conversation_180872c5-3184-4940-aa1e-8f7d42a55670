import { HTTPS_BASE } from '@/api';
import {
  GET_BANKS,
  GET_PAYOUT_INFO_URL,
  LINK_BVN_URL,
  PAYOUT_VERIFICATION_URL,
  REQUEST_PAYOUT_URL,
  SAVE_PAYOUT_INFO_URL,
  VERIFY_BANK,
} from '@/api';
import {
  GET_USER_TRANSACTION_HISTORY_URL,
  INITIALIZE_TRANSACTION_URL,
  STRIP_PAYMENT_INIT_URL,
  TRANSACTION_GRAPH_URL,
  VERIFY_PAYSTACK_TRANSACTION_URL,
} from '@/api';
import {
  GetAccountDetailsResponse,
  GetTransactionHistoryResponse,
  ILinkBVN,
  IMakeWithdrawalRequestResponse,
  InitializeTransactionResponse,
  ITransactionFilterConfig,
  IVerifyBank,
  PayoutInformationPayload,
  PayoutVerificationPayload,
  PayoutVerificationResponse,
  SaveAccountDetailsResponse,
} from '@/api';

import { AxiosResponse } from 'axios';

export const apiInitializeTransaction = async (payload: { amount: number }) =>
  HTTPS_BASE.post<InitializeTransactionResponse>(
    INITIALIZE_TRANSACTION_URL,
    payload
  );
export const apiVerifyBank = async (payload: IVerifyBank) =>
  HTTPS_BASE.post<AxiosResponse, any>(VERIFY_BANK, payload);

export const apiGetBanks = async () => {
  return HTTPS_BASE.get<AxiosResponse<any, any>>(GET_BANKS);
};

export const apiVerifyPaystackTransaction = async (transactionRef: string) => {
  return HTTPS_BASE.get<AxiosResponse<any, any>>(
    VERIFY_PAYSTACK_TRANSACTION_URL(transactionRef)
  );
};

export const apiMakeWithdrawalRequest = async (
  amount: number,
  token: string
): Promise<AxiosResponse<IMakeWithdrawalRequestResponse>> => {
  return HTTPS_BASE.post<IMakeWithdrawalRequestResponse>(
    REQUEST_PAYOUT_URL,
    { amount },
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    }
  );
};

export const apiSaveAccountDetails = async (
  payload: PayoutInformationPayload
) =>
  HTTPS_BASE.post<AxiosResponse<SaveAccountDetailsResponse, any>>(
    SAVE_PAYOUT_INFO_URL,
    payload
  );

export const apiPayoutVerification = async (
  payload: PayoutVerificationPayload
) =>
  HTTPS_BASE.post<AxiosResponse<PayoutVerificationResponse, any>>(
    PAYOUT_VERIFICATION_URL,
    payload
  );

export const apiLinkBnv = async (payload: ILinkBVN) =>
  HTTPS_BASE.post<AxiosResponse<any, any>>(LINK_BVN_URL, payload);

export const apiGetTransactiongraph = async (config = {}) =>
  HTTPS_BASE.get<AxiosResponse<any, any>>(TRANSACTION_GRAPH_URL, config);

export const apiGetAccountDetails = async () =>
  HTTPS_BASE.get<AxiosResponse<GetAccountDetailsResponse, any>>(
    GET_PAYOUT_INFO_URL
  );

export const apiGetTransactionHistory = async (
  config: ITransactionFilterConfig
) => {
  const isAConfigOptionsSet = (): boolean => {
    return Object.keys(config).some(
      (key) => config[key as keyof ITransactionFilterConfig] !== undefined
    );
  };

  let url = `${GET_USER_TRANSACTION_HISTORY_URL(config.id, '')}${
    isAConfigOptionsSet() && '?'
  }`;

  let addedQuery = false;

  if (config.skip !== undefined) {
    if (addedQuery) {
      url += '&';
    }
    url += `skip=${config.skip}`;
    addedQuery = true;
  }

  if (config.take !== undefined) {
    if (addedQuery) {
      url += '&';
    }
    url += `take=${config.take}`;
    addedQuery = true;
  }

  if (config.status !== undefined) {
    if (addedQuery) {
      url += '&';
    }
    url += `status=${config.status}`;
    addedQuery = true;
  }

  if (config.category !== undefined) {
    if (addedQuery) {
      url += '&';
    }
    url += `category=${config.category}`;
    addedQuery = true;
  }

  if (config.type !== undefined) {
    if (addedQuery) {
      url += '&';
    }
    url += `type=${config.type}`;
    addedQuery = true;
  }

  if (config.startDate !== undefined) {
    if (addedQuery) {
      url += '&';
    }
    url += `startDate=${config.startDate}`;
    addedQuery = true;
  }

  if (config.endDate !== undefined) {
    if (addedQuery) {
      url += '&';
    }
    url += `endDate=${config.endDate}`;
    addedQuery = true;
  }

  return HTTPS_BASE.get<GetTransactionHistoryResponse>(url);
};

export const apiInitializeStripePayment = async (payload: { amount: number }) =>
  HTTPS_BASE.post<AxiosResponse<any, any>>(STRIP_PAYMENT_INIT_URL, payload);
