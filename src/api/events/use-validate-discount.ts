import type { AxiosError } from 'axios';
import axios from 'axios';
import { createMutation } from 'react-query-kit';

import { HTTPS_BASE } from '../../api/common';
import type { ErrorResponse } from '../../api/common/types';
import { VALIDATE_EVENT_DISCOUNT } from './constants';
import type {
  ValidateEventDiscountPayload,
  ValidateEventDiscountResponse,
} from './types';

export const useValidateEventDiscount = createMutation<
  ValidateEventDiscountResponse,
  ValidateEventDiscountPayload & { eventId: string },
  Error
>({
  mutationFn: async ({ eventId, ...payload }) =>
    HTTPS_BASE({
      url: VALIDATE_EVENT_DISCOUNT(eventId),
      method: 'POST',
      data: payload,
    })
      .then((response) => response.data.data)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      }),
});
