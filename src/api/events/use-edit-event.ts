import type { AxiosError } from 'axios';
import axios from 'axios';
import { createMutation } from 'react-query-kit';

import { HTTPS_FILE } from '../../api/common';
import type { ErrorResponse, FormData } from '../../api/common/types';
import { EDIT_EVENT_URL } from './constants';
import type { CreateEventPayload, EventCreationResponse } from './types';

export const useEditEvent = createMutation<
  EventCreationResponse,
  { form: Partial<CreateEventPayload | FormData>; id: string },
  Error
>({
  mutationFn: async ({ form, id }) =>
    HTTPS_FILE({
      url: EDIT_EVENT_URL(id),
      method: 'PATCH',
      data: form,
    })
      .then((response) => response.data.data)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      }),
});
