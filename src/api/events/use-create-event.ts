import type { AxiosError } from 'axios';
import axios from 'axios';
import { createMutation } from 'react-query-kit';

import { HTTPS_FILE } from '../../api/common';
import type { ErrorResponse, FormData } from '../../api/common/types';
import { CREATE_EVENT_URL } from './constants';
import type { CreateEventPayload, EventCreationResponse } from './types';

export const useCreateEvent = createMutation<
  EventCreationResponse,
  CreateEventPayload | FormData,
  Error
>({
  mutationFn: async (data) =>
    HTTPS_FILE({
      url: CREATE_EVENT_URL,
      method: 'POST',
      data,
    })
      .then((response) => response.data.data)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      }),
});
