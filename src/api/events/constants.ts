export const BASE_EVENTS_URL = '/events';

export const CREATE_EVENT_URL = `${BASE_EVENTS_URL}`;
export const EDIT_EVENT_URL = (id: string) => `${BASE_EVENTS_URL}/${id}`;

export const ALL_EVENT_URL = (queryString: string) =>
  `${BASE_EVENTS_URL}${queryString ? `?${queryString}` : ''}`;

export const TRENDING_EVENTS_URL = (queryString: string) =>
  `${BASE_EVENTS_URL}/trending${queryString ? `?${queryString}` : ''}`;

export const SINGLE_EVENT_URL = (id: string, queryString: string) =>
  `${BASE_EVENTS_URL}/${id}${queryString ? `?${queryString}` : ''}`;

export const SINGLE_EVENT_WITH_SLUG_URL = (slug: string, queryString: string) =>
  `${BASE_EVENTS_URL}/slug/${slug}${queryString ? `?${queryString}` : ''}`;

export const SEARCH_EVENTS_URL = (queryString: string) =>
  `${BASE_EVENTS_URL}?${queryString}`;

export const DELETE_EVENT_URL = (id: string) => `${BASE_EVENTS_URL}/${id}`;

export const DELETE_EVENT_ARTIST_URL = (id: string, artistId: string) =>
  `${BASE_EVENTS_URL}/${id}/artists/${artistId}`;

export const PURCHASE_EVENT_TICKET_URL = (id: string) =>
  `${BASE_EVENTS_URL}/${id}/tickets/purchase`;

export const RESERVE_EVENT_TICKET_URL = (id: string) =>
  `${BASE_EVENTS_URL}/${id}/tickets/reserve`;

export const UPDATE_RESERVE_EVENT_TICKET_URL = (id: string) =>
  `${BASE_EVENTS_URL}/${id}/tickets/reserve/update`;

export const GET_EVENT_TICKET = (eventId: string, ticketId: string) =>
  `${BASE_EVENTS_URL}/${eventId}/tickets/${ticketId}`;

export const VERIFY_EVENT_TICKET_URL = (eventId: string, ticketId: string) =>
  `${BASE_EVENTS_URL}/${eventId}/tickets/${ticketId}/validate`;

export const EVENT_REGISTRATION_URL = (eventId: string) =>
  `${BASE_EVENTS_URL}/registration/${eventId}`;

export const EVENT_ACCESS_CODES = (eventId: string) =>
  `${BASE_EVENTS_URL}/${eventId}/access-code`;

export const VALIDATE_EVENT_ACCESS_CODE = (eventId: string) =>
  `${BASE_EVENTS_URL}/${eventId}/access-code/validate`;

export const DELETE_EVENT_ACCESS_CODE = (
  eventId: string,
  accessCodeId: string
) => `${BASE_EVENTS_URL}/${eventId}/access-code/${accessCodeId}`;

export const EVENT_DISCOUNT = (eventId: string) =>
  `${BASE_EVENTS_URL}/${eventId}/discounts`;

export const EVENT_GUEST_LIST = (eventId: string, fileType: 'excel' | 'pdf') =>
  `${BASE_EVENTS_URL}/${eventId}/ticket-data/${fileType}`;

export const UPSERT_EVENT_DISCOUNT = (eventId: string, discountId: string) =>
  `${BASE_EVENTS_URL}/${eventId}/discounts/${discountId}`;

export const VALIDATE_EVENT_DISCOUNT = (eventId: string) =>
  `${BASE_EVENTS_URL}/${eventId}/discounts/validate`;

export const UPCOMING_EVENT_URL = `${BASE_EVENTS_URL}/upcoming`;
export const EVENT_CATEGORIES_URL = `${BASE_EVENTS_URL}/category`;

export const SPOTIFY_SEARCH_ARTIST_URL = 'https://api.spotify.com/v1/search?q=';

export const SPOTIFY_SEARCH_ARTIST_BY_ID_URL =
  'https://api.spotify.com/v1/artists?ids=';

export const EVENT_DETAILS_URL = `${BASE_EVENTS_URL}/slug`;

export const FEATURED_EVENTS_URL = (queryString: string) =>
  `${BASE_EVENTS_URL}?${queryString}`;