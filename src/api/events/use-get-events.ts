import type { AxiosError } from 'axios';
import axios from 'axios';
import { createQuery } from 'react-query-kit';

import { constructQueryStrings, HTTPS_BASE } from '../../api/common';
import type { ErrorResponse } from '../../api/common/types';
import { ALL_EVENT_URL } from './constants';
import {
  type AllEventsQueryParams,
  type EventsSearchResultResponse,
} from './types';

export const useGetEvents = createQuery<
  EventsSearchResultResponse,
  AllEventsQueryParams,
  Error
>({
  queryKey: ['getEvents'],
  fetcher: async (queryObj) => {
    const queryParams = constructQueryStrings<AllEventsQueryParams>(queryObj);
    return HTTPS_BASE({
      url: ALL_EVENT_URL(queryParams),
      method: 'GET',
    })
      .then((response) => response.data.data)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      });
  },
});
