import type { AxiosError } from 'axios';
import axios from 'axios';
import { createQuery } from 'react-query-kit';

import type { ErrorResponse } from '../../api/common/types';
import { SPOTIFY_SEARCH_ARTIST_URL } from './constants';
import { type SpotifyArtistsResponse } from './types';

export const useSpotifySearchArtist = createQuery<
  SpotifyArtistsResponse,
  { query: string; accessToken: string },
  Error
>({
  queryKey: ['spotifySearchArtist'],
  fetcher: async ({ accessToken, query }) =>
    axios
      .get<SpotifyArtistsResponse>(
        `${SPOTIFY_SEARCH_ARTIST_URL}${query}&type=artist`,
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: 'Bearer ' + accessToken,
          },
        }
      )
      .then((response) => response.data)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          console.log(
            '🚀 ~ error.response:',
            JSON.stringify(error.request),
            error.response,
            error.response?.data
          );
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      }),
});
