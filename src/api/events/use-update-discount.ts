import type { AxiosError } from 'axios';
import axios from 'axios';
import { createMutation } from 'react-query-kit';

import { HTTPS_BASE } from '../../api/common';
import type { ErrorResponse } from '../../api/common/types';
import { UPSERT_EVENT_DISCOUNT } from './constants';
import type { CreateEventDiscountPayload, EventDiscount } from './types';

export const useUpdateEventDiscount = createMutation<
  EventDiscount,
  Partial<CreateEventDiscountPayload> & { eventId: string; discountId: string },
  Error
>({
  mutationFn: async ({ eventId, discountId, ...payload }) =>
    HTTPS_BASE({
      url: UPSERT_EVENT_DISCOUNT(eventId, discountId),
      method: 'PATCH',
      data: payload,
    })
      .then((response) => response.data.data)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      }),
});
