import type { AxiosError } from 'axios';
import axios from 'axios';
import { createMutation } from 'react-query-kit';

import { HTTPS_BASE } from '../../api/common';
import type { ErrorResponse } from '../../api/common/types';
import { VALIDATE_EVENT_ACCESS_CODE } from './constants';
import type { IEventAccessCode, ValidateEventAccessCodePayload } from './types';

export const useValidateEventAccessCode = createMutation<
  IEventAccessCode,
  ValidateEventAccessCodePayload,
  Error
>({
  mutationFn: async ({ code, eventId }) =>
    HTTPS_BASE({
      url: VALIDATE_EVENT_ACCESS_CODE(eventId),
      method: 'POST',
      data: { code },
    })
      .then((response) => response.data.data)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      }),
});
