import type { AxiosError } from 'axios';
import axios from 'axios';
import { createQuery } from 'react-query-kit';

import { constructQueryStrings, HTTPS_BASE } from '../../api/common';
import type { ErrorResponse } from '../../api/common/types';
import { SEARCH_EVENTS_URL } from './constants';
import {
  type EventSearchInterface,
  type EventsSearchResultResponse,
} from './types';

export const useSearchEvents = createQuery<
  EventsSearchResultResponse,
  EventSearchInterface,
  Error
>({
  queryKey: ['searchEvents'],
  fetcher: async (queryObj) => {
    const queryParams = constructQueryStrings<EventSearchInterface>(queryObj);
    return HTTPS_BASE({
      url: SEARCH_EVENTS_URL(queryParams),
      method: 'GET',
    })
      .then((response) => response.data.data)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      });
  },
});
