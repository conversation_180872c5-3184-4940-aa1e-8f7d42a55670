import type { AxiosError } from 'axios';
import axios from 'axios';
import { createQuery } from 'react-query-kit';

import { HTTPS_BASE } from '../../api/common';
import type { ErrorResponse } from '../../api/common/types';
import { EVENT_ACCESS_CODES } from './constants';
import { type IEventAccessCode } from './types';

export const useGetEventAccessCodes = createQuery<
  IEventAccessCode[],
  { eventId: string },
  Error
>({
  queryKey: ['getEventAccessCodes'],
  fetcher: async ({ eventId }) =>
    HTTPS_BASE({
      url: EVENT_ACCESS_CODES(eventId),
      method: 'GET',
    })
      .then((response) => response.data.data)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      }),
});
