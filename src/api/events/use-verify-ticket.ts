import type { AxiosError } from 'axios';
import axios from 'axios';
import { createQuery } from 'react-query-kit';

import { HTTPS_BASE } from '../../api/common';
import type { ErrorResponse } from '../../api/common/types';
import { VERIFY_EVENT_TICKET_URL } from './constants';
import type { VerifyTicketPayload, VerifyTicketResponse } from './types';

export type TicketVerificationError = {
  status: 'INVALID' | 'USED' | 'UNKNOWN';
  message: string;
};

export const useVerifyTicket = createQuery<
  VerifyTicketResponse,
  VerifyTicketPayload,
  TicketVerificationError
>({
  queryKey: ['verifyEventTicket'],
  fetcher: async ({ eventId, ticketId }) =>
    HTTPS_BASE({
      url: VERIFY_EVENT_TICKET_URL(eventId, ticketId),
      method: 'GET',
    })
      .then((response) => response.data.data)
      .catch((error: AxiosError) => {
        const defaultError: TicketVerificationError = {
          status: 'INVALID',
          message: 'Something went wrong',
        };

        if (axios.isAxiosError<ErrorResponse>(error)) {
          const statusCode = error.response?.data?.statusCode;
          const message = error.response?.data?.message;

          return Promise.reject({
            status: statusCode === 400 ? 'USED' : 'INVALID',
            message: message ?? defaultError.message,
          });
        }

        if (error instanceof Error) {
          return Promise.reject({
            status: 'UNKNOWN',
            message: error.message,
          });
        }

        return Promise.reject(defaultError);
      }),
});
