import type { AxiosError } from 'axios';
import axios from 'axios';
import { createQuery } from 'react-query-kit';

import { constructQueryStrings, HTTPS_BASE } from '../../api/common';
import type { ErrorResponse } from '../../api/common/types';
import { TRENDING_EVENTS_URL } from './constants';
import { type IEvent, type TrendingEventsQueryParams } from './types';

export const useGetTrendingEvents = createQuery<
  IEvent[],
  TrendingEventsQueryParams,
  Error
>({
  queryKey: ['getTrendingEvents'],
  fetcher: async (queryObj) => {
    const queryParams =
      constructQueryStrings<TrendingEventsQueryParams>(queryObj);
    return HTTPS_BASE({
      url: TRENDING_EVENTS_URL(queryParams),
      method: 'GET',
    })
      .then((response) => response.data.data.events)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      });
  },
});
