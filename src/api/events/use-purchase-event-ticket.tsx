import type { AxiosError } from 'axios';
import axios from 'axios';
import { createMutation } from 'react-query-kit';

import { HTTPS_BASE } from '../../api/common';
import type { ErrorResponse } from '../../api/common/types';
import { PURCHASE_EVENT_TICKET_URL } from './constants';
import type {
  PurchaseEventTicketPayload,
  PurchaseEventTicketResponse,
} from './types';

export const usePurchaseEventTicket = createMutation<
  PurchaseEventTicketResponse,
  PurchaseEventTicketPayload,
  Error
>({
  mutationFn: async ({ id, category, userCurrency, discountCode }) =>
    HTTPS_BASE({
      url: PURCHASE_EVENT_TICKET_URL(id),
      method: 'POST',
      data: { category, userCurrency, discountCode },
    })
      .then((response) => response.data.data)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      }),
});
