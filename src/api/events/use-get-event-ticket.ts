import type { AxiosError } from 'axios';
import axios from 'axios';
import { createQuery } from 'react-query-kit';

import { HTTPS_BASE } from '../../api/common';
import type { ErrorResponse } from '../../api/common/types';
import { GET_EVENT_TICKET } from './constants';
import { type ITicketDetails, type VerifyTicketPayload } from './types';

export const useGetEventTicket = createQuery<
  ITicketDetails,
  VerifyTicketPayload,
  Error
>({
  queryKey: ['getEventTicket'],
  fetcher: async ({ eventId, ticketId }) =>
    HTTPS_BASE({
      url: GET_EVENT_TICKET(eventId, ticketId),
      method: 'GET',
    })
      .then((response) => response.data.data)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      }),
});
