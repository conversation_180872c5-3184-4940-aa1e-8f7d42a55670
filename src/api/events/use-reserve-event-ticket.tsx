import type { AxiosError } from 'axios';
import axios from 'axios';
import { createMutation } from 'react-query-kit';

import { HTTPS_BASE } from '../../api/common';
import type { ErrorResponse } from '../../api/common/types';
import { RESERVE_EVENT_TICKET_URL } from './constants';
import type {
  ReserveEventTicketPayload,
  ReserveEventTicketResponse,
} from './types';

export const useReserveEventTicket = createMutation<
  ReserveEventTicketResponse,
  ReserveEventTicketPayload,
  Error
>({
  mutationKey: ['reserveEventTicket'],
  mutationFn: async ({ id, reservations, userCurrency }) =>
    HTTPS_BASE({
      url: RESERVE_EVENT_TICKET_URL(id),
      method: 'POST',
      data: { eventId: id, userCurrency, reservations },
    })
      .then((response) => response.data.data)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      }),
});
