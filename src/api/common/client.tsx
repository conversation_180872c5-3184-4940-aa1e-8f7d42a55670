import axios, { type AxiosError } from 'axios';

import { authorizationInterceptor } from './interceptors';

const API_URL = process.env.NEXT_PUBLIC_API_BASE_URL;

const TIMEOUT = 10 * 60 * 1000;

export const HTTPS_FILE = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'multipart/form-data',
  },
});

export const HTTPS_BASE = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

export const HTTPS = axios.create({
  timeout: TIMEOUT,
  headers: {
    'Content-Type': 'application/json',
  },
});

HTTPS_BASE.interceptors.request.use(authorizationInterceptor, (error) => {
  return Promise.reject(error);
});

HTTPS_BASE.interceptors.response.use(
  (value) => value,
  (error: AxiosError) => {
    console.log(
      'error - interceptor',
      JSON.stringify(
        error.response ? error.response?.data : error.message || error
      )
    );
    return Promise.reject(error);
  }
);

HTTPS_FILE.interceptors.request.use(authorizationInterceptor, (error) => {
  return Promise.reject(error);
});
