'use client';
import {
  QueryCache,
  QueryClient,
  QueryClientProvider,
  HydrationBoundary,
  defaultShouldDehydrateQuery,
} from '@tanstack/react-query';

let browserQueryClient: QueryClient | undefined;

function makeQueryClient() {
  return new QueryClient({
    queryCache: new QueryCache(),
    defaultOptions: {
      queries: {
        refetchOnWindowFocus: false,
        staleTime: 60 * 1000, // 1 minute
      },
      dehydrate: {
        // Include in-flight queries so streaming SSR can hydrate them
        shouldDehydrateQuery: (query) =>
          defaultShouldDehydrateQuery(query) ||
          query.state.status === 'pending',
      },
    },
  });
}

export function getQueryClient() {
  // Always new instance on server
  if (typeof window === 'undefined') return makeQueryClient();

  // Reuse instance in browser
  if (!browserQueryClient) browserQueryClient = makeQueryClient();
  return browserQueryClient;
}

export function APIProvider({
  children,
  dehydratedState,
}: {
  children: React.ReactNode;
  dehydratedState?: any;
}) {
  const queryClient = getQueryClient();

  return (
    <QueryClientProvider client={queryClient}>
      <HydrationBoundary state={dehydratedState}>{children}</HydrationBoundary>
    </QueryClientProvider>
  );
}
