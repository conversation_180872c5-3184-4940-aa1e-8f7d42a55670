import type {
  GetNextPageParamFunction,
  GetPreviousPageParamFunction,
} from '@tanstack/react-query';

import type { PaginateQuery } from './types';

type KeyParams = {
  [key: string]: any;
};
export const DEFAULT_LIMIT = 10;

export function getQueryKey<T extends KeyParams>(key: string, params?: T) {
  return [key, ...(params ? [params] : [])];
}

// for infinite query pages to flatList data
export function normalizePages<T>(pages?: PaginateQuery<T>[]): T[] {
  return pages
    ? pages.reduce((prev: T[], current) => [...prev, ...current.results], [])
    : [];
}

// export function getUrlParameters(
//   url: string | null
// ): Record<string, string> | null {
//   if (!url) return null;

//   const queryString = url.split('?')[1];
//   if (!queryString) return {};

//   return queryString.split('&').reduce((acc: Record<string, string>, param) => {
//     const [key, value] = param.split('=');
//     if (key) {
//       acc[decodeURIComponent(key)] = decodeURIComponent(value || '');
//     }
//     return acc;
//   }, {});
// }

// a function that accept a url and return params as an object
export function getUrlParameters(
  url: string | null
): { [k: string]: string } | null {
  if (url === null) {
    return null;
  }

  let regex = /[?&]([^=#]+)=([^&#]*)/g,
    params = {},
    match;

  while ((match = regex.exec(url))) {
    if (match[1] !== null) {
      //@ts-ignore
      params[match[1]] = match[2];
    }
  }
  return params;
}

export const getPreviousPageParam: GetNextPageParamFunction<
  unknown,
  PaginateQuery<unknown>
> = (page) => getUrlParameters(page.previous)?.offset ?? null;

export const getNextPageParam: GetPreviousPageParamFunction<
  unknown,
  PaginateQuery<unknown>
> = (page) => getUrlParameters(page.next)?.offset ?? null;

/**
 * Constructs a URL query string from an object of parameters
 * - Handles primitive values, arrays, objects, and nested structures
 * - Supports customizable encoding and serialization options
 * - Properly handles null and undefined values
 *
 * @param params - Object containing query parameters
 * @param options - Configuration options for query string construction
 * @returns Formatted query string starting without the '?' character
 */
export const constructQueryStrings = <T extends Record<string, any>>(
  params: T,
  options: {
    /**
     * Whether to encode parameter names and values using encodeURIComponent
     * @default true
     */
    encode?: boolean;

    /**
     * How to handle null or undefined values
     * - 'ignore': Skip these parameters (default)
     * - 'empty': Include as empty string (e.g., 'param=')
     * - 'null': Include as the string 'null' (e.g., 'param=null')
     * @default 'ignore'
     */
    nullHandling?: 'ignore' | 'empty' | 'null';

    /**
     * Custom value transformer function
     * Allows custom handling of specific value types
     */
    valueTransformer?: (key: string, value: any) => string | string[] | null;

    /**
     * Whether to include array indices in parameter names
     * - true: 'items[0]=value&items[1]=value'
     * - false: 'items=value&items=value' (default)
     * @default false
     */
    arrayIndices?: boolean;

    /**
     * Separator for array values
     * If provided, array values will be joined using this separator
     * instead of creating multiple parameters
     * Example: { arrayFormat: ',' } → 'colors=red,green,blue'
     */
    arrayFormat?: string;
  } = {}
): string => {
  const {
    encode = true,
    nullHandling = 'ignore',
    valueTransformer,
    arrayIndices = false,
    arrayFormat,
  } = options;

  // Internal recursive function to handle nested objects
  const processParams = (
    obj: Record<string, any>,
    prefix = ''
  ): [string, string][] => {
    const result: [string, string][] = [];

    for (const [key, value] of Object.entries(obj)) {
      // Skip null/undefined based on options
      if (value === null || value === undefined) {
        if (nullHandling === 'ignore') continue;
        if (nullHandling === 'empty') {
          result.push([prefix ? `${prefix}[${key}]` : key, '']);
        } else if (nullHandling === 'null') {
          result.push([prefix ? `${prefix}[${key}]` : key, 'null']);
        }
        continue;
      }

      // Apply custom transformer if provided
      if (valueTransformer) {
        const transformed = valueTransformer(
          prefix ? `${prefix}[${key}]` : key,
          value
        );
        if (transformed === null) continue;

        if (typeof transformed === 'string') {
          result.push([prefix ? `${prefix}[${key}]` : key, transformed]);
          continue;
        } else if (Array.isArray(transformed)) {
          transformed.forEach((item) => {
            result.push([prefix ? `${prefix}[${key}]` : key, item]);
          });
          continue;
        }
      }

      // Handle different value types
      if (Array.isArray(value)) {
        // Use specified array format if provided
        if (arrayFormat) {
          result.push([
            prefix ? `${prefix}[${key}]` : key,
            value.join(arrayFormat),
          ]);
        } else {
          // Process array items
          value.forEach((item, index) => {
            if (item === null || item === undefined) {
              if (nullHandling === 'ignore') return;
              item = nullHandling === 'null' ? 'null' : '';
            }

            const paramName = arrayIndices
              ? `${prefix ? `${prefix}[${key}]` : key}[${index}]`
              : prefix
              ? `${prefix}[${key}]`
              : key;

            if (typeof item === 'object' && item !== null) {
              // For object items in array
              result.push(...processParams(item, paramName));
            } else {
              result.push([paramName, String(item)]);
            }
          });
        }
      } else if (typeof value === 'object') {
        // Process nested objects
        result.push(
          ...processParams(value, prefix ? `${prefix}[${key}]` : key)
        );
      } else {
        // Handle simple values
        result.push([prefix ? `${prefix}[${key}]` : key, String(value)]);
      }
    }

    return result;
  };

  // Process parameters and build the query string
  return processParams(params)
    .map(([key, value]) => {
      const encodedKey = encode ? encodeURIComponent(key) : key;
      const encodedValue = encode ? encodeURIComponent(value) : value;
      return `${encodedKey}=${encodedValue}`;
    })
    .join('&');
};

export const toQueryString = (data: Record<string, any>) => {
  return (
    '?' +
    Object.entries(data)
      .map(
        ([key, value]) =>
          `${encodeURIComponent(key)}=${encodeURIComponent(value)}`
      )
      .join('&')
  );
};
