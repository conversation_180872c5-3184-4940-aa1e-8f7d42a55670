import type { AxiosError } from 'axios';
import axios from 'axios';
import { createMutation } from 'react-query-kit';

import { type UserObjectData } from '../auth';
import { HTTPS_BASE } from '../common';
import type { ErrorResponse } from '../common/types';
import { SEND_FEEDBACK_URL } from './constants';
import { type SendFeebackInterface } from './types';

export const useSendFeedback = createMutation<
  UserObjectData,
  {
    userId: string;
    payload: SendFeebackInterface;
  },
  Error
>({
  mutationFn: async ({ payload }) =>
    HTTPS_BASE({
      url: SEND_FEEDBACK_URL,
      method: 'POST',
      data: payload,
    })
      .then((response) => response.data.data)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      }),
});
