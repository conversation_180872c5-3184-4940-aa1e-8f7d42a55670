export enum ReportEnum {
  LIVE_SESSION = 'LIVE_SESSION',
  EVENT = 'EVENT',
  APP = 'APP',
  TRANSACTION = 'TRANSACTION',
}
import {
  type TransactionCategory,
  type TransactionStatus,
  type TransactionType,
} from '@/api';

import type {
  LocationObject,
  SocialsTypes,
  USER_ROLE,
} from '../auth';

import {type UserObjectData} from '../auth';

import { IdVerificationData } from '@/api';

export interface IUpdatePasswordDto {
  oldPassword: string;
  password: string;
  token?: string;
}

export interface UserSearchInterface {
  skip: number;
  take: number;
  roles?: USER_ROLE[];
  userIds?: string[];
  usernames?: string[];
  query?: string;
  paginate?: boolean;
}

export interface SearchResultResponse {
  total: number;
  users: UserObjectData[];
}

export type HomeDataPayload = {
  lat: number;
  lng: number;
  userCurrency: string;
};

export interface TrendingDjResponse {
  id: string;
  profileImageUrl: string;
  fullName: string;
  username: string;
  questerCount: number;
}

export type HomeDataResponse = {
  user: UserObjectData;
  trendingDJs: TrendingDjResponse[];
};

export interface UpdateProfileInterface {
  bio?: string;
  socials?: SocialsTypes;
  location?: LocationObject;
  fullName?: string;
  phoneNumber?: string;
  role?: string;
  category?: string;
  genres?: string[];
  gender?: string;
  profileImageUrl?: object;
  emailNotification?: boolean;
  pushNotification?: boolean;
  allowNearbyDiscovery?: boolean;
}

export interface PatchFCMTokenInterface {
  userId: string;
  fcmToken: string;
}

export interface SendFeebackInterface {
  email: string;
  message: string;
  fullName: string;
}

export interface ReportProblemInterface {
  title: string;
  type: NonNullable<ReportEnum | undefined>;
  description: string;
  songRequestId?: string;
}

import {type IEventsWithTicket} from '../events';

export interface UserTicketsResponse {
  eventsWithTicket: IEventsWithTicket[];
  total: number;
}

export type AddFavouritePayload = {
  type: 'EVENT' | 'ACCOUNT';
  accountId: string | null;
  eventId: string | null;
};

export type RemoveFavouritePayload = {} & AddFavouritePayload;

export type PinPayload = {
  pin: string;
};

export interface IEmailUpdate {
  newEmail: string;
  password: string;
}

export interface IDeleteUserAccount {
  password: string;
}

export interface UpdateProfileInterface {
  bio?: string;
  socials?: SocialsTypes;
  location?: LocationObject;
  fullName?: string;
  phoneNumber?: string;
}
