import type { AxiosError, AxiosResponse } from 'axios';
import axios from 'axios';
import { createMutation } from 'react-query-kit';

import { HTTPS_BASE } from '../common';
import type { ErrorResponse } from '../common/types';
import { VERIFY_USERNAME } from './constants';
import { VerifyUsername } from '@/api/auth';

export const useVerifyUserUsername = createMutation<
  VerifyUsername,
  VerifyUsername,
  Error
>({
  mutationFn: async (data) =>
    HTTPS_BASE({
      url: VERIFY_USERNAME,
      method: 'POST',
      data,
    })
      .then((response) => response.data.data)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      }),
});

export const apiVerifyUserUsername = async (
  username: string,
  signal?: AbortSignal
) =>
  HTTPS_BASE.get<AxiosResponse<VerifyUsername, any>>(
    `${VERIFY_USERNAME}/${username}`,
    { signal }
  );
