// USER
export const BASE_USER_URL = '/users';
export const USER_PASSWORD_RESET_URL = (email: string) =>
  `${BASE_USER_URL}/password/reset?email=${email}`;
export const VERIFY_USERNAME = `${BASE_USER_URL}/verify-username`;
export const SEARCH_USERS_URL = (queryString: string) =>
  `${BASE_USER_URL}?${queryString}`;
export const USER_HOME_URL = (queryString: string) =>
  `${BASE_USER_URL}/home${queryString}`;
export const PROFILE_UPDATE_URL = (userId: string) =>
  `${BASE_USER_URL}/${userId}/profile`;
export const FCMTOKEN_URL = (userId: string) =>
  `${BASE_USER_URL}/${userId}/fcmToken`;
export const GET_USER_TRANSACTION_HISTORY_URL = (
  userId: string,
  queryParams: string
) =>
  `${BASE_USER_URL}/${userId}/transactions${
    queryParams ? `?${queryParams}` : ''
  }`;
export const SEND_FEEDBACK_URL = '/contact-us';
export const USER_TICKETS_URL = (userId: string) =>
  `${BASE_USER_URL}/${userId}/tickets`;

export const REPORT_PROBLEM_URL = '/reports';

export const FAVORITE_URL = '/users/favorites';

export const TRENDING_DJS_URL = `${BASE_USER_URL}/dj/trending`;

export const PROFILE_IMAGE_UPLOAD_URL = (userId: string) =>
  `${BASE_USER_URL}/${userId}/media`;

export const CREATE_PIN_URL = `${BASE_USER_URL}/wallet/pin`;
export const VALIDATE_PIN_URL = `${BASE_USER_URL}/wallet/validate-pin`;

export const NEARBY_USERS_URL = (distance: string) =>
  `${BASE_USER_URL}/nearby${distance ? `?distance=${distance}` : ''}`;

export const GET_USER_URL = (userId: string) => `${BASE_USER_URL}/${userId}`;

export const UPDATE_USER_EMAIL = `${BASE_USER_URL}/email/change`;
export const UPDATE_PASSWORD = `${BASE_USER_URL}/password/change`;
export const DELETE_USER_ACCOUNT = `${BASE_USER_URL}/`;

export const EDIT_PERSONAL_INFO = (id: string) => {
  if (!id) {
    throw new Error('User ID is required to edit personal info');
  }
  return `${BASE_USER_URL}/${id}/personal-info`;
};
export const EDIT_SOCIAL_INFO = (id: string) => {
  if (!id) {
    throw new Error('User ID is required to edit social info');
  }
  return `${BASE_USER_URL}/${id}/social-info`;
};

export const CREATE_WALLET_URL = `${BASE_USER_URL}/create-wallet`;

export const VERIFY_PASSWORD_URL = `${BASE_USER_URL}/password/validate`;

export const UPDATE_WALLET_TRANSACTION_PIN = `${BASE_USER_URL}/wallet/pin/update`;

export const VERIFY_WALLET_TRANSACTION_PIN = `${BASE_USER_URL}/wallet/validate-pin`;

export const CREATE_WALLET_TRANSACTION_PIN = `${BASE_USER_URL}/wallet/pin`;

export const CURRENT_USER_URL = `${BASE_USER_URL}/me`;
export const EDIT_USER_PROFILE_URL = (id: string) =>
  `${BASE_USER_URL}/${id}/profile`;
export const USER_EVENT_URL = (id: string) => `${BASE_USER_URL}/${id}/events`;
export const USER_TICKET_URL = (id: string) => `${BASE_USER_URL}/${id}/tickets`;
export const USER_FAVORITES_EVENTS_URL = `${BASE_USER_URL}/favorites?type=EVENT`;
export const USER_UPDATE_FAVORITES_EVENTS_URL = `${BASE_USER_URL}/favorites`;
export const USER_TICKET_BY_ID = (eventId: string) =>
  `${BASE_USER_URL}/tickets/${eventId}`;

export const VERIFY_SIGNED_USERNAME_URL = (username: string) =>
  `${BASE_USER_URL}/verify-username/${username}`;