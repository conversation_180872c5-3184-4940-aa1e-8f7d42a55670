import type { AxiosError } from 'axios';
import axios from 'axios';
import { createQuery } from 'react-query-kit';

import { constructQueryStrings, HTTPS_BASE } from '../common';
import type { ErrorResponse } from '../common/types';
import { SEARCH_USERS_URL } from './constants';
import type { SearchResultResponse, UserSearchInterface } from './types';
import { type EventSearchInterface } from '../events';

export type SearchUsersPayload = { queryObj: Partial<UserSearchInterface> };

export const useApiSearchUsers = createQuery<
  SearchResultResponse,
  SearchUsersPayload,
  Error
>({
  queryKey: ['searchUsers'],
  fetcher: async ({ queryObj }) => {
    if (queryObj.skip === 0) {
      delete queryObj.skip;
    }
    const queryString =
      constructQueryStrings<
        Partial<UserSearchInterface & EventSearchInterface>
      >(queryObj);
    return HTTPS_BASE({
      url: SEARCH_USERS_URL(queryString),
      method: 'GET',
    })
      .then((response) => response.data.data)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      });
  },
});
