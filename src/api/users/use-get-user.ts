import axios, { type AxiosError } from 'axios';
import { createQuery } from 'react-query-kit';

import type { UserObjectData } from '../auth/types';
import { HTTPS_BASE } from '../common';
import { type ErrorResponse } from '../common/types';

type Variables = { id: string };
type Response = UserObjectData;

export const useGetUser = createQuery<Response, Variables, AxiosError>({
  queryKey: ['users'],
  fetcher: async ({ id }) =>
    HTTPS_BASE.get(`users/${id}`)
      .then((response) => response.data.data)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      }),
});
