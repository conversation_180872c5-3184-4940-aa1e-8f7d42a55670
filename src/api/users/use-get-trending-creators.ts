import type { AxiosError } from 'axios';
import axios from 'axios';
import { createQuery } from 'react-query-kit';

import { HTTPS_BASE } from '../common';
import type { ErrorResponse } from '../common/types';
import type { TrendingDjResponse } from '@/api';
import { TRENDING_DJS_URL } from './constants';

export const useGetTrendingCreators = createQuery<
  TrendingDjResponse[],
  void,
  Error
>({
  queryKey: ['getTrendingCreators'],
  fetcher: async () =>
    HTTPS_BASE({
      url: TRENDING_DJS_URL,
      method: 'GET',
    })
      .then((response) => response.data.data)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }
        throw new Error(message);
      }),
});
