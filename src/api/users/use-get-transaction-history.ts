import type { AxiosError } from 'axios';
import axios from 'axios';
import { createQuery } from 'react-query-kit';

import { constructQueryStrings, HTTPS_BASE } from '../common';
import type { ErrorResponse } from '../common/types';
import { GET_USER_TRANSACTION_HISTORY_URL } from './constants';
import {
  type GetTransactionHistoryResponse,
  type ITransactionFilterConfig,
} from '@/api';

export const useGetTransactionHistory = createQuery<
  GetTransactionHistoryResponse,
  ITransactionFilterConfig,
  Error
>({
  queryKey: ['getTransactionHistory'],
  fetcher: async (queryObj) => {
    const queryParams =
      constructQueryStrings<ITransactionFilterConfig>(queryObj);
    return HTTPS_BASE({
      url: GET_USER_TRANSACTION_HISTORY_URL(queryObj.id, queryParams),
      method: 'GET',
    })
      .then((response) => response.data.data)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      });
  },
});
