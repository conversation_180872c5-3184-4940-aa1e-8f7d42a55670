import type { AxiosError } from 'axios';
import axios from 'axios';
import { createQuery } from 'react-query-kit';

import type { UserObjectData } from '../auth/types';
import { HTTPS_BASE } from '../common';
import { type ErrorResponse } from '../common/types';
import { NEARBY_USERS_URL } from './constants';

type Variables = { distance: string };
type Response = UserObjectData[];

export const useGetNearbyUsers = createQuery<Response, Variables, AxiosError>({
  queryKey: ['nearby-users'],
  fetcher: async ({ distance }) => {
    return HTTPS_BASE({
      url: NEARBY_USERS_URL(distance),
      method: 'GET',
    })
      .then((response) => response.data.data)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      });
  },
});
