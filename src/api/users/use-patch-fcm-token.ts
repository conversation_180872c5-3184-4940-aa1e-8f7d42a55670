import type { AxiosError } from 'axios';
import axios from 'axios';
import { createMutation } from 'react-query-kit';

import { HTTPS_BASE } from '../common';
import type { ErrorResponse } from '../common/types';
import { FCMTOKEN_URL } from './constants';
import { type PatchFCMTokenInterface } from './types';

export const usePatchFcmToken = createMutation<
  undefined,
  PatchFCMTokenInterface,
  Error
>({
  mutationFn: async ({ userId, fcmToken }) =>
    HTTPS_BASE({
      url: FCMTOKEN_URL(userId),
      method: 'PATCH',
      data: { fcmToken },
    })
      .then((response) => response.data.data)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      }),
});
