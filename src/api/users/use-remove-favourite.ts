import type { AxiosError } from 'axios';
import axios from 'axios';
import { createMutation } from 'react-query-kit';

import { HTTPS_BASE } from '../common';
import type { ErrorResponse } from '../common/types';
import { FAVORITE_URL } from './constants';
import { type RemoveFavouritePayload } from './types';

export const useRemoveFavourite = createMutation<
  undefined,
  RemoveFavouritePayload,
  Error
>({
  mutationFn: async (data) =>
    HTTPS_BASE({
      url: FAVORITE_URL,
      method: 'DELETE',
      data,
    })
      .then((response) => response.data.data)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      }),
});
