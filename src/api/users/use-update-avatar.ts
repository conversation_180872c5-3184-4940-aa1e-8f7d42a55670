import { createMutation } from 'react-query-kit';

import { type UserObjectData } from '../auth';
import { HTTPS_FILE } from '../common';
import { type FormData } from '../common/types';
import { PROFILE_IMAGE_UPLOAD_URL } from './constants';

export const useUploadProfileImage = createMutation<
  UserObjectData,
  {
    userId: string;
    payload: FormData;
  },
  Error
>({
  mutationFn: async ({ userId, payload }) =>
    HTTPS_FILE({
      url: PROFILE_IMAGE_UPLOAD_URL(userId),
      method: 'PATCH',
      data: payload,
    })
      .then((response) => response.data.data)
      .catch((error) => {
        let message = 'Something went wrong';

        if (error.response) {
          message = error.response.data.message || message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      }),
});
