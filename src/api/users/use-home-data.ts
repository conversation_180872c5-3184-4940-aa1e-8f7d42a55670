import type { AxiosError } from 'axios';
import axios from 'axios';
import { createQuery } from 'react-query-kit';

import { HTTPS_BASE, toQueryString } from '../common';
import type { ErrorResponse } from '../common/types';
import { USER_HOME_URL } from './constants';
import type { HomeDataPayload, HomeDataResponse } from './types';

export const useGetHomeData = createQuery<
  HomeDataResponse,
  HomeDataPayload,
  Error
>({
  queryKey: ['getHomeData'],
  fetcher: async (payload) =>
    HTTPS_BASE({
      url: USER_HOME_URL(toQueryString(payload)),
      method: 'GET',
    })
      .then((response) => response.data.data)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      }),
});
