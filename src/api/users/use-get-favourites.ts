import type { AxiosError } from 'axios';
import axios from 'axios';
import { createQuery } from 'react-query-kit';

import { type UserObjectData } from '../auth';
import { HTTPS_BASE } from '../common';
import type { ErrorResponse } from '../common/types';
import { type IEvent } from '../events';
import { FAVORITE_URL } from './constants';

export const useGetUserFavourites = createQuery<
  IEvent[] | UserObjectData[],
  { type: 'ACCOUNT' | 'EVENT' },
  Error
>({
  queryKey: ['getUserFavourites'],
  fetcher: async ({ type }) =>
    HTTPS_BASE({
      url: `${FAVORITE_URL}?type=${type}`,
      method: 'GET',
    })
      .then((response) => response.data.data)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      }),
});
