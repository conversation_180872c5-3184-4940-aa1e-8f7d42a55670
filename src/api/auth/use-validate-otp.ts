import type { AxiosError } from 'axios';
import axios from 'axios';
import { createMutation } from 'react-query-kit';

import { HTTPS_BASE } from '../common';
import type { ErrorResponse } from '../common/types';
import { VALIDATE_OTP_URL } from './constants';
import type { OtpPayload, VerifyOtpResponse } from './types';

export const useValidateOtp = createMutation<
  VerifyOtpResponse,
  OtpPayload,
  Error
>({
  mutationFn: async (data) =>
    HTTPS_BASE({
      url: VALIDATE_OTP_URL,
      method: 'POST',
      data,
    })
      .then((response) => response.data.data)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      }),
});
