import type { AxiosError } from 'axios';
import axios from 'axios';
import { createQuery } from 'react-query-kit';

import { constructQueryStrings, HTTPS_BASE } from '../common';
import type { ErrorResponse } from '../common/types';
import { type HomeDataPayload } from '../users';
import { AUTH_CURRENT_USER_URL } from './constants';
import { type UserObjectData } from './types';

export const useGetLoggedInUser = createQuery<
  UserObjectData,
  Pick<HomeDataPayload, 'userCurrency'>,
  Error
>({
  queryKey: ['getUser'],
  fetcher: async (queryObj) => {
    const queryParams =
      constructQueryStrings<Pick<HomeDataPayload, 'userCurrency'>>(queryObj);
    return HTTPS_BASE({
      url: AUTH_CURRENT_USER_URL(queryParams),
      method: 'GET',
    })
      .then((response) => response.data.data)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      });
  },
});
