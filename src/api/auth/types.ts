import { EventItem, SearchResultInterface } from '@/api';
import { Session, User } from 'next-auth';
import { JWT } from 'next-auth/jwt';

export const USER_ROLE_VALUES = ['QUESTER', 'CREATOR'] as const;

export type USER_ROLE = (typeof USER_ROLE_VALUES)[number];

export const CREATOR_CATEGORIES = [
  'ANCHOR',
  'DJ',
  'ORGANIZER',
  'ORGANIZATION',
  'OTHERS',
] as const;

export type CREATOR_CATEGORY = (typeof CREATOR_CATEGORIES)[number];

export type LoginPayload = {
  email: string;
  password: string;
  country?: string;
};
export type SocialLoginPayload = {
  code?: string;
  idToken?: string | null;
  type: 'apple' | 'google';
};

export interface LoginResponse {
  user: UserObjectData;
  token: string;
}

export type AccountCreationPayload = {
  role: USER_ROLE;
  email: string;
  password: string;
  fullName: string;
  username: string;
  location: LocationObject;
  isSocialSetup?: boolean;
};

export type CreatorAccountCreationPayload = {
  role: USER_ROLE;
  email: string;
  password: string;
  fullName: string;
  username: string;
  location?: LocationObject;
  category: CREATOR_CATEGORY;
  avatar?: string;
  bio: string;
  gender: string;
};

export interface AccountCreationResponse {
  user: UserObjectData;
  expiresAt: string;
  token: string;
}

export type OtpPayload = {
  email: string;
  otp: string;
};

export type VerifyOtpResponse = {
  expiresAt: string;
  token: string;
  user: Pick<
    UserObjectData,
    'email' | 'fullName' | 'id' | 'isEmailVerified' | 'role' | 'username'
  >;
};

export interface UserObjectData {
  id: string;
  userId: string;
  username: string;
  email: string;
  bio?: string | null;
  url?: string | null;
  socials?: SocialsTypes | null;
  location?: LocationObject | null;
  fullName: string;
  phoneNumber: string;
  rcNumber: string | null;
  role: USER_ROLE;
  createdAt: any;
  updatedAt: any;
  isSuspended: boolean;
  fcmToken: string | null;
  meta: {
    hasLinkedBVN: boolean;
    blockUsernameChange: boolean;
    usernameChangedCount: number;
  } | null;
  bannerUrl: string;
  profileImageUrl: string;
  pin: string | null;
  isIdVerified: boolean;
  isArchived: boolean;
  idVerificationData: IdVerificationData | null;
  walletBalance: number;
  collaborations: string[];
  wallet: Wallet;
  wallets: IWalletItem[];
  passwordReset: any | null;
  restrictions: any | null;
  bankAccountVerificationType?: string;
  bankAccountVerificationStatus?: string;
  isEmailVerified: boolean;
  isAccountSet: boolean;
  genres: string[];
  category?: CREATOR_CATEGORY;
  isFavourite?: boolean;
  emailNotification?: boolean;
  pushNotification?: boolean;
  allowNearbyDiscovery?: boolean;
}

export type SocialsTypes = {
  x?: string;
  tiktok?: string;
  facebook?: string;
  instagram?: string;
  snapchat?: string;
  spotify?: string;
  youtube?: string;
};

export interface LocationObject {
  city: string;
  state: string;
  street: string;
  address: string;
  country: string;
  landmark?: string;
  coordinates: Point;
}

export interface Point {
  lat: number;
  lng: number;
  latitude: number;
  longitude: number;
}

export interface IdVerificationData {
  bvn: IBvn | null;
  bank: IBank | null;
}

interface IBvn {
  lastName: string;
  firstName: string;
  middleName: string;
  dateOfBirth: string; // ISO 8601 date format
  phoneNumber: string;
}

interface IBank {
  fullName: string;
  lastName: string;
  firstName: string;
  middleName: string;
  accountNumber: string;
  bankName?: string;
}

export interface Wallet {
  balance: number;
  country: string;
  countryCode: string;
  currency: string;
  id: string;
  createdAt: Date;
  updatedAt: Date;
  userId: string;
  meta: any;
}

interface IWalletItem {
  balance: number;
  country: string;
  countryCode: string;
  currency: string;
  id: string;
  userId: string;
  createdAt: Date;
  updatedAt: Date;
  meta: any;
}

export interface LoginResponse {
  user: UserObjectData;
  token: string;
}

export interface UserWithSession
  extends Omit<User, keyof UserObjectData>,
    UserObjectData {
  accessToken: string;
}

export interface ExtendedSession extends Session {
  user: UserWithSession;
  accessToken: string;
}

export interface ExtendedJWT extends JWT {
  accessToken: string;
  user: UserWithSession;
}

export interface AuthState extends LoginResponse {
  loading?: boolean;
  search: SearchResultInterface;
  selectedUser: {
    profile: UserObjectData | null;
    events: EventItem[];
  };
  favourites: {
    djs: {
      users: UserObjectData[];
      loading: boolean;
    };
  };
  isPinValid: boolean;
}

export interface DecodedTokenType extends Partial<LoginResponse> {
  userId: string;
  id: string;
  exp: number;
  iat: number;
}
