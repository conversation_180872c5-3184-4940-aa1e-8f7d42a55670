import { AxiosResponse } from 'axios';

import {
  HTTPS_BASE,
  HTTPS_FILE,
  CHANGE_PASSWORD_URL,
  CREATE_WALLET_TRANSACTION_PIN,
  CREATE_WALLET_URL,
  CURRENT_USER_URL,
  DELETE_USER_ACCOUNT,
  EDIT_PERSONAL_INFO,
  EDIT_SOCIAL_INFO,
  EDIT_USER_PROFILE_URL,
  GET_USER_URL,
  NEWSLETTER_URL,
  PROFILE_IMAGE_UPLOAD_URL,
  REQUEST_OTP_URL,
  SEARCH_USERS_URL,
  UPDATE_PASSWORD,
  UPDATE_USER_EMAIL,
  UPDATE_WALLET_TRANSACTION_PIN,
  USER_PASSWORD_RESET_URL,
  VALIDATE_OTP_URL,
  VERIFY_EMAIL_URL,
  VERIFY_PASSWORD_URL,
  VERIFY_SIGNED_USERNAME_URL,
  VERIFY_WALLET_TRANSACTION_PIN,
  IDeleteUserAccount,
  IEmailUpdate,
  IWalletTransactionPin,
  IUpdatePasswordDto,
  LoginResponse,
  OtpPayload,
  SearchResultResponse,
  UserObjectData,
  IVerifyWalletTransactionPin,
  ICreateWalletTransactionPin,
  AccountCreationPayload,
} from '@/api';

export const apiRequestOtp = async (payload: Omit<OtpPayload, 'otp'>) =>
  HTTPS_BASE.post<AxiosResponse<LoginResponse, any>>(REQUEST_OTP_URL, payload);

export const apiValidateOtp = async (payload: OtpPayload) =>
  HTTPS_BASE.post<AxiosResponse<LoginResponse, any>>(VALIDATE_OTP_URL, payload);

export const apiGetLoggedInUser = async () =>
  HTTPS_BASE.get<AxiosResponse<UserObjectData>>(CURRENT_USER_URL);

export const apiResetPassword = async (payload: {
  email: string | undefined;
}) =>
  HTTPS_BASE.get<AxiosResponse<any, any>>(
    `${USER_PASSWORD_RESET_URL}?email=${payload.email}`
  );

export const apiUpdateUserProfile = async (
  id: string,
  token: string,
  payload: Partial<UserObjectData>
) =>
  HTTPS_BASE.patch<AxiosResponse<UserObjectData, any>>(
    EDIT_USER_PROFILE_URL(id),
    payload,
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    }
  );

export const apiSearchUsers = async (queryString: string) => {
  return HTTPS_BASE.get<AxiosResponse<SearchResultResponse>>(
    SEARCH_USERS_URL(queryString)
  );
};

export const apiUpdateUserPersonalInfo = async (
  id: string,
  payload: Partial<UserObjectData>
) =>
  HTTPS_BASE.patch<AxiosResponse<UserObjectData, any>>(
    EDIT_PERSONAL_INFO(id),
    payload
  );

export const apiUploadProfileImage = async ({
  userId,
  payload,
  token,
}: {
  userId: string;
  payload: FormData;
  token: string;
}) =>
  HTTPS_FILE.patch<AxiosResponse<UserObjectData>>(
    PROFILE_IMAGE_UPLOAD_URL(userId),
    payload,
    {
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'multipart/form-data',
      },
    }
  );

export const apiGetUser = async (userId: string) =>
  HTTPS_BASE.get<AxiosResponse<UserObjectData>>(GET_USER_URL(userId));

export const apiUpdateUserSocialInfo = async (
  id: string,
  payload: Partial<UserObjectData>
) =>
  HTTPS_BASE.patch<AxiosResponse<UserObjectData, any>>(
    EDIT_SOCIAL_INFO(id),
    payload
  );

export const apiChangeUserPassword = async (
  payload: IUpdatePasswordDto,
  config = {}
): Promise<AxiosResponse<any>> => {
  return HTTPS_BASE.post(UPDATE_PASSWORD, payload, config);
};

export const apiChangeUserEmail = async (
  payload: IEmailUpdate,
  config = {}
): Promise<AxiosResponse<any>> => {
  return HTTPS_BASE.post(UPDATE_USER_EMAIL, payload);
};

export const apiUpdateUserWalletPin = async (
  payload: IWalletTransactionPin,
  config = {}
): Promise<AxiosResponse<any>> => {
  return HTTPS_BASE.put(UPDATE_WALLET_TRANSACTION_PIN, payload);
};

export const apiVerifyWalletPin = async (
  payload: IVerifyWalletTransactionPin,
  config = {}
): Promise<AxiosResponse<any>> => {
  return HTTPS_BASE.post(VERIFY_WALLET_TRANSACTION_PIN, payload);
};

export const apiCreateTransactionPin = async (
  payload: ICreateWalletTransactionPin,
  config = {}
): Promise<AxiosResponse<any>> => {
  return HTTPS_BASE.put(CREATE_WALLET_TRANSACTION_PIN, payload);
};

export const apiDeleteUserAccount = async (
  payload: IDeleteUserAccount,
  config = {}
): Promise<AxiosResponse<any>> => {
  return HTTPS_BASE.post(DELETE_USER_ACCOUNT, payload);
};

export const apiGetUserById = async (userId: string, token?: string) =>
  HTTPS_BASE.get<AxiosResponse<UserObjectData>>(GET_USER_URL(userId), {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });

export const apiChangePassword = async (payload: {
  userId: string;
  token: string;
  password: string;
}): Promise<AxiosResponse<any>> => {
  return HTTPS_BASE.post(CHANGE_PASSWORD_URL, payload);
};

export const apiVerifySignedUsername = async (
  username: string,
  token: string
): Promise<AxiosResponse<any>> => {
  return HTTPS_BASE.get(VERIFY_SIGNED_USERNAME_URL(username), {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
};

export const apiVerifyPassword = async (password: string, token: string) => {
  return HTTPS_BASE.post(
    VERIFY_PASSWORD_URL,
    { password }, // send password as part of the request body
    {
      headers: {
        Authorization: `Bearer ${token}`, // add the token to the Authorization header
      },
    }
  );
};

export const apiCreateWallet = async (payload: {
  country: string;
  token: string;
}) =>
  HTTPS_BASE.post<AxiosResponse<undefined, any>>(
    CREATE_WALLET_URL,
    {
      country: payload.country,
    },
    {
      headers: {
        Authorization: `Bearer ${payload.token}`, // add the token to the Authorization header
      },
    }
  );

export const apiVerifyEmail = async (email: string) =>
  HTTPS_BASE.get(`${VERIFY_EMAIL_URL}/${email}`);

export const apiNewsletter = async (payload: {
  email: string;
}): Promise<AxiosResponse<any>> => {
  return HTTPS_BASE.post(NEWSLETTER_URL, payload);
};
