import type { AxiosError } from 'axios';
import axios from 'axios';
import { createMutation } from 'react-query-kit';

import { HTTPS_BASE } from '../common';
import type { ErrorResponse } from '../common/types';
import { ACCOUNT_CREATE_URL } from './constants';
import type {
  AccountCreationResponse,
  CreatorAccountCreationPayload,
} from './types';

export const useCreateCreatorAccount = createMutation<
  AccountCreationResponse,
  CreatorAccountCreationPayload,
  Error
>({
  mutationFn: async (data) =>
    HTTPS_BASE({
      url: ACCOUNT_CREATE_URL,
      method: 'POST',
      data,
    })
      .then((response) => response.data.data)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      }),
});
