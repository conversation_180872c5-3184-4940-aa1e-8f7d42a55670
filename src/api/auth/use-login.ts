import type { AxiosError, AxiosResponse } from 'axios';
import axios from 'axios';
import { createMutation } from 'react-query-kit';

import { HTTPS_BASE } from '../common';
import type { ErrorResponse } from '../common/types';
import { LOGIN_URL } from './constants';
import type { LoginPayload, LoginResponse } from './types';

export const useLogin = createMutation<LoginResponse, LoginPayload, Error>({
  mutationFn: async (data) =>
    HTTPS_BASE({
      url: LOGIN_URL,
      method: 'POST',
      data,
    })
      .then((response) => response.data.data)
      .catch((error: AxiosError) => {
        let message = 'Something went wrong';

        if (axios.isAxiosError<ErrorResponse>(error)) {
          message = error.response?.data?.message ?? message;
        } else if (error instanceof Error) {
          message = error.message;
        }

        throw new Error(message);
      }),
});

export const apiLoginUser = async (payload: LoginPayload) =>
  HTTPS_BASE.post<AxiosResponse<LoginResponse, any>>(LOGIN_URL, payload);
