import { useState, Fragment, useRef, useEffect } from 'react';
import { Input } from '@/components/ui/ninput';

type OtpInputProps = {
  length: number;
  otp: string;
  onOtpChange: (otp: string) => void;
  mask?: boolean;
  autoFocus?: boolean;
};

const Otp = ({
  length,
  otp,
  onOtpChange,
  mask = false,
  autoFocus = false,
}: OtpInputProps): JSX.Element => {
  const [tempOtp, setTempOtp] = useState<string[]>(new Array(length).fill(''));
  const inputRefs = useRef<HTMLInputElement[]>([]);

  useEffect(() => {
    if (otp === '') {
      setTempOtp(new Array(length).fill(''));
    }
  }, [otp, length]);

  const handleOnchange = (
    { target }: React.ChangeEvent<HTMLInputElement>,
    index: number
  ): void => {
    const { value } = target;
    const newOtp = [...tempOtp];

    // Only update if the input is a single character digit
    if (/^\d$/.test(value)) {
      newOtp[index] = value;
      setTempOtp(newOtp);
      onOtpChange(newOtp.join(''));

      // Move to the next input if there is one
      if (index < length - 1) {
        inputRefs.current[index + 1]?.focus();
      }
    }
  };

  const handleOnKeyDown = (
    { key }: React.KeyboardEvent<HTMLInputElement>,
    index: number
  ) => {
    if (key === 'Backspace') {
      const newOtp = [...tempOtp];
      newOtp[index] = '';
      setTempOtp(newOtp);
      onOtpChange(newOtp.join(''));

      if (index > 0) {
        inputRefs.current[index - 1]?.focus();
      }
    }
  };

  useEffect(() => {
    if (autoFocus && inputRefs.current[0]) {
      inputRefs.current[0].focus();
    }
  }, [autoFocus]);

  return (
    <div className="flex items-center space-x-2 w-fit">
      {tempOtp.map((value, index) => (
        <Fragment key={index}>
          <Input
            ref={(el) => {
              inputRefs.current[index] = el!;
            }}
            onChange={(e) => handleOnchange(e, index)}
            onKeyDown={(e) => handleOnKeyDown(e, index)}
            className="w-12 h-14 text-center border rounded-lg text-lg border-gray-300 placeholder:text-slate-300 dark:placeholder:text-slate-500"
            type={mask ? 'password' : 'text'}
            placeholder="0"
            value={value}
          />
        </Fragment>
      ))}
    </div>
  );
};

export default Otp;
