import React, { useEffect, useState } from 'react';
import GooglePlacesAutocomplete, { geocodeByAddress, geocodeByPlaceId, getLatLng } from 'react-google-places-autocomplete';
import { env } from '@/env.mjs';

interface PlaceDetails {
  address: string;
  street: string;
  city: string;
  state: string;
  country: string;
  landmark: string;
  coordinates: { lat: number; lng: number };
}

interface IProps {
  onChange: (value: any) => void;
  value: string | undefined;
}

const PlaceSearch = ({value, onChange}:IProps) => {
  const [selectedValue, setSelectedValue] = useState<any>(null);


  const handleSelect = async (value: any) => {
    setSelectedValue(value);
    geocodeByAddress(value?.label)
    .then((results) => getLatLng(results[0]!))
    .then(async ({ lat, lng }) => {
      try {
        const response = await fetch(
          `https://maps.googleapis.com/maps/api/geocode/json?latlng=${lat},${lng}&key=${env.NEXT_PUBLIC_GOOGLE_MAP_API_KEY}`
        );
        const data = await response.json();
      
        data.results[0] && onChange( {
          result: data.results[0],
          landmark: value.value.structured_formatting.main_text
        });
      } catch (error) {
        console.error('Error fetching address:', error);
      }
    })
        // const address = result.formatted_address || '';
        // const coordinates = {
        //   lat: result.geometry?.location.lat() || 0,
        //   lng: result.geometry?.location.lng() || 0,
        // };

        // let street = '';
        // let city = '';
        // let state = '';
        // let country = '';
        // let landmark = '';

        // result.address_components.forEach((component) => {
        //   const types = component.types;
        //   if (types.includes('route')) {
        //     street = component.long_name;
        //   }
        //   if (types.includes('locality')) {
        //     city = component.long_name;
        //   }
        //   if (types.includes('administrative_area_level_1')) {
        //     state = component.long_name;
        //   }
        //   if (types.includes('country')) {
        //     country = component.long_name;
        //   }
        // });

        // setSelectedPlaceDetails({ address, street, city, state, country, landmark, coordinates });
  };

  useEffect(() => {
    if (value) {
      setSelectedValue({ label: value, value: { description: value } });
    }
  }, [value]);

  return (
    <div className="flex md:w-[400px]">
      <div className="w-full">
        <GooglePlacesAutocomplete
          apiKey={env.NEXT_PUBLIC_GOOGLE_MAP_API_KEY}
          selectProps={{
            value: selectedValue,
            onChange: handleSelect,
            placeholder: 'Search for a place',
            className: 'w-full border border-gray-300 rounded',
            isClearable: true,
          }}
        />
      </div>
    </div>
  );
};

export default PlaceSearch;
