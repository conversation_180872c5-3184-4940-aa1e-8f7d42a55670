import { Icons } from '@/components/icons/icons';
import React from 'react';
import { MdLocationPin } from 'react-icons/md';

export default function UniqueSearch() {
  return (
    <div className='hidden lg:flex items-center border-b border rounded-[80px] w-[500px] py-2 relative'>
      <div className='flex-1 flex justify-center items-center h-full'>
        <input
          placeholder='Search for events'
          className='outline-hidden text-xs w-full px-3'
        />
      </div>
      <div className='border-l h-4 border-gray-400 mx-3'></div>
      <div className='flex-1 flex items-center justify-between h-full px-2'>
        <div className='flex items-center'>
          <MdLocationPin size={20} className='text-[#664FB0] mr-2' />
          <input
            placeholder='Everywhere'
            className='outline-hidden text-xs w-full'
          />
        </div>
        <div className='absolute right-1 top-1/2 transform -translate-y-1/2 bg-[#D0C9EA] rounded-[80px] py-1 px-1'>
          <Icons.search />
        </div>
      </div>
    </div>
  );
}
