import { RiErrorWarningLine } from 'react-icons/ri';
import classNames from 'classnames';

interface InputProp {
  label?: string;
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => any;
  warning?: string;
  error?: string;
  success?: string;
  prefix?: string;
  disabled?: boolean;
}

export const InputWithPrefix = ({
  label = '',
  placeholder = '',
  value,
  onChange = () => null,
  warning = '',
  error = '',
  success = '',
  prefix = 'https://', // Default prefix is https://
  disabled = false,
}: InputProp) => {
  // Ensure URL starts with http:// or https:// when the user enters a value
  const handleChange = (inputValue: string) => {
    let sanitizedValue = inputValue.trim();

    // If the value doesn't start with http or https, add the prefix
    if (!/^https?:\/\//i.test(sanitizedValue) && sanitizedValue.length > 0) {
      sanitizedValue = `${prefix}${sanitizedValue}`;
    }

    // Call the provided onChange function with the updated value
    onChange(sanitizedValue);
  };

  return (
    <div className='flex flex-col'>
      {/* Label */}
      {label.length > 0 && (
        <div className='text-sm font-normal text-[#18181B] pb-1.5 text[14px]'>
          {label}
        </div>
      )}

      {/* Input Wrapper */}
      <div
        className={classNames(
          'flex flex-row items-center bg-white border border-gray-300 shadow-xs rounded-lg',
          error.length > 0 ? 'border-red-600' : '',
          disabled ? 'bg-gray-100' : ''
        )}
      >
        {/* Prefix */}
        <div
          className={classNames(
            'px-3 py-2 text-[#71717A] font-normal text-base flex',
            disabled ? 'bg-gray-100' : ''
          )}
        >
          {prefix}
        </div>

        {/* Inner Input Wrapper */}
        <div
          className={classNames(
            'flex flex-row items-center gap-2 px-4 py-2 border-l border-gray-300 w-full',
            error.length > 0 ? 'border-red-600' : '',
            disabled ? 'bg-gray-100' : ''
          )}
        >
          <input
            className='font-normal text-base text-gray-900 w-full outline-hidden border-none'
            placeholder={placeholder}
            value={value}
            onChange={(event) => handleChange(event.target.value)}
            disabled={disabled}
          />

          {/* Error Icon */}
          {error.length > 0 && (
            <RiErrorWarningLine className='text-red-600' size={20} />
          )}
        </div>
      </div>

      {/* Error, Warning, and Success Messages */}
      {error.length > 0 && (
        <div className='text-sm font-normal text-red-600 pt-1.5'>{error}</div>
      )}

      {warning.length > 0 && (
        <div className='text-sm font-normal text-yellow-500 pt-1.5'>
          {warning}
        </div>
      )}

      {success.length > 0 && (
        <div className='text-sm font-normal text-green-700 pt-1.5'>
          {success}
        </div>
      )}
    </div>
  );
};
