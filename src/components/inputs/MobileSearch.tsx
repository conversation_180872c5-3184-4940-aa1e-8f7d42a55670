import { apiGetAllEvent } from '@/api/events/requests';
import { ISingleEvent } from '@/types';
import { useRouter } from 'next/navigation';
import { useState, useRef } from 'react';
import { CiSearch } from 'react-icons/ci';

interface MobileSearchProps {
  apiKey: string;
  value: string;
  onSelect?: (city: string, country: string) => void;
}

const MobileSearch: React.FC<MobileSearchProps> = ({
  apiKey,
  value,
  onSelect,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [query, setQuery] = useState('');
  const [events, setEvents] = useState<ISingleEvent[]>([]);
  const router = useRouter();

  const handleIconClick = () => {
    setIsOpen(!isOpen);
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  const fetchEvents = async (searchTerm: string) => {
    if (!searchTerm || searchTerm.length < 3) {
      setEvents([]);
      return;
    }

    setLoading(true);
    try {
      const response = await apiGetAllEvent({ title: searchTerm });
      const {
        data: {
          data: { events },
        },
      } = response;
      //
      setEvents(events);
    } catch (error) {
      console.error('Error fetching events:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const searchTerm = e.target.value;
    setQuery(searchTerm);
    fetchEvents(searchTerm);
  };

  return (
    <div className='relative inline-block'>
      <CiSearch
        onClick={handleIconClick}
        className='cursor-pointer text-[#664FB0]'
        size={24}
      />
      {isOpen && (
        <div className='absolute z-50 right-0 mt-2 w-64 bg-white border text-[#1B1B1B] rounded-lg shadow-lg overflow-hidden'>
          <input
            type='text'
            value={query}
            onChange={handleInputChange}
            ref={inputRef}
            className='w-full p-2 border-b focus:outline-[#6600CC] rounded-lg'
            placeholder='Search for event...'
          />
          <ul className='max-h-60 overflow-auto'>
            {loading && <li className='p-2 text-[14px]'>Loading...</li>}
            {events.map((event, i) => {
              return (
                <li
                  key={i}
                  className='p-2 text-[16px] cursor-pointer hover:bg-gray-100'
                  onClick={() => {
                    setIsOpen(false);
                    router.push(`/event/${event.slug}`);
                  }}
                >
                  <p className='font-semibold text-lg'>{event?.title}</p>
                  <p className='text-gray-500'>
                    {event?.location.landmark}, {event.location.city}
                  </p>
                </li>
              );
            })}
          </ul>
        </div>
      )}
    </div>
  );
};

export default MobileSearch;
