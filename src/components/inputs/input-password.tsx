import * as React from 'react';
import { Input } from '@/components/ui/ninput';
import { EyeIcon, EyeOffIcon } from 'lucide-react';

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  suffix?: React.ReactNode;
  prefixIcon?: React.ReactNode;
}

const PasswordInput = React.forwardRef<HTMLInputElement, InputProps>(
  ({ suffix, prefixIcon, className, type, ...props }, ref) => {
    const [showPassword, setShowPassword] = React.useState(false);
    return (
      <Input
        type={showPassword ? 'text' : 'password'}
        suffix={
          showPassword ? (
            <EyeIcon
              size={'15'}
              color="#71717A"
              onClick={() => setShowPassword(false)}
            />
          ) : (
            <EyeOffIcon
              size={'15'}
              color="#71717A"
              onClick={() => setShowPassword(true)}
            />
          )
        }
        prefixIcon={prefixIcon}
        className={className}
        {...props}
        ref={ref}
      />
    );
  }
);

PasswordInput.displayName = 'PasswordInput';

export { PasswordInput };
