import React from "react";

interface CurrencyInputProps {
  value: number;
  onChange: (value: number) => void;
}

export const CurrencyInput: React.FC<CurrencyInputProps> = ({ value, onChange }) => {
  // Function to format the value as currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "NGN",
      minimumFractionDigits: 2,
    }).format(value);
  };

  // Handler to parse the input to a number and call the onChange prop
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const rawValue = e.target.value.replace(/[^0-9.]/g, ""); // Remove non-numeric characters
    const numberValue = parseFloat(rawValue);
    if (!isNaN(numberValue)) {
      onChange(numberValue);
    } else {
      onChange(0);
    }
  };

  return (
    <input
      type="text"
      value={formatCurrency(value)}
      onChange={handleInputChange}
      placeholder="0.00"
      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-xs focus:outline-hidden focus:ring-blue-500 focus:border-blue-500"
    />
  );
};
