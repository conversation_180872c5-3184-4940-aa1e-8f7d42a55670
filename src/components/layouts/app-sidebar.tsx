'use client';
import * as React from 'react';

import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
} from '@/components/ui/sidebar';
import Link from 'next/link';
import <PERSON><PERSON><PERSON>ogo from '~/svg/PoplaSplash.svg';
import { SmBoldLabel } from '@/components/ui/typography';
import UnstyledLink from '@/components/links/UnstyledLink';

import { UserFollowingItem } from '@/components/following/item';
import { usePathname } from 'next/navigation';
import { FOLLOWING_USERS, NAV_SECTIONS } from '@/lib/constants/generic';

const isPathActive = (itemHref: string, currentPath: string): boolean => {
  if (itemHref === '/home' && currentPath === '/') return true;
  if (itemHref === '/home') return currentPath === '/home';
  return currentPath.startsWith(itemHref);
};

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const pathname = usePathname();

  return (
    <Sidebar {...props}>
      <SidebarHeader>
        <Link
          href='/'
          className='h-full max-h-12 w-full max-w-28 text-accent-moderate dark:text-white'
        >
          <PoplaLogo />
        </Link>
      </SidebarHeader>
      <SidebarContent>
        {NAV_SECTIONS.map((section, idx) => (
          <SidebarGroup key={`section-${idx}`}>
            {section.title && (
              <SidebarGroupLabel asChild>
                <SmBoldLabel themed weight='bold'>
                  {section.title}
                </SmBoldLabel>
              </SidebarGroupLabel>
            )}
            <SidebarGroupContent>
              <SidebarMenu>
                {section.items.map(({ title, href, Icon }) => {
                  const isActive = isPathActive(href, pathname);
                  return (
                    <SidebarMenuItem key={title}>
                      <SidebarMenuButton asChild isActive={isActive}>
                        <UnstyledLink href={href}>
                          <Icon />
                          {title}
                        </UnstyledLink>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  );
                })}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        ))}

        {FOLLOWING_USERS.length > 0 && (
          <SidebarGroup>
            <SidebarGroupLabel asChild>
              <SmBoldLabel themed weight='bold'>
                Following
              </SmBoldLabel>
            </SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu className='gap-4'>
                {FOLLOWING_USERS.map((user) => (
                  <UserFollowingItem
                    key={user.username}
                    avatar={user.avatar}
                    displayName={user.displayName}
                    username={user.username}
                  />
                ))}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        )}
      </SidebarContent>
      <SidebarRail />
    </Sidebar>
  );
}
