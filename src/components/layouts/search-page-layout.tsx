import React from 'react';
import { useRouter } from 'next/navigation';
import { Back } from '../ui/back';
import { SearchInput } from '../ui/search-input';

interface SearchPageLayoutProps {
  placeholder?: string;
  children: React.ReactNode;
  footer?: React.ReactNode;
  searchValue?: string;
  onSearchChange?: (text: string) => void;
}

export const SearchPageLayout = ({
  placeholder,
  children,
  footer,
  searchValue = '',
  onSearchChange = () => {},
}: SearchPageLayoutProps) => {
  const router = useRouter();

  return (
    <div className="flex flex-col min-h-screen bg-white dark:bg-neutral-900">
      {/* Header */}
      <div className="flex items-center gap-2 px-4 py-4 border-b border-neutral-200 dark:border-neutral-800">
        <button
          onClick={() => router.back()}
          className="p-2 rounded-md hover:bg-neutral-100 dark:hover:bg-neutral-800"
        >
          <Back />
        </button>
        <div className="flex-1">
          <SearchInput
            value={searchValue}
            onChangeText={(text) => onSearchChange(text)}
            placeholder={placeholder}
            showClearButton={!!searchValue}
          />
        </div>
      </div>

      {/* Main content */}
      <div className="flex-1 p-4 space-y-4 overflow-y-auto">{children}</div>

      {/* Footer */}
      {footer && (
        <div className="border-t border-neutral-200 dark:border-neutral-800 p-4 mt-auto">
          {footer}
        </div>
      )}
    </div>
  );
};