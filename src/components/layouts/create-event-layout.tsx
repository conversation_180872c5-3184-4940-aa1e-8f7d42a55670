'use client';

import React from 'react';

type CreateEventLayoutProps = {
  title?: string;
  subTitle?: string;
  children: React.ReactNode;
  footer?: React.ReactNode;
};

export const CreateEventLayout: React.FC<CreateEventLayoutProps> = ({
  title,
  subTitle,
  children,
  footer,
}) => {
  return (
    <div className="flex flex-col w-full md:w-[500px] px-4">
      <main className=" mt-4 gap-1">
        <div className="text-sm text-muted-foreground mb-6">
          <span className="font-medium text-white">Events</span>
          <span className="mx-1">/</span>
          <span className="text-muted-foreground">Create</span>
        </div>
        {title && (
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            {title}
          </h1>
        )}
        {subTitle && (
          <p className="text-gray-600 dark:text-gray-400">{subTitle}</p>
        )}
      </main>

      <section className="flex flex-col gap-4 py-4">{children}</section>

      {footer && (
        <footer className="pb-4 flex justify-end">
          <div className="w-full flex flex-row gap-x-4">{footer}</div>
        </footer>
      )}
    </div>
  );
};
