'use client';
import React, { useCallback, useEffect, useState } from 'react';
import { Icons } from '@/components/icons/icons';
import Link from 'next/link';
import { Button } from '@/components/ui';
import toast from 'react-hot-toast';
import { apiNewsletter, apiContactUs } from '@/api';
import useVisibility from '@/lib/hooks/useVisibility';

export default function Footer() {
  const [isEventsOpen, setIsEventsOpen] = useState<boolean>(false);
  const [isPoplaOpen, setIsPoplaOpen] = useState<boolean>(false);
  const [isAppOpen, setIsAppOpen] = useState<boolean>(false);
  const [isSupportOpen, setIsSupportOpen] = useState<boolean>(false);
  const [email, setEmail] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);

  const [isMobile, setIsMobile] = useState(false);
  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    message: '',
  });
  const {
    isOpen: isModalOpen,
    onClose: handleCloseModal,
    onOpen: handleOpenModal,
  } = useVisibility();
  const [contactFormLoading, setContactFormLoading] = useState(false);

  const modalRef = React.useRef<HTMLDivElement | null>(null);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({ ...prevData, [name]: value }));
  };

  const handleSubmit = async () => {
    setContactFormLoading(true);
    const response = await apiContactUs(formData);
    if (response.status === 200 || 201) {
      setContactFormLoading(false);
      toast.success('Submitted information successfully');
      handleCloseModal();
      setFormData({ fullName: '', email: '', message: '' });
    } else {
      toast.error('Failed to send message at this time');
    }
  };

  // Set isMobile based on window width
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768); // md breakpoint is 768px
    };

    handleResize(); // Set initial value
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const sectionClass = (isOpen: boolean) =>
    `transition-all duration-300 overflow-hidden ${
      isOpen ? 'max-h-screen' : 'max-h-0'
    }`;

  const policy = [
    {
      text: 'Terms and Conditions',
      link: '/policy#terms_and_conditions',
    },
    {
      text: 'Privacy Policy',
      link: '/policy#privacy_policy',
    },
    {
      text: 'Cookies Policy',
      link: '/policy#cookie_policy',
    },
    {
      text: 'Cancellation Policy ',
      link: '/policy#cancellation_policy',
    },
  ];

  const handelSubmit = useCallback(
    async (e: React.FormEvent) => {
      e.preventDefault();
      setLoading(true);
      try {
        const response = await apiNewsletter({
          email,
        });
        if (response.data.message) {
          toast.success(response.data.message);
          setEmail('');
        }
      } catch (error: any) {
        if (error?.response?.data?.message) {
          toast.error(error?.response?.data?.message);
        }
      } finally {
        setLoading(false);
      }
    },
    [email]
  );

  return (
    <>
      <main className='flex justify-center md:pt-[100px]'>
        <div className='max-w-[1440px] flex flex-1 px-[16px] md:px-[80px] py-[20px] md:py-[22px]'>
          <div className='flex flex-1 flex-col md:flex-row md:justify-between gap-1 md:gap-10 text-[15px] flex-wrap '>
            <div className='flex flex-1 flex-col md:gap-4'>
              <div
                onClick={() => setIsEventsOpen(!isEventsOpen)}
                className=' cursor-pointer md:cursor-default flex justify-between items-center'
              >
                <p className='py-[12px] md:py-[0px] font-bold'>Events</p>
                {isEventsOpen ? (
                  <Icons.menuOpen className=' md:hidden' />
                ) : (
                  <Icons.menuClose className=' md:hidden' />
                )}
              </div>
              <ul
                className={`${
                  isMobile
                    ? sectionClass(isEventsOpen)
                    : 'max-h-screen flex flex-1 flex-col md:gap-4'
                } pl-2 md:pl-0`}
              >
                <li className='py-[12px] md:py-[0px] text-[16px] text-[#3F3F46]'>
                  <Link href={'/event'}>All Events</Link>
                </li>
                <li className='py-[12px] md:py-[0px] text-[16px] text-[#3F3F46]'>
                  <Link href={'/create-event'}>Create Event</Link>
                </li>
                <li className='py-[12px] md:py-[0px] text-[16px] text-[#3F3F46]'>
                  <Link href={'/my-events'}>My Events</Link>
                </li>
              </ul>
            </div>
            <div className='flex flex-1 flex-col md:gap-4'>
              <div
                onClick={() => setIsPoplaOpen(!isPoplaOpen)}
                className=' cursor-pointer md:cursor-default flex justify-between items-center'
              >
                <p className='py-[12px] md:py-[0px] font-bold'>Popla</p>
                {isPoplaOpen ? (
                  <Icons.menuOpen className=' md:hidden' />
                ) : (
                  <Icons.menuClose className=' md:hidden' />
                )}
              </div>
              <ul
                className={`${
                  isMobile
                    ? sectionClass(isPoplaOpen)
                    : 'max-h-screen flex flex-1 flex-col md:gap-4'
                } pl-2 md:pl-0`}
              >
                <li className='py-[12px] md:py-[0px] text-[16px] text-[#3F3F46]'>
                  Pricing
                </li>
                <li className='py-[12px] md:py-[0px] text-[16px] text-[#3F3F46]'>
                  Sustainability
                </li>
              </ul>
            </div>
            <div className='flex flex-1 flex-col md:gap-4'>
              <div
                onClick={() => setIsAppOpen(!isAppOpen)}
                className=' cursor-pointer md:cursor-default flex justify-between items-center'
              >
                <p className='py-[12px] md:py-[0px] font-bold cursor-pointer'>
                  Get the App
                </p>
                {isAppOpen ? (
                  <Icons.menuOpen className=' md:hidden' />
                ) : (
                  <Icons.menuClose className=' md:hidden' />
                )}
              </div>
              <ul
                className={`${
                  isMobile
                    ? sectionClass(isAppOpen)
                    : 'max-h-screen flex flex-1 flex-col md:gap-4'
                } pl-2 md:pl-0`}
              >
                <li className='py-[12px] md:py-[0px] text-[16px] text-[#3F3F46] cursor-pointer'>
                  <Link
                    href={'https://apps.apple.com/ng/app/popla/id6474488670'}
                  >
                    IOS
                  </Link>
                </li>
                <li className='py-[12px] md:py-[0px] text-[16px] text-[#3F3F46] cursor-pointer'>
                  <Link
                    href={
                      'https://play.google.com/store/apps/details?id=com.popla.app&pli=1'
                    }
                  >
                    Android
                  </Link>
                </li>
              </ul>
            </div>
            <div className='flex flex-1 flex-col md:gap-4'>
              <div
                onClick={() => setIsSupportOpen(!isSupportOpen)}
                className=' cursor-pointer md:cursor-default flex justify-between items-center'
              >
                <p className='py-[12px] md:py-[0px] font-bold'>Support</p>
                {isSupportOpen ? (
                  <Icons.menuOpen className=' md:hidden' />
                ) : (
                  <Icons.menuClose className=' md:hidden' />
                )}
              </div>
              <ul
                className={`${
                  isMobile
                    ? sectionClass(isSupportOpen)
                    : 'max-h-screen flex flex-1 flex-col md:gap-4'
                } pl-2 md:pl-0`}
              >
                <li className='py-[12px] md:py-[0px] text-[16px] text-[#3F3F46] cursor-pointer'>
                  <Link href='/help'>Help Center</Link>
                </li>
                <li
                  onClick={handleOpenModal}
                  className='py-[12px] md:py-[0px] text-[16px] text-[#3F3F46] cursor-pointer'
                >
                  Get in touch
                </li>
              </ul>
            </div>
            <div className='flex flex-1 flex-col md:gap-4 '>
              <p className='py-[12px] md:py-[0px] font-bold'>
                Join our Newsletter List
              </p>
              <p className='text-[16px] md:w-[402px] text-[#A2A2A2]'>
                Stay updated with the latest news and Popla offers by signing up
                for our newsletter.
              </p>
              <form
                onSubmit={handelSubmit}
                className='flex flex-1 h-[60px] flex-row pt-[20px] md:pt-0'
              >
                <div className='flex flex-1 h-[60px] items-center pl-10 border-l border-y border-[#71717A] md:border-none md:shadow-lg rounded-l-full'>
                  {/* <p className='text-[20px] text-[#A2A2A2]'>Enter email address</p> */}
                  <input
                    type='email'
                    required
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className='flex flex-1 text-[20px] placeholder:text-[#A2A2A2] focus:outline-hidden'
                    placeholder='Enter email address'
                  />
                </div>
                <Button
                  type='submit'
                  loading={loading}
                  className='cursor-pointer flex bg-linear-to-r h-[60px] from-[#B098FF] to-[#6600CC] justify-center items-center p-[15px] rounded-l-none rounded-r-full'
                >
                  <Icons.newsLetter className='mr-2 w-5' />
                </Button>
              </form>
            </div>
          </div>
        </div>
      </main>
      <main className='flex justify-center border-t  border-t-[#6600CC]'>
        <div className='max-w-[1440px] flex flex-1 px-[16px] md:px-[58px] py-[20px] md:py-[10px]'>
          <div className='flex flex-1 flex-col md:flex-row md:justify-between md:items-center text-[15px] flex-wrap '>
            <div className='hidden md:flex md:py-[22px]'>
              <p className='w-[129px]'>
                © {new Date().getFullYear()} Popla Ltd
              </p>
            </div>
            <ul className='flex flex-col md:flex-row md:items-center md:gap-4 '>
              {policy.map((item, i) => (
                <li className='py-[12px] md:py-[0px] cursor-pointer' key={i}>
                  <Link
                    href={item.link}
                    className='flex items-center gap-x-3 leading-10'
                  >
                    {item.text}
                  </Link>
                </li>
              ))}
            </ul>
            <div className='cursor-pointer py-[12px] md:py-[22px] gap-1 flex items-center'>
              <p className=''>English (United Kingdom)</p>
              {/* <Icons.menuClose /> */}
            </div>
            <ul className='flex flex-row items-center gap-3 py-[12px] md:py-[22px]'>
              <li>
                <Link
                  href={'https://www.instagram.com/getpopla'}
                  target='_blank'
                  rel='noopener noreferrer'
                >
                  <Icons.instagram className='mr-2 w-5' />
                </Link>
              </li>
              <li>
                <Link
                  href={'https://www.tiktok.com/@getpopla'}
                  target='_blank'
                  rel='noopener noreferrer'
                >
                  <Icons.tiktok className='mr-2 w-5' />
                </Link>
              </li>
              <li>
                <Link
                  href={'https://www.linkedin.com/company/popla-ltd'}
                  target='_blank'
                  rel='noopener noreferrer'
                >
                  <Icons.linkedIn className='mr-2 w-5' />
                </Link>
              </li>
              <li>
                <Link
                  href={'https://x.com/getpopla'}
                  target='_blank'
                  rel='noopener noreferrer'
                >
                  <Icons.twitter className='mr-2 w-5' />
                </Link>
              </li>
              <li>
                <Link
                  href={'https://www.youtube.com/@getpopla'}
                  target='_blank'
                  rel='noopener noreferrer'
                >
                  <Icons.youtube className='mr-2 w-5' />
                </Link>
              </li>
            </ul>
            <div className='md:hidden py-[12px]'>
              <p className='w-[129px]'>
                © {new Date().getFullYear()} Popla Ltd
              </p>
            </div>
          </div>
        </div>
        {isModalOpen && (
          <div ref={modalRef} className='p-8'>
            <h2 className='text-xl font-bold mb-4'>Contact Us</h2>
            <div className='space-y-4'>
              <div>
                <label
                  className='block text-sm font-medium mb-1'
                  htmlFor='fullName'
                >
                  Full Name
                </label>
                <input
                  type='text'
                  id='fullName'
                  name='fullName'
                  value={formData.fullName}
                  onChange={handleInputChange}
                  required
                  className='w-full px-4 py-2 border rounded-md'
                  placeholder='Enter your full name'
                />
              </div>
              <div>
                <label
                  className='block text-sm font-medium mb-1'
                  htmlFor='email'
                >
                  Email
                </label>
                <input
                  type='email'
                  id='email'
                  name='email'
                  value={formData.email}
                  onChange={handleInputChange}
                  required
                  className='w-full px-4 py-2 border rounded-md'
                  placeholder='Enter your email'
                />
              </div>
              <div>
                <label
                  className='block text-sm font-medium mb-1'
                  htmlFor='message'
                >
                  Message
                </label>
                <parea
                  id='message'
                  name='message'
                  value={formData.message}
                  onChange={handleInputChange}
                  required
                  className='w-full px-4 py-2 border rounded-md'
                  rows={4}
                  placeholder='Enter your message'
                />
              </div>
              <Button
                onClick={handleSubmit}
                loading={contactFormLoading}
                type='submit'
                className='w-full'
              >
                Submit
              </Button>
            </div>
          </div>
        )}
      </main>
    </>
  );
}
