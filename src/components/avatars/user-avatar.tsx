'use client';

import React from 'react';
import Image from 'next/image';
import clsx from 'clsx';

interface UserAvatarProps {
  avatar: string;
  username?: string;
  className?: string;
  textClassName?: string;
  hideStatusPill?: boolean;
}

export const UserAvatar: React.FC<UserAvatarProps> = ({
  username,
  avatar,
  className,
  textClassName,
  hideStatusPill,
}) => (
  <div className={clsx('flex flex-col items-center gap-1', className)}>
    <div className='relative flex size-16 items-center justify-center rounded-full'>
      <div className='absolute inset-0 rounded-full bg-linear-to-br from-[#664FB0] to-[#A992F5]' />

      <div className='absolute size-[60px] rounded-full bg-brand-10 dark:bg-brand-90' />

      <Image
        src={avatar}
        alt={username || 'User avatar'}
        width={56}
        height={56}
        className='rounded-full object-contain z-10'
      />

      {!hideStatusPill && (
        <div className='absolute -bottom-[5px] flex w-10 flex-row items-center justify-center gap-0.5 rounded-md bg-red-50 px-1 py-0.5'>
          <div className='size-1 rounded-full bg-white' />
          <span className='text-[10px] font-bold text-white leading-none'>
            LIVE
          </span>
        </div>
      )}
    </div>

    {username && (
      <span className={clsx('text-sm', textClassName)}>{username}</span>
    )}
  </div>
);
