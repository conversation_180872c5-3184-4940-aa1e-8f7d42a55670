'use client';

import React from 'react';
import ReactDOM from 'react-dom';
import clsx from 'clsx';

interface ConfirmationDialogProps {
  visible: boolean;
  title?: string;
  message: string;
  onConfirm: () => void;
  onCancel: () => void;
}

export const ConfirmationDialog: React.FC<ConfirmationDialogProps> = ({
  visible,
  title = 'Are you sure?',
  message,
  onConfirm,
  onCancel,
}) => {
  if (!visible) return null;

  return ReactDOM.createPortal(
    <div
      role="dialog"
      aria-modal="true"
      aria-labelledby="confirmation-dialog-title"
      aria-describedby="confirmation-dialog-description"
      className="fixed inset-0 z-50 flex items-center justify-center bg-black/50"
    >
      <div className="w-4/5 max-w-md rounded-lg bg-white p-6 shadow-lg dark:bg-gray-800">
        <div className="flex flex-col items-center justify-center gap-2">
          <h2
            id="confirmation-dialog-title"
            className="text-2xl font-semibold"
          >
            {title}
          </h2>
          <p
            id="confirmation-dialog-description"
            className="mb-4 text-center text-base font-bold text-gray-600 dark:text-gray-300"
          >
            {message}
          </p>
        </div>
        <div className="flex flex-col gap-3">
          <button
            onClick={onConfirm}
            className={clsx(
              'w-full rounded bg-blue-600 py-2 px-4 text-white hover:bg-blue-700',
              'focus:outline-hidden focus:ring-2 focus:ring-blue-500 focus:ring-offset-2'
            )}
          >
            Yes
          </button>
          <button
            onClick={onCancel}
            className={clsx(
              'w-full rounded border border-gray-400 py-2 px-4 text-gray-700 hover:bg-gray-100',
              'dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700',
              'focus:outline-hidden focus:ring-2 focus:ring-gray-500 focus:ring-offset-2'
            )}
          >
            Dismiss
          </button>
        </div>
      </div>
    </div>,
    document.body
  );
};