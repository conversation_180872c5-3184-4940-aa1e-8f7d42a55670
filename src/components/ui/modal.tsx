'use client';

import React, { useEffect } from 'react';
import { X } from 'lucide-react';

type ModalProps = {
  isOpen: boolean;
  title?: string;
  onClose: () => void;
  children: React.ReactNode;
  maxHeight?: string;
};

export const Modal: React.FC<ModalProps> = ({
  isOpen,
  title,
  onClose,
  children,
}) => {
  // Close modal on ESC key
  useEffect(() => {
    const onKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        onClose();
      }
    };
    window.addEventListener('keydown', onKeyDown);
    return () => window.removeEventListener('keydown', onKeyDown);
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <>
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black/40 z-40 animate-fadeIn"
        onClick={onClose}
      />

      {/* Modal */}
      <div
        className="fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 
                   bg-white dark:bg-bg-canvas-dark rounded-lg shadow-xl z-50
                   w-[90vw] max-w-lg max-h-[90vh] overflow-y-auto p-6
                   animate-scaleIn"
        role="dialog"
        aria-modal="true"
        aria-labelledby="modal-title"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Title */}
        {title && (
          <h2
            id="modal-title"
            className="text-xl font-semibold mb-4 text-gray-900 dark:text-gray-100"
          >
            {title}
          </h2>
        )}

        {/* Close Button */}
        <button
          aria-label="Close modal"
          className="absolute top-3 right-3 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          onClick={onClose}
        >
          <X size={24} />
        </button>

        {/* Content */}
        <div className="text-gray-700 dark:text-gray-300">{children}</div>
      </div>

      {/* Animations with Tailwind keyframes */}
      <style>
        {`
        @keyframes scaleIn {
          from {
            opacity: 0;
            transform: scale(0.95); /* only scale */
          }
          to {
            opacity: 1;
            transform: scale(1);
          }
        }
        .animate-scaleIn { animation: scaleIn 0.2s ease forwards; }
      `}
      </style>
    </>
  );
};
