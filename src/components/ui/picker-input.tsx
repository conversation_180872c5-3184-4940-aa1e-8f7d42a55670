'use client';

import React from 'react';

interface DateButtonProps {
  value?: Date | string | null;
  label: string;
  disabled?: boolean;
  onClick?: () => void;
}

export const InputPickerButton: React.FC<DateButtonProps> = ({
  value,
  label,
  disabled,
  onClick,
}) => {
  return (
    <button
      type="button"
      className="h-[52px] w-full flex items-center justify-between rounded-md border border-border-subtle-light p-3 dark:border-border-subtle-dark cursor-pointer"
      disabled={disabled}
      onClick={onClick}
    >
      <p
        className={
          value
            ? 'dark:text-neutral-100'
            : 'text-neutral-400 dark:text-neutral-500'
        }
      >
        {label}
      </p>
    </button>
  );
};
