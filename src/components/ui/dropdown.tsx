import { useRouter } from 'next/navigation';
import { useState } from 'react';

type MenuItem = {
  title: string;
  hide?: boolean;
  subList?: MenuItem[];
  action?: () => void;
};

const DropdownMenu = ({slug, menu}:{slug?: string; menu: MenuItem[]}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [activeSubMenu, setActiveSubMenu] = useState<string | null>(null);
  const router = useRouter()

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  const toggleSubMenu = (title: string) => {
    setActiveSubMenu(activeSubMenu === title ? null : title);
  };

  const edit = () => {
    router.push(`/event/edit-event/${slug}`)
  };

  return (
    <div className="relative inline-block text-left">
      <button onClick={toggleDropdown} className="p-3 justify-center items-center hover:bg-gray-200">
        <svg width="4" height="20" viewBox="0 0 4 20" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M1.52063 3.5C0.69163 3.5 0.015625 2.829 0.015625 2C0.015625 1.171 0.68262 0.5 1.51062 0.5H1.52063C2.34963 0.5 3.02063 1.171 3.02063 2C3.02063 2.829 2.34963 3.5 1.52063 3.5ZM3.02063 10C3.02063 9.171 2.34963 8.5 1.52063 8.5H1.51062C0.68262 8.5 0.015625 9.171 0.015625 10C0.015625 10.829 0.69163 11.5 1.52063 11.5C2.34963 11.5 3.02063 10.829 3.02063 10ZM3.02063 18C3.02063 17.171 2.34963 16.5 1.52063 16.5H1.51062C0.68262 16.5 0.015625 17.171 0.015625 18C0.015625 18.829 0.69163 19.5 1.52063 19.5C2.34963 19.5 3.02063 18.829 3.02063 18Z" fill="#18181B"/>
        </svg>
      </button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-56 origin-top-right bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-hidden">
          <div className="">
            {menu.map((item, index) => (
              <div key={index} className="">
                {
                  !item.hide && (
                    <>
                 <button
                    onClick={() => item.subList ? toggleSubMenu(item.title) : item.action && item.action() }
                    className={`flex w-full items-center justify-between text-left ${
                      item.title === "Delete event" ? 'text-[#DC2828] border-t border-[#EAECF0]' : ''
                    } hover:bg-gray-100 rounded-md py-3 px-4`}
                  >
                  {item.title}
                  {item.subList && (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                      className={`w-4 h-4 ml-2 transform transition-transform ${
                        activeSubMenu === item.title ? 'rotate-180' : ''
                      }`}
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  )}
                </button>

                {item.subList && activeSubMenu === item.title && (
                  <div className="mt-2 ml-4 space-y-1">
                    {item.subList.map((subItem, subIndex) => (
                      <button
                        key={subIndex}
                        onClick={subItem.action}
                        className="w-full text-left text-gray-600 hover:bg-gray-100 rounded-md p-2"
                      >
                        {subItem.title}
                      </button>
                    ))}
                  </div>
                )}
                   </>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default DropdownMenu;
