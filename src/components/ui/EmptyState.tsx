'use client';
import { Icons } from '@/components/icons/icons';
import React from 'react';

type EmptyStateProps = {
  title: string;
  info: string;
  isTicket?: boolean;
};

export const EmptyState: React.FC<EmptyStateProps>  = ({
  title,
  info,
  isTicket = false
}) => {

  return (
    <div className='flex justify-center items-center flex-col py-[200px]'>
      {
        isTicket ? <Icons.emptyTicketIcon /> : <Icons.emptyIcon />
      }
      <p className='text-[24px] font-semibold text-center text-[#272727]'>{title}</p>
      <p className='text-[14px] font-medium text-center text-[#71717A]'>{info}</p>
    </div>
  );
};
