import React from 'react';
import { IoSearch } from 'react-icons/io5';
import { cn } from '@/lib';

export interface SearchInputProps
  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'value' | 'onChange'> {
  placeholder?: string;
  value: string;
  onChangeText: (text: string) => void;
  iconClassName?: string;
  inputClassName?: string;
  placeholderClassName?: string;
  focusedInputClassName?: string;
  onSubmit?: () => void;
  onClear?: () => void;
  showClearButton?: boolean;
}

export const SearchInput = React.forwardRef<HTMLInputElement, SearchInputProps>(
  (
    {
      placeholder,
      value,
      onChangeText,
      iconClassName,
      inputClassName,
      focusedInputClassName,
      showClearButton = true,
      onClear,
      onSubmit,
      ...rest
    },
    ref
  ) => {
    const [isFocused, setIsFocused] = React.useState(false);

    return (
      <div className="relative w-full">
        <IoSearch
          size={18}
          className={cn(
            'absolute left-4 top-1/2 -translate-y-1/2 text-gray-400 dark:text-gray-200',
            iconClassName
          )}
        />
        <input
          ref={ref}
          type="text"
          value={value}
          placeholder={placeholder}
          onChange={(e) => onChangeText(e.target.value)}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          onKeyDown={(e) => {
            if (e.key === 'Enter') onSubmit?.();
          }}
          className={cn(
            'w-full rounded-xl py-2 pl-12 pr-10 bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-400 dark:placeholder-gray-300 text-base border-0 focus:outline-hidden',
            inputClassName,
            isFocused && focusedInputClassName
          )}
          {...rest}
        />
        {showClearButton && value && (
          <button
            type="button"
            onClick={() => {
              onClear?.();
              onChangeText('');
            }}
            className="absolute right-4 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:text-gray-300 dark:hover:text-gray-100"
          >
            ×
          </button>
        )}
      </div>
    );
  }
);

SearchInput.displayName = 'SearchInput';