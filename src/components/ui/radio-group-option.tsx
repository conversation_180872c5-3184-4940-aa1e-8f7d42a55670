import React from 'react';

type RadioGroupOptionProps = {
  value: string;
  currentValue: string;
  title: string;
  description?: string;
  customContent?: React.ReactNode;
  onChange?: (value: string) => void;
  labelClassName?: string;
};

export const RadioGroupOption: React.FC<RadioGroupOptionProps> = ({
  value,
  currentValue,
  title,
  description,
  customContent,
  onChange,
  labelClassName,
}) => {
  const isSelected = currentValue === value;

  return (
    <label
      htmlFor={`radio-${value}`}
      className="flex cursor-pointer flex-row items-center gap-4 rounded-md bg-gray-100 px-4 py-3 dark:bg-gray-800"
    >
      <div className="flex-1 gap-2.5">
        <div className={`font-medium text-base ${labelClassName || ''}`}>
          {title}
        </div>
        {!customContent && description && (
          <div className="text-sm text-gray-500 dark:text-gray-400">
            {description}
          </div>
        )}
        {customContent && (
          <div>
            {customContent}
          </div>
        )}
      </div>
      <input
        id={`radio-${value}`}
        type="radio"
        name="radio-group"
        value={value}
        checked={isSelected}
        onChange={() => onChange && onChange(value)}
        className="h-6 w-6 cursor-pointer rounded-full border-gray-300 text-blue-600 focus:ring-blue-500"
      />
    </label>
  );
};