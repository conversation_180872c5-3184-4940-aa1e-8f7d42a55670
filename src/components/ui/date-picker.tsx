'use client';
import React, { useCallback, useState } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import {
  addDays,
  format,
  isSameYear,
  isToday,
  isTomorrow,
  startOfDay,
  startOfWeek,
  subDays,
} from 'date-fns';
import {
  MdBoldLabel,
  SmBoldLabel,
  XsBoldLabel,
} from '@/components/ui/typography';
import CalendarIcon from '~/svg/calendar.svg';
import { cn } from '@/lib/utils';

interface DatePickerProps {
  onSelected?: (v: Date) => void;
}

const initDays = (date: Date) => {
  const weekStart = startOfWeek(date);
  const newDays: Date[] = [];
  for (let i = 0; i < 7; i++) {
    newDays.push(addDays(weekStart, i));
  }
  return newDays;
};

export const DatePicker: React.FC<DatePickerProps> = ({ onSelected }) => {
  const [days, setDays] = useState<Date[]>(initDays(new Date()));
  const [currentDate, setCurrentDate] = useState<Date>(new Date());

  const addNewDays = useCallback((date: Date) => {
    setDays(initDays(date));
  }, []);

  const handlePrev = (): void => {
    const dayStart = startOfDay(currentDate);
    const newDate = subDays(dayStart, 1);
    addNewDays(newDate);
    setCurrentDate(newDate);
    if (onSelected) {
      onSelected(newDate);
    }
  };

  const handleNext = (): void => {
    const dayStart = startOfDay(currentDate);
    const newDate = addDays(dayStart, 1);
    addNewDays(newDate);
    setCurrentDate(newDate);
    if (onSelected) {
      onSelected(newDate);
    }
  };

  const showDatePicker = () => {
    console.log('Calendar icon clicked - modal would open here');
  };

  return (
    <div className='w-full max-w-7xl mx-auto'>
      <div className='flex items-center justify-between gap-4 p-4'>
        <div className='w-8 h-8' />
        <div className='flex items-center gap-4'>
          <button
            onClick={handlePrev}
            className='p-1 hover:bg-bg-interactive-primary-light dark:hover:bg-bg-interactive-primary-dark rounded-full transition-colors'
          >
            <ChevronLeft
              size={24}
              className='text-fg-base-light dark:text-fg-base-dark'
            />
          </button>
          <div>
            <MdBoldLabel weight='bold' themed>
              {isToday(currentDate)
                ? 'Today'
                : isTomorrow(currentDate)
                ? 'Tomorrow'
                : format(
                    currentDate,
                    `EEE, dd MMMM${
                      isSameYear(currentDate, new Date()) ? '' : ' yyyy'
                    }`
                  )}
            </MdBoldLabel>
          </div>
          <button
            onClick={handleNext}
            className='p-1 hover:bg-bg-interactive-primary-light dark:hover:bg-bg-interactive-primary-dark rounded-full transition-colors'
          >
            <ChevronRight
              size={24}
              className='text-fg-base-light dark:text-fg-base-dark'
            />
          </button>
        </div>
        <button onClick={showDatePicker} className='size-8'>
          <CalendarIcon size={32} />
        </button>
      </div>

      <div className='flex flex-col gap-2 px-2 md:px-5 py-2'>
        <div className='flex justify-between gap-3'>
          {['S', 'M', 'T', 'W', 'T', 'F', 'S'].map((day, index) => (
            <div
              key={index}
              className='h-10 flex-1 flex items-center justify-center'
            >
              <XsBoldLabel themed weight='bold'>
                {day}
              </XsBoldLabel>
            </div>
          ))}
        </div>

        <div className='flex justify-between gap-3'>
          {days.map((day, index) => {
            const isSelected = format(day, 'd') === format(currentDate, 'd');
            return (
              <button
                key={index}
                onClick={() => {
                  setCurrentDate(day);
                  if (onSelected) {
                    onSelected(day);
                  }
                }}
                className={`h-10 flex-1 flex items-center justify-center rounded-full transition-colors ${
                  isSelected
                    ? 'bg-brand-60'
                    : 'bg-transparent hover:bg-brand-10 dark:hover:bg-brand-80'
                }`}
              >
                <SmBoldLabel
                  weight='bold'
                  className={cn(
                    isSelected
                      ? 'text-white'
                      : 'text-fg-disabled-light dark:text-fg-disabled-dark'
                  )}
                >
                  {format(day, 'd')}
                </SmBoldLabel>
              </button>
            );
          })}
        </div>
      </div>
    </div>
  );
};
