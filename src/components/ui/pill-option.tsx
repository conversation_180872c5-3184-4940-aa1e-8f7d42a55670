import React from 'react';
import { cn } from '@/lib';

type PillOptionProps = {
  option: string;
  className?: string;
  textClassName?: string;
  selected?: boolean;
  onPress?: (option: string) => void;
};

export const PillOption: React.FC<PillOptionProps> = ({
  option,
  className,
  textClassName,
  selected,
  onPress,
}) => {
  return (
    <button
      type="button"
      className={cn(
        'rounded-full border border-accent-muted-light dark:border-accent-muted-dark py-3 px-5',
        className,
        selected &&
          'bg-brand-60 dark:bg-brand-60 border-brand-60 dark:border-brand-60'
      )}
      onClick={() => onPress?.(option)}
      disabled={!onPress}
    >
      <span className={textClassName}>{option}</span>
    </button>
  );
};