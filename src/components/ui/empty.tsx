import React from 'react';
import { useColorScheme } from '@/lib/utils/index';

import { DarkEmptyIcon, LightEmptyIcon, Tiny } from '@/components/ui';

interface EmptyStateProps {
  text?: string;
}

export const EmptyState: React.FC<EmptyStateProps> = ({ text }) => {
  const isDark = useColorScheme() === 'dark';

  return (
    <div className='flex-1 flex flex-col items-center justify-center gap-3.5'>
      {isDark ? <DarkEmptyIcon /> : <LightEmptyIcon />}
      <Tiny className='text-gray-500 dark:text-gray-400'>
        {text || 'There is nothing here'}
      </Tiny>
    </div>
  );
};
