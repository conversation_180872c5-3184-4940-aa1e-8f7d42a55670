import React from 'react';
import * as SelectPrimitive from '@radix-ui/react-select';
import { Check, ChevronDown, ChevronUp } from 'lucide-react';
import { cn } from '@/lib';

const Select = SelectPrimitive.Root;
const SelectGroup = SelectPrimitive.Group;
const SelectValue = SelectPrimitive.Value;
const SelectTrigger = React.forwardRef<
  React.ComponentRef<typeof SelectPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger> & {
    hideChevron?: boolean;
  }
>(({ className, children, hideChevron = false, ...props }, ref) => {
  return (
    <SelectPrimitive.Trigger
      ref={ref}
      className={cn(
        'flex flex-row h-10 items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm text-muted-foreground focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2',
        props.disabled && 'cursor-not-allowed opacity-50',
        className
      )}
      {...props}
    >
      {children}
      {!hideChevron && <ChevronDown size={20} aria-hidden='true' />}
    </SelectPrimitive.Trigger>
  );
});
SelectTrigger.displayName = 'SelectTrigger';

const SelectContent = React.forwardRef<
  React.ComponentRef<typeof SelectPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content> & {
    className?: string;
  }
>(({ className, children, ...props }, ref) => (
  <SelectPrimitive.Portal>
    <SelectPrimitive.Content
      ref={ref}
      className={cn(
        'relative z-50 min-w-32 max-h-60 overflow-auto rounded-md border border-border bg-popover py-2 px-1 shadow-md shadow-black/10',
        className
      )}
      {...props}
    >
      <SelectPrimitive.ScrollUpButton className='flex cursor-default items-center justify-center py-1'>
        <ChevronUp size={14} />
      </SelectPrimitive.ScrollUpButton>
      <SelectPrimitive.Viewport>{children}</SelectPrimitive.Viewport>
      <SelectPrimitive.ScrollDownButton className='flex cursor-default items-center justify-center py-1'>
        <ChevronDown size={14} />
      </SelectPrimitive.ScrollDownButton>
    </SelectPrimitive.Content>
  </SelectPrimitive.Portal>
));
SelectContent.displayName = 'SelectContent';

const SelectLabel = React.forwardRef<
  React.ComponentRef<typeof SelectPrimitive.Label>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>
>(({ className, ...props }, ref) => (
  <SelectPrimitive.Label
    ref={ref}
    className={cn(
      'py-1.5 pl-8 pr-2 text-popover-foreground text-sm font-semibold',
      className
    )}
    {...props}
  />
));
SelectLabel.displayName = 'SelectLabel';

const SelectItem = React.forwardRef<
  React.ComponentRef<typeof SelectPrimitive.Item>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item> & {
    textClassName?: string;
    hasCheck?: boolean;
    customIcon?: React.ReactNode;
  }
>(
  (
    {
      className,
      textClassName,
      hasCheck = true,
      customIcon,
      children,
      ...props
    },
    ref
  ) => {
    return (
      <SelectPrimitive.Item
        ref={ref}
        className={cn(
          'relative flex cursor-default select-none items-center rounded-sm py-2 pl-8 pr-2 outline-hidden focus:bg-accent focus:text-accent-foreground disabled:pointer-events-none disabled:opacity-50',
          className
        )}
        {...props}
      >
        {hasCheck && (
          <SelectPrimitive.ItemIndicator className='absolute left-2 flex items-center justify-center'>
            <Check size={16} />
          </SelectPrimitive.ItemIndicator>
        )}
        <SelectPrimitive.ItemText className={cn('text-sm', textClassName)}>
          {children}
        </SelectPrimitive.ItemText>
        {customIcon}
      </SelectPrimitive.Item>
    );
  }
);
SelectItem.displayName = 'SelectItem';

const SelectSeparator = React.forwardRef<
  React.ComponentRef<typeof SelectPrimitive.Separator>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>
>(({ className, ...props }, ref) => (
  <SelectPrimitive.Separator
    ref={ref}
    className={cn('-mx-1 my-1 h-px bg-muted', className)}
    {...props}
  />
));
SelectSeparator.displayName = 'SelectSeparator';

export {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectSeparator,
  SelectTrigger,
  SelectValue,
};
