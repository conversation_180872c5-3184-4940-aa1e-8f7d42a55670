'use client';

import * as React from 'react';
import { Moon, Sun } from 'lucide-react';
import { useTheme } from 'next-themes';

import { Button } from '@/components/ui';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

export function ModeToggle({ className }: { className?: string }) {
  const { setTheme } = useTheme();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant='outline' size='icon' className={className}>
          <Sun className='h-[1.2rem] w-[1.2rem] scale-100 rotate-0 transition-all dark:scale-0 dark:-rotate-90' />
          <Moon className='absolute h-[1.2rem] w-[1.2rem] scale-0 rotate-90 transition-all dark:scale-100 dark:rotate-0' />
          <span className='sr-only'>Toggle theme</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align='end'>
        <DropdownMenuItem onClick={() => setTheme('light')}>
          Light
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => setTheme('dark')}>
          Dark
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => setTheme('system')}>
          System
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

import { Switch } from '@/components/ui/switch';
import { cn } from '@/lib/utils';
import { Small } from '@/components/ui/typography';
import SunIcon from '~/svg/sun.svg';
import MoonIcon from '~/svg/moon.svg';

interface ThemeToggleSwitchProps {
  className?: string;
  label?: string;
  showLabel?: boolean;
  showIcon?: boolean;
}

export function ThemeToggleSwitch({
  className,
  label = 'Toggle dark mode',
  showLabel = false,
  showIcon = true,
}: ThemeToggleSwitchProps) {
  const { theme, setTheme, resolvedTheme } = useTheme();
  const [mounted, setMounted] = React.useState(false);

  React.useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <div className={cn('flex items-center gap-2', className)}>
        {showIcon && <SunIcon width={24} height={24} className='opacity-50' />}
        <Switch disabled />
        {showLabel && (
          <Small themed className='opacity-50'>
            Loading...
          </Small>
        )}
      </div>
    );
  }

  const isDarkMode =
    theme === 'dark' || (theme === 'system' && resolvedTheme === 'dark');

  const handleToggle = (checked: boolean) =>
    setTheme(checked ? 'dark' : 'light');

  return (
    <div className={cn('flex items-center gap-2', className)}>
      {showIcon &&
        (isDarkMode ? (
          <MoonIcon width={24} height={24} />
        ) : (
          <SunIcon width={24} height={24} />
        ))}
      <Switch
        checked={isDarkMode}
        onCheckedChange={handleToggle}
        aria-label={label}
        className='w-10 h-6 rounded-[32px]'
        thumbClassName='size-5 data-[state=checked]:translate-x-4'
      />
      {showLabel && <Small themed>{isDarkMode ? 'Dark' : 'Light'}</Small>}
    </div>
  );
}
