import React, { useEffect, useState } from 'react';

import { cn } from '@/lib';
import { MdRegularLabel } from '../ui';

import { AiOutlineMinus } from 'react-icons/ai';
import { IoAddOutline } from 'react-icons/io5';

export interface CounterProps {
  value?: number;
  initialValue?: number;
  minimum?: number;
  maximum?: number;
  onValueChange?: (value: number) => void;
  className?: string;
}

export const Counter: React.FC<CounterProps> = ({
  value,
  initialValue = 0,
  minimum = 0,
  maximum = 100,
  onValueChange,
  className,
}) => {
  const [count, setCount] = useState(value ?? initialValue);

  // Sync internal state with controlled value prop
  useEffect(() => {
    if (value !== undefined && value !== count) {
      setCount(value);
    }
  }, [value, count]);

  const handleDecrement = () => {
    if (count > minimum) {
      const newValue = count - 1;
      setCount(newValue);
      onValueChange?.(newValue);
    }
  };

  const handleIncrement = () => {
    if (count < maximum) {
      const newValue = count + 1;
      setCount(newValue);
      onValueChange?.(newValue);
    }
  };

  const isDecrementDisabled = count <= minimum;
  const isIncrementDisabled = count >= maximum;

  return (
    <div
      className={cn(
        'w-[100px] flex flex-row items-center justify-between rounded-full bg-white p-2 dark:bg-gray-800',
        className
      )}
    >
      <button
        onClick={handleDecrement}
        disabled={isDecrementDisabled}
        style={{ opacity: isDecrementDisabled ? 0.2 : 1 }}
        aria-label='Decrement'
        type='button'
      >
        <AiOutlineMinus size={16} color='#4F46E5' />
      </button>

      <MdRegularLabel>{count}</MdRegularLabel>

      <button
        onClick={handleIncrement}
        disabled={isIncrementDisabled}
        style={{ opacity: isIncrementDisabled ? 0.2 : 1 }}
        aria-label='Increment'
        type='button'
      >
        <IoAddOutline size={16} color='#4F46E5' />
      </button>
    </div>
  );
};