import React from 'react';
import { useRouter } from 'next/navigation';
import { IoChevronBackSharp } from 'react-icons/io5';

import { colors } from '../ui';

interface BackProps {
  onBackPress?: () => void;
  color?: string;
}

export const Back: React.FC<BackProps> = ({ onBackPress, color }) => {
  const router = useRouter();

  const handleBack = () => {
    if (onBackPress) {
      onBackPress();
    } else {
      router.back();
    }
  };

  return (
    <button
      className='w-8 h-8 flex items-center justify-center'
      onClick={handleBack}
      aria-label='Back'
      type='button'
    >
      <IoChevronBackSharp
        size={32}
        color={color || colors.brand['60']}
        className='text-fg-link'
      />
    </button>
  );
};
