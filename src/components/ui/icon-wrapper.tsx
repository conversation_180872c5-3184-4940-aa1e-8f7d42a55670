import React from 'react';
import { motion } from 'framer-motion';
import colors from './colors';

interface IconWrapperProps {
  icon: React.ReactElement<{ color?: string; style?: React.CSSProperties }>;
  isFocused: boolean;
  isFilled: boolean;
  hasError: boolean;
  className?: string;
  style?: React.CSSProperties;
}

export const IconWrapper: React.FC<IconWrapperProps> = ({
  icon,
  isFocused,
  isFilled,
  hasError,
  className = '',
  style,
}) => {
  const getIconColor = () => {
    if (hasError) return colors.red[80];
    if (isFilled) return colors.brand[60];
    if (isFocused) return colors.brand[60];
    return colors.grey[50];
  };

  const iconColor = React.useMemo(() => getIconColor(), [isFocused, isFilled, hasError]);

  const iconWithState = React.cloneElement(icon, {
    color: iconColor,
    style: icon.props?.style || {},
  });

  return (
    <motion.div
      className={className}
      style={style}
      animate={{ color: iconColor }}
      transition={{ type: 'tween', duration: 0.15 }}
    >
      {iconWithState}
    </motion.div>
  );
};