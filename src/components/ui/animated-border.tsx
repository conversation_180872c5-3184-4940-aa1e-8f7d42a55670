import React from 'react';

interface AnimatedBorderProps {
  width: number;
  height: number;
  isSelected: boolean;
  borderRadius?: number;
  borderWidth?: number;
  blurRadius?: number;
}

export const AnimatedBorder: React.FC<AnimatedBorderProps> = ({
  width,
  height,
  isSelected,
  borderRadius = 8,
  borderWidth = 2,
  blurRadius = 8,
}) => {
  if (!isSelected) return null;

  const sizeStyle = {
    width,
    height,
    borderRadius,
    padding: borderWidth,
  };

  return (
    <div
      style={sizeStyle}
      className="relative"
      aria-hidden="true"
    >
      {/* Outer glowing border */}
      <svg
        width={width}
        height={height}
        viewBox={`0 0 ${width} ${height}`}
        className="absolute top-0 left-0"
        style={{ borderRadius }}
      >
        <defs>
          <radialGradient id="grad-purple" cx="50%" cy="50%" r="75%">
            <stop offset="0%" stopColor="#5336E2" stopOpacity="1" />
            <stop offset="100%" stopColor="#5336E2" stopOpacity="0" />
          </radialGradient>
          <radialGradient id="grad-magenta" cx="50%" cy="50%" r="75%">
            <stop offset="0%" stopColor="#E733E0" stopOpacity="1" />
            <stop offset="100%" stopColor="#E733E0" stopOpacity="0" />
          </radialGradient>
          <radialGradient id="grad-white" cx="50%" cy="50%" r="75%">
            <stop offset="0%" stopColor="#FFFFFF" stopOpacity="1" />
            <stop offset="100%" stopColor="#FFFFFF" stopOpacity="0" />
          </radialGradient>

          {/* Glow filter */}
          <filter id="glow" x="-50%" y="-50%" width="200%" height="200%" colorInterpolationFilters="sRGB">
            <feDropShadow dx="0" dy="0" stdDeviation={blurRadius / 2} floodColor="#E733E0" floodOpacity="0.6" />
          </filter>

          {/* Animate rotation */}
          <style>
            {`
              @keyframes rotateGlow {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
              }
              .rotate-animation {
                transform-origin: 50% 50%;
                animation: rotateGlow 3s linear infinite;
              }
            `}
          </style>
        </defs>

        <g filter="url(#glow)" className="rotate-animation" transform={`translate(${width / 2}, ${height / 2})`}>
          {/* Translate back after rotation */}
          <circle
            cx={0}
            cy={0}
            r={Math.sqrt(width * width + height * height) / 2}
            fill="url(#grad-purple)"
          />
          <circle
            cx={0}
            cy={0}
            r={Math.sqrt(width * width + height * height) / 2 * 0.8}
            fill="url(#grad-magenta)"
          />
          <circle
            cx={0}
            cy={0}
            r={Math.sqrt(width * width + height * height) / 2 * 0.6}
            fill="url(#grad-white)"
          />
        </g>

        {/* Inner solid rect to create the border effect */}
        <rect
          x={borderWidth}
          y={borderWidth}
          width={width - borderWidth * 2}
          height={height - borderWidth * 2}
          rx={borderRadius}
          ry={borderRadius}
          fill="#fff"
        />
      </svg>
    </div>
  );
};