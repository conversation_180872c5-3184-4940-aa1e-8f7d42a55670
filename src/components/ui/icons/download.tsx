import React from 'react';

interface DownloadIconProps extends React.SVGProps<SVGSVGElement> {
  color?: string;
}

const DownloadIcon: React.FC<DownloadIconProps> = ({ color = '#7257FF', ...props }) => (
  <svg
    width={16}
    height={16}
    viewBox="0 0 16 16"
    fill="none"
    {...props}
  >
    <path
      d="M13.3332 9.7781V11.3337C13.3332 12.4382 12.4377 13.3337 11.3332 13.3337H4.66651C3.56194 13.3337 2.66651 12.4382 2.66651 11.3337L2.6665 9.7781M5.03687 6.81514L7.99984 9.7781M7.99984 9.7781L10.9628 6.81514M7.99984 9.7781V2.66699"
      stroke={color}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export default DownloadIcon;