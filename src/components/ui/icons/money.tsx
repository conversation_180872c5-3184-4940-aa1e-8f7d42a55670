import React from 'react';

interface PaperMoneyIconProps extends React.SVGProps<SVGSVGElement> {
  color?: string;
  width?: number | string;
  height?: number | string;
}

const PaperMoneyIcon: React.FC<PaperMoneyIconProps> = ({
  color = '#fff',
  width = 33,
  height = 34,
  ...props
}) => (
  <svg
    width={width}
    height={height}
    viewBox={`0 0 33 34`}
    fill="none"
    {...props}
  >
    <path
      d="M27.5 15.6465C24.9372 15.6465 22.7839 13.8937 22.1733 11.5215M22.0043 25.0511C22.12 22.1157 24.5362 19.7715 27.5 19.7715M5.50001 18.3965C8.06278 18.3965 10.2162 20.1493 10.8267 22.5214M10.9896 9.1131C10.8132 11.9916 8.42285 14.2715 5.50001 14.2715M25.6068 24.8338C19.5057 26.8942 13.4046 20.8926 7.30342 23.0326C6.42047 23.3423 5.5 22.671 5.5 21.7353V11.833C5.5 10.6444 6.267 9.58953 7.39317 9.2092C13.4943 7.14872 19.5954 13.1503 25.6966 11.0104C26.5795 10.7007 27.5 11.372 27.5 12.3077V22.2099C27.5 23.3986 26.733 24.4534 25.6068 24.8338ZM19.25 17.0215C19.25 18.5403 18.0188 19.7715 16.5 19.7715C14.9812 19.7715 13.75 18.5403 13.75 17.0215C13.75 15.5027 14.9812 14.2715 16.5 14.2715C18.0188 14.2715 19.25 15.5027 19.25 17.0215Z"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export default PaperMoneyIcon;