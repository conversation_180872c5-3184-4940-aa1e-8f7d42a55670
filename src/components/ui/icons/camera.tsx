import React from 'react';

interface CameraIconProps extends React.SVGProps<SVGSVGElement> {
  color?: string;
}

const CameraIcon: React.FC<CameraIconProps> = ({ color = '#FFF', ...props }) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    {...props}
  >
    <path
      d="M7 9H9M6 6H14C15.1046 6 16 6.89543 16 8V16C16 17.1046 15.1046 18 14 18H6C4.89543 18 4 17.1046 4 16V8C4 6.89543 4.89543 6 6 6ZM20 17L16 14V10L20 7V17Z"
      stroke={color}
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export default CameraIcon;