import * as React from 'react';

interface TickProps extends React.SVGProps<SVGSVGElement> {
  color?: string;
  isRTL?: boolean;
}

const Tick: React.FC<TickProps> = ({ color = '#B4A6FF', isRTL = false, ...props }) => (
  <svg
    width={17}
    height={16}
    viewBox="0 0 17 16"
    fill="none"
    style={{ transform: isRTL ? 'scaleX(-1)' : undefined }}
    {...props}
  >
    <path
      d="M13.8332 4.6333L7.44265 11.0238C6.92195 11.5445 6.07773 11.5445 5.55703 11.0238L3.1665 8.6333"
      stroke={color}
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export default Tick;