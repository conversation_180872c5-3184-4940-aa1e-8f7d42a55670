import React from 'react';

interface HeartIconProps extends React.SVGProps<SVGSVGElement> {
  color?: string;
}

const HeartIcon: React.FC<HeartIconProps> = ({ color = '#6E7375', ...props }) => (
  <svg
    width={25}
    height={24}
    viewBox="0 0 25 24"
    fill="none"
    {...props}
  >
    <path
      d="M19.1647 7.23375C18.7737 6.84262 18.3096 6.53234 17.7987 6.32065C17.2878 6.10896 16.7402 6 16.1871 6C15.6341 6 15.0865 6.10896 14.5756 6.32065C14.0647 6.53234 13.6005 6.84262 13.2096 7.23375L12.7066 7.73674C12.5363 7.90705 12.2602 7.90705 12.0899 7.73674L11.5869 7.23375C10.7972 6.44406 9.72612 6.00041 8.60932 6.00041C7.49253 6.00041 6.42147 6.44406 5.63178 7.23375C4.84208 8.02344 4.39844 9.0945 4.39844 10.2113C4.39844 11.3281 4.84208 12.3991 5.63178 13.1888L10.548 18.1051C11.5699 19.1269 13.2266 19.1269 14.2485 18.1051L19.1647 13.1888C19.5558 12.7979 19.8661 12.3337 20.0778 11.8228C20.2895 11.3119 20.3984 10.7643 20.3984 10.2113C20.3984 9.65828 20.2895 9.11068 20.0778 8.59978C19.8661 8.08888 19.5558 7.6247 19.1647 7.23375Z"
      stroke={color}
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export default HeartIcon;