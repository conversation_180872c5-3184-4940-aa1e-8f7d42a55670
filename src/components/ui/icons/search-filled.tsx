import * as React from 'react';

interface SearchFilledIconProps extends React.SVGProps<SVGSVGElement> {
  color?: string;
  stroke?: string;
  isRTL?: boolean;
}

const SearchFilledIcon: React.FC<SearchFilledIconProps> = ({
  color = '#7257FF',
  stroke = '#131214',
  isRTL = false,
  ...props
}) => (
  <svg
    width={25}
    height={24}
    viewBox="0 0 25 24"
    fill="none"
    style={{ transform: isRTL ? 'scaleX(-1)' : undefined }}
    {...props}
  >
    <circle cx="9.7002" cy="9.5" r="4.5" fill={stroke} />
    <path
      d="M11.3105 3.75C15.3738 3.75019 18.6699 7.0562 18.6699 11.1104C18.6698 12.8902 18.0427 14.5174 16.9854 15.79L16.6943 16.1416L17.0166 16.4639L20.377 19.8232C20.4717 19.918 20.4717 20.082 20.377 20.1768C20.3257 20.228 20.2647 20.25 20.2002 20.25C20.1517 20.25 20.1052 20.2379 20.0635 20.21L20.0234 20.1768L16.6641 16.8164L16.3418 16.4941L15.9902 16.7852C14.7176 17.8425 13.0904 18.4696 11.3105 18.4697C7.25681 18.4697 3.95039 15.164 3.9502 11.1104C3.9502 7.05608 7.2471 3.75 11.3105 3.75ZM10.54 5.21973C7.7141 5.21981 5.41037 7.5237 5.41016 10.3496C5.41016 11.0357 5.97403 11.5996 6.66016 11.5996C7.3463 11.5996 7.91016 11.0358 7.91016 10.3496C7.91037 8.89598 9.08638 7.71981 10.54 7.71973C11.2262 7.71973 11.79 7.15587 11.79 6.46973C11.7899 5.78371 11.2261 5.21973 10.54 5.21973Z"
      fill={color}
      stroke={color}
    />
  </svg>
);

export default SearchFilledIcon;