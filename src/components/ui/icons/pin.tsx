import * as React from 'react';

interface PinIconProps extends React.SVGProps<SVGSVGElement> {
  color?: string;
  isRTL?: boolean;
}

const PinIcon: React.FC<PinIconProps> = ({
  color = '#FFF',
  isRTL = false,
  ...props
}) => (
  <svg
    width={24}
    height={24}
    viewBox="0 0 24 24"
    fill="none"
    style={{ transform: isRTL ? 'scaleX(-1)' : undefined }}
    {...props}
  >
    <path
      d="M20.7402 9.07953C20.6802 9.91953 20.2602 10.6795 19.5902 11.1895L15.6402 14.1495V16.2395C15.6402 16.8595 15.4002 17.4395 14.9602 17.8695C14.0902 18.7395 12.5702 18.7395 11.7002 17.8695L9.44018 15.6095L4.52018 20.5295C4.37018 20.6795 4.18018 20.7495 3.99018 20.7495C3.80018 20.7495 3.61018 20.6795 3.46018 20.5295C3.17018 20.2395 3.17018 19.7595 3.46018 19.4695L8.38018 14.5495L6.12018 12.2895C5.68018 11.8495 5.44018 11.2695 5.44018 10.6595C5.44018 10.0495 5.68018 9.45953 6.12018 9.02953C6.55018 8.59953 7.14018 8.34953 7.75018 8.34953H9.84018L12.8002 4.39953C13.3002 3.72953 14.0702 3.30953 14.9102 3.24953C15.7402 3.18953 16.5702 3.49953 17.1602 4.08953L19.9002 6.82953C20.4902 7.41953 20.8002 8.23953 20.7402 9.07953Z"
      fill={color}
    />
  </svg>
);

export default PinIcon;