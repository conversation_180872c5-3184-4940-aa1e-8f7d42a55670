import React from 'react';

interface HomeFilledIconProps extends React.SVGProps<SVGSVGElement> {
  color?: string;
}

const HomeFilledIcon: React.FC<HomeFilledIconProps> = ({ color = '#7257FF', ...props }) => (
  <svg
    width={24}
    height={24}
    viewBox="0 0 24 24"
    fill="none"
    {...props}
  >
    <path
      d="M10.6504 4.44922C11.3928 3.8905 12.4194 3.85495 13.1973 4.34375L13.3496 4.44922L19.3486 8.94824V8.94922C19.9138 9.37518 20.2499 10.047 20.25 10.749V16.749C20.25 18.5429 18.7939 19.999 17 19.999H7C5.20614 19.999 3.75 18.5429 3.75 16.749V10.749C3.75014 10.047 4.08615 9.37518 4.65137 8.94922L4.65039 8.94824L10.6504 4.44922Z"
      fill={color}
      stroke={color}
    />
  </svg>
);

export default HomeFilledIcon;