import * as React from 'react';

interface OnlineIndicatorProps extends React.SVGProps<SVGSVGElement> {
  color?: string;
  strokeColor?: string;
  size?: number | string;
  isRTL?: boolean;
}

const OnlineIndicator: React.FC<OnlineIndicatorProps> = ({
  color = '#23A15D',
  strokeColor = '#131214',
  size = 20,
  isRTL = false,
  ...props
}) => (
  <svg
    width={size}
    height={size}
    viewBox={`0 0 ${size} ${size}`}
    fill="none"
    style={{ transform: isRTL ? 'scaleX(-1)' : undefined }}
    {...props}
  >
    <circle
      cx={Number(size) / 2}
      cy={Number(size) / 2}
      r={Number(size) / 2 - 2} 
      fill={color}
      stroke={strokeColor}
      strokeWidth={4}
    />
  </svg>
);

export default OnlineIndicator;