import React from 'react';

interface ArrowUpRightProps extends React.SVGProps<SVGSVGElement> {
  color?: string;
}

const ArrowUpRight: React.FC<ArrowUpRightProps> = ({ color = '#B4A6FF', ...props }) => (
  <svg
    width={16}
    height={16}
    viewBox="0 0 16 16"
    fill="none"
    {...props}
  >
    <path
      d="M11.8507 9.66L11.8513 4.595C11.852 4.51577 11.8205 4.44431 11.769 4.39284M6.6653 4.47455L11.5669 4.31056C11.6461 4.30987 11.7176 4.34136 11.769 4.39284M11.769 4.39284L4.22656 11.9353"
      stroke={color}
      strokeWidth={1.5}
      strokeLinecap="round"
    />
  </svg>
);

export default ArrowUpRight;