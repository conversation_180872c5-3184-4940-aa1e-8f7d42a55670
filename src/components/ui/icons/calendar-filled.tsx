import React from 'react';

interface CalendarFilledIconProps extends React.SVGProps<SVGSVGElement> {
  color?: string;
  strokeColor?: string;
}

const CalendarFilledIcon: React.FC<CalendarFilledIconProps> = ({
  color = '#7257FF',
  strokeColor = '#131214',
  ...props
}) => (
  <svg
    width={25}
    height={24}
    viewBox="0 0 25 24"
    fill="none"
    {...props}
  >
    <path
      d="M5.6001 8C5.6001 6.34315 6.94324 5 8.6001 5H16.6001C18.257 5 19.6001 6.34315 19.6001 8V16C19.6001 17.6569 18.257 19 16.6001 19H8.6001C6.94324 19 5.6001 17.6569 5.6001 16V8Z"
      fill={color}
    />
    <path
      d="M12.1517 9.40849C12.3351 9.03687 12.8651 9.03687 13.0485 9.40849L13.6593 10.6462C13.7322 10.7938 13.8729 10.8961 14.0358 10.9198L15.4017 11.1182C15.8119 11.1778 15.9756 11.6818 15.6788 11.9711L14.6904 12.9346C14.5726 13.0494 14.5188 13.2149 14.5466 13.3771L14.78 14.7375C14.85 15.146 14.4213 15.4575 14.0545 15.2646L12.8328 14.6223C12.6871 14.5457 12.5131 14.5457 12.3674 14.6223L11.1457 15.2646C10.7789 15.4575 10.3502 15.146 10.4202 14.7375L10.6535 13.3771C10.6814 13.2149 10.6276 13.0494 10.5098 12.9346L9.52135 11.9711C9.22459 11.6818 9.38834 11.1778 9.79845 11.1182L11.1644 10.9198C11.3272 10.8961 11.468 10.7938 11.5409 10.6462L12.1517 9.40849Z"
      fill={color}
    />
    <path
      d="M8.6001 6L8.6001 4M16.6001 6V4M12.6001 6V4M8.6001 19H16.6001C18.257 19 19.6001 17.6569 19.6001 16V8C19.6001 6.34315 18.257 5 16.6001 5H8.6001C6.94324 5 5.6001 6.34315 5.6001 8V16C5.6001 17.6569 6.94324 19 8.6001 19ZM11.1644 10.9198L9.79845 11.1182C9.38834 11.1778 9.22459 11.6818 9.52135 11.9711L10.5098 12.9346C10.6276 13.0494 10.6814 13.2149 10.6535 13.3771L10.4202 14.7375C10.3502 15.146 10.7789 15.4575 11.1457 15.2646L12.3674 14.6223C12.5131 14.5457 12.6871 14.5457 12.8328 14.6223L14.0545 15.2646C14.4213 15.4575 14.85 15.146 14.78 14.7375L14.5466 13.3771C14.5188 13.2149 14.5726 13.0494 14.6904 12.9346L15.6788 11.9711C15.9756 11.6818 15.8119 11.1778 15.4017 11.1182L14.0358 10.9198C13.8729 10.8961 13.7322 10.7938 13.6593 10.6462L13.0485 9.40849C12.8651 9.03687 12.3351 9.03687 12.1517 9.40849L11.5409 10.6462C11.468 10.7938 11.3272 10.8961 11.1644 10.9198Z"
      stroke={strokeColor}
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export default CalendarFilledIcon;