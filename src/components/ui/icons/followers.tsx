import React from 'react';

interface FollowersProps extends React.SVGProps<SVGSVGElement> {
  color?: string;
}

const Followers: React.FC<FollowersProps> = ({ color = '#DBD4FF', ...props }) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    {...props}
  >
    <path
      d="M13.1062 13C13.1062 13 13.3327 12.4987 13.3327 12C13.3327 11.2269 12.9994 10.6667 12.3327 10.3333M10.666 7.25954C11.4509 6.91663 11.9994 6.13348 11.9994 5.22222C11.9994 4.31096 11.4509 3.52781 10.666 3.1849M9.1327 5.22222C9.1327 6.44952 8.13777 7.44444 6.91047 7.44444C5.68317 7.44444 4.68825 6.44952 4.68825 5.22222C4.68825 3.99492 5.68317 3 6.91047 3C8.13777 3 9.1327 3.99492 9.1327 5.22222ZM6.84103 9.875C8.96466 9.875 10.2865 10.805 10.9157 11.4011C11.208 11.6781 11.2297 12.1115 11.0322 12.4626C10.8455 12.7945 10.4942 13 10.1133 13H3.72499C3.33338 13 2.97221 12.7888 2.78022 12.4475C2.58643 12.1029 2.59694 11.6789 2.87484 11.3978C3.4642 10.8017 4.72124 9.875 6.84103 9.875Z"
      stroke={color}
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export default Followers;