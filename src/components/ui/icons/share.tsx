import * as React from 'react';

interface ShareIconProps extends React.SVGProps<SVGSVGElement> {
  color?: string;
  isRTL?: boolean;
}

const ShareIcon: React.FC<ShareIconProps> = ({
  color = '#070707',
  isRTL = false,
  ...props
}) => (
  <svg
    width={24}
    height={24}
    viewBox="0 0 24 24"
    fill="none"
    style={{ transform: isRTL ? 'scaleX(-1)' : undefined }}
    {...props}
  >
    <path
      d="M5.60156 12L5.60156 17C5.60156 18.6569 6.94471 20 8.60156 20H15.4016C17.0584 20 18.4016 18.6569 18.4016 17V12M15.2016 7.2L12.0016 4M12.0016 4L8.80156 7.2M12.0016 4V14.4"
      stroke={color}
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export default ShareIcon;