import * as React from 'react';

interface PencilProps extends React.SVGProps<SVGSVGElement> {
  color?: string;
  isRTL?: boolean;
}

const Pencil: React.FC<PencilProps> = ({ color = '#B4A6FF', isRTL = false, ...props }) => (
  <svg
    width={16}
    height={16}
    viewBox="0 0 16 16"
    fill="none"
    style={{ transform: isRTL ? 'scaleX(-1)' : undefined }}
    {...props}
  >
    <path
      d="M10.6892 3.11991C10.6892 3.11991 11.0039 2.86186 11.1919 2.78398C11.3799 2.7061 11.5814 2.66602 11.7849 2.66602C11.9885 2.66602 12.19 2.7061 12.378 2.78398C12.566 2.86186 12.7368 2.97601 12.8807 3.11991C13.0246 3.26381 13.1388 3.43465 13.2167 3.62266C13.2946 3.81068 13.3346 4.0122 13.3346 4.2157C13.3346 4.41921 13.2946 4.62073 13.2167 4.80874C13.1388 4.99676 12.8807 5.3115 12.8807 5.3115C12.8807 5.3115 7.90266 11.7327 5.48412 12.7081C4.88733 12.9488 4.08154 13.1643 3.45337 13.3141C2.98521 13.4258 2.57481 13.0154 2.6865 12.5473C2.83637 11.9191 3.05183 11.1133 3.29253 10.5165C4.26796 8.09799 10.6892 3.11991 10.6892 3.11991Z"
      stroke={color}
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export default Pencil;