import React from 'react';

interface ChevronRightProps extends React.SVGProps<SVGSVGElement> {
  color?: string;
}

const ChevronRight: React.FC<ChevronRightProps> = ({ color = '#7257FF', ...props }) => (
  <svg
    width={24}
    height={24}
    viewBox="0 0 24 24"
    fill="none"
    {...props}
  >
    <path
      d="M9 18L14.6854 12.7071C15.1049 12.3166 15.1049 11.6834 14.6854 11.2929L9 6"
      stroke={color}
      strokeWidth={1.5}
      strokeLinecap="round"
    />
  </svg>
);

export default ChevronRight;