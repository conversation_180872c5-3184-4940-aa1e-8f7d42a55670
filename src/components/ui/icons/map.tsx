import React from 'react';


interface MapIconProps extends React.SVGProps<SVGSVGElement> {
  color?: string;
  fill?: string;
}

const MapIcon: React.FC<MapIconProps> = ({
  color = '#B4A6FF',
  fill = 'white',
  style,
  ...props
}) => {
  return (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      {...props}
      style={{
        ...style,
      }}
    >
      <path
        d="M11.3332 10.3327C11.3332 10.5168 11.1839 10.666 10.9998 10.666C10.8157 10.666 10.6665 10.5168 10.6665 10.3327C10.6665 10.1486 10.8157 9.99935 10.9998 9.99935C11.1839 9.99935 11.3332 10.1486 11.3332 10.3327Z"
        fill={fill}
      />
      <path
        d="M10.9998 13.3327L10.6277 13.9839C10.8583 14.1156 11.1414 14.1156 11.3719 13.9839L10.9998 13.3327ZM7.99984 14.0827C8.41405 14.0827 8.74984 13.7469 8.74984 13.3327C8.74984 12.9185 8.41405 12.5827 7.99984 12.5827V14.0827ZM12.5832 6.66602C12.5832 7.08023 12.919 7.41602 13.3332 7.41602C13.7474 7.41602 14.0832 7.08023 14.0832 6.66602H12.5832ZM12.1968 3.52968C12.4897 3.23679 12.4897 2.76191 12.1968 2.46902C11.9039 2.17613 11.4291 2.17613 11.1362 2.46902L12.1968 3.52968ZM4.80284 5.86301C5.09574 6.15591 5.57061 6.15591 5.8635 5.86301C6.1564 5.57012 6.1564 5.09525 5.8635 4.80235L4.80284 5.86301Z"
        fill={color}
      />
      {/* Keep adding the rest of the <path> elements similarly */}
    </svg>
  );
};

export default MapIcon;