import React from 'react';

interface DiscountIconProps extends React.SVGProps<SVGSVGElement> {
  color?: string;
}

const DiscountIcon: React.FC<DiscountIconProps> = ({ color = '#070707', ...props }) => (
  <svg
    width={24}
    height={24}
    viewBox="0 0 24 24"
    fill="none"
    {...props}
  >
    <path
      d="M9.37596 14.584C9.1462 14.9286 9.23933 15.3943 9.58398 15.624C9.92862 15.8538 10.3943 15.7607 10.624 15.416L9.37596 14.584ZM14.624 9.41601C14.8538 9.07136 14.7606 8.60571 14.416 8.37595C14.0713 8.14619 13.6057 8.23932 13.3759 8.58397L14.624 9.41601ZM8.69519 5.14622L8.59763 5.88985L8.69519 5.14622ZM10.4944 4.5827L9.98578 4.03152L10.4944 4.5827ZM6.2591 6.85233L5.52196 6.71406L6.2591 6.85233ZM5.14712 8.32765L5.49205 8.99363L5.14712 8.32765ZM4.21662 11.0882L4.88733 10.7526L4.21662 11.0882ZM4.21662 12.9118L3.5459 12.5762L4.21662 12.9118ZM5.14712 15.6723L5.49205 15.0064H5.49205L5.14712 15.6723ZM6.2591 17.1477L5.52196 17.2859L6.2591 17.1477ZM8.69519 18.8538L8.79276 19.5974L8.69519 18.8538ZM10.4944 19.4173L9.98578 19.9685L10.4944 19.4173ZM13.5056 19.4173L12.997 18.8661L13.5056 19.4173ZM15.3048 18.8538L15.2072 19.5974L15.3048 18.8538ZM17.7409 17.1477L17.0038 17.0094L17.7409 17.1477ZM18.8529 15.6723L18.5079 15.0064L18.8529 15.6723ZM19.7834 12.9118L20.4541 12.5762L19.7834 12.9118ZM19.7834 11.0882L19.1127 10.7526L19.7834 11.0882ZM18.8529 8.32765L18.5079 8.99363L18.8529 8.32765ZM17.7409 6.85233L18.478 6.71406L17.7409 6.85233ZM15.3048 5.14622L15.4024 5.88985L15.3048 5.14622ZM13.5056 4.5827L14.0142 4.03152V4.03152L13.5056 4.5827ZM10 9.5H9.25C9.25 9.36193 9.36193 9.25 9.5 9.25V10V10.75C10.1904 10.75 10.75 10.1904 10.75 9.5H10ZM9.5 10V9.25C9.63807 9.25 9.75 9.36193 9.75 9.5H9H8.25C8.25 10.1904 8.80964 10.75 9.5 10.75V10ZM9 9.5H9.75C9.75 9.63807 9.63807 9.75 9.5 9.75V9V8.25C8.80964 8.25 8.25 8.80964 8.25 9.5H9ZM9.5 9V9.75C9.36193 9.75 9.25 9.63807 9.25 9.5H10H10.75C10.75 8.80964 10.1904 8.25 9.5 8.25V9ZM15 14.5H14.25C14.25 14.3619 14.3619 14.25 14.5 14.25V15V15.75C15.1904 15.75 15.75 15.1904 15.75 14.5H15ZM14.5 15V14.25C14.6381 14.25 14.75 14.3619 14.75 14.5H14H13.25C13.25 15.1904 13.8096 15.75 14.5 15.75V15ZM14 14.5H14.75C14.75 14.6381 14.6381 14.75 14.5 14.75V14V13.25C13.8096 13.25 13.25 13.8096 13.25 14.5H14ZM14.5 14V14.75C14.3619 14.75 14.25 14.6381 14.25 14.5H15H15.75C15.75 13.8096 15.1904 13.25 14.5 13.25V14ZM10 15L10.624 15.416L14.624 9.41601L14 8.99999L13.3759 8.58397L9.37596 14.584L10 15ZM8.69519 5.14622L8.59763 5.88985C9.47146 6.00449 10.3571 5.72997 11.003 5.13388L10.4944 4.5827L9.98578 4.03152C9.67311 4.32005 9.23391 4.46047 8.79276 4.40259L8.69519 5.14622ZM6.2591 6.85233L6.99624 6.99061C7.12606 6.29854 7.81485 5.78715 8.59763 5.88985L8.69519 5.14622L8.79276 4.40259C7.26578 4.20225 5.80691 5.19497 5.52196 6.71406L6.2591 6.85233ZM5.14712 8.32765L5.49205 8.99363C6.27488 8.58818 6.83425 7.8542 6.99624 6.99061L6.2591 6.85233L5.52196 6.71406C5.44782 7.1093 5.1884 7.46165 4.80219 7.66168L5.14712 8.32765ZM4.21662 11.0882L4.88733 10.7526C4.57115 10.1207 4.81619 9.34368 5.49205 8.99363L5.14712 8.32765L4.80219 7.66168C3.42464 8.37516 2.84934 10.0318 3.5459 11.4238L4.21662 11.0882ZM4.21662 12.9118L4.88733 13.2474C5.28133 12.4601 5.28133 11.5399 4.88733 10.7526L4.21662 11.0882L3.5459 11.4238C3.72847 11.7887 3.72847 12.2113 3.5459 12.5762L4.21662 12.9118ZM5.14712 15.6723L5.49205 15.0064C4.81619 14.6563 4.57115 13.8793 4.88733 13.2474L4.21662 12.9118L3.5459 12.5762C2.84934 13.9682 3.42464 15.6248 4.80219 16.3383L5.14712 15.6723ZM6.2591 17.1477L6.99624 17.0094C6.83425 16.1458 6.27488 15.4118 5.49205 15.0064L5.14712 15.6723L4.80219 16.3383C5.1884 16.5384 5.44782 16.8907 5.52196 17.2859L6.2591 17.1477ZM8.69519 18.8538L8.59763 18.1102C7.81485 18.2129 7.12606 17.7015 6.99624 17.0094L6.2591 17.1477L5.52196 17.2859C5.80691 18.805 7.26578 19.7977 8.79276 19.5974L8.69519 18.8538ZM10.4944 19.4173L11.003 18.8661C10.3571 18.27 9.47146 17.9955 8.59763 18.1102L8.69519 18.8538L8.79276 19.5974C9.2339 19.5395 9.67311 19.68 9.98578 19.9685L10.4944 19.4173ZM13.5056 19.4173L12.997 18.8661C12.4423 19.378 11.5577 19.378 11.003 18.8661L10.4944 19.4173L9.98578 19.9685C11.115 21.0105 12.885 21.0105 14.0142 19.9685L13.5056 19.4173ZM15.3048 18.8538L15.4024 18.1102C14.5285 17.9955 13.6429 18.27 12.997 18.8661L13.5056 19.4173L14.0142 19.9685C14.3269 19.68 14.7661 19.5395 15.2072 19.5974L15.3048 18.8538ZM17.7409 17.1477L17.0038 17.0094C16.8739 17.7015 16.1852 18.2129 15.4024 18.1102L15.3048 18.8538L15.2072 19.5974C16.7342 19.7977 18.1931 18.805 18.478 17.2859L17.7409 17.1477ZM18.8529 15.6723L18.5079 15.0064C17.7251 15.4118 17.1658 16.1458 17.0038 17.0094L17.7409 17.1477L18.478 17.2859C18.5522 16.8907 18.8116 16.5384 19.1978 16.3383L18.8529 15.6723ZM19.7834 12.9118L19.1127 13.2474C19.4289 13.8793 19.1838 14.6563 18.5079 15.0064L18.8529 15.6723L19.1978 16.3383C20.5754 15.6248 21.1507 13.9682 20.4541 12.5762L19.7834 12.9118ZM19.7834 11.0882L19.1127 10.7526C18.7187 11.5399 18.7187 12.4601 19.1127 13.2474L19.7834 12.9118L20.4541 12.5762C20.2715 12.2113 20.2715 11.7887 20.4541 11.4238L19.7834 11.0882ZM18.8529 8.32765L18.5079 8.99363C19.1838 9.34368 19.4289 10.1207 19.1127 10.7526L19.7834 11.0882L20.4541 11.4238C21.1507 10.0318 20.5754 8.37516 19.1978 7.66168L18.8529 8.32765ZM17.7409 6.85233L17.0038 6.99061C17.1658 7.8542 17.7251 8.58818 18.5079 8.99363L18.8529 8.32765L19.1978 7.66168C18.8116 7.46165 18.5522 7.1093 18.478 6.71406L17.7409 6.85233ZM15.3048 5.14622L15.4024 5.88985C16.1851 5.78715 16.8739 6.29854 17.0038 6.99061L17.7409 6.85233L18.478 6.71406C18.1931 5.19497 16.7342 4.20225 15.2072 4.40259L15.3048 5.14622ZM13.5056 4.5827L12.997 5.13388C13.6429 5.72997 14.5285 6.00449 15.4024 5.88985L15.3048 5.14622L15.2072 4.40259C14.7661 4.46047 14.3269 4.32005 14.0142 4.03152L13.5056 4.5827ZM13.5056 4.5827L14.0142 4.03152C12.885 2.98949 11.115 2.98949 9.98578 4.03152L10.4944 4.5827L11.003 5.13388C11.5577 4.62204 12.4423 4.62204 12.997 5.13388L13.5056 4.5827Z"
      fill={color}
    />
  </svg>
);

export default DiscountIcon;