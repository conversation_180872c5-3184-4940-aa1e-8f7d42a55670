import * as React from 'react';

interface SearchIconProps extends React.SVGProps<SVGSVGElement> {
  color?: string;
  isRTL?: boolean;
}

const SearchIcon: React.FC<SearchIconProps> = ({
  color = '#6E7375',
  isRTL = false,
  ...props
}) => (
  <svg
    width={25}
    height={24}
    viewBox="0 0 25 24"
    fill="none"
    style={{ transform: isRTL ? 'scaleX(-1)' : undefined }}
    {...props}
  >
    <path
      d="M20.1992 20L16.3325 16.1333M18.4214 11.1111C18.4214 15.0385 15.2377 18.2222 11.3103 18.2222C7.38297 18.2222 4.19922 15.0385 4.19922 11.1111C4.19922 7.18375 7.38297 4 11.3103 4C15.2377 4 18.4214 7.18375 18.4214 11.1111Z"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export default SearchIcon;