import * as React from 'react';

interface XIconProps extends React.SVGProps<SVGSVGElement> {
  color?: string;
  isRTL?: boolean;
}

const XIcon: React.FC<XIconProps> = ({
  color = 'white',
  isRTL = false,
  ...props
}) => (
  <svg
    width={24}
    height={24}
    viewBox="0 0 24 24"
    fill="none"
    style={{ transform: isRTL ? 'scaleX(-1)' : undefined }}
    {...props}
  >
    <defs>
      <clipPath id="clip0_24508_12449">
        <rect width={24} height={24} fill="white" />
      </clipPath>
    </defs>
    <g clipPath="url(#clip0_24508_12449)">
      <path
        d="M19.3564 4.51029L19.3791 4.48438H19.3446H16.8V4.47625L16.7882 4.48972L12.6116 9.2699L8.99639 4.49057L9.00459 4.48438H8.98393H3.73393H3.70231L3.72152 4.50949L9.98696 12.7011L4.04895 19.4897L4.02629 19.5156H4.06071H6.60536V19.5237L6.61711 19.5103L11.1956 14.28L15.1912 19.5095L15.1831 19.5156H15.2036H20.325H20.3564L20.3375 19.4906L13.8041 10.8543L19.3564 4.51029ZM0.015625 3.42857C0.015625 1.54613 1.54613 0.015625 3.42857 0.015625H20.5714C22.4539 0.015625 23.9844 1.54613 23.9844 3.42857V20.5714C23.9844 22.4539 22.4539 23.9844 20.5714 23.9844H3.42857C1.54613 23.9844 0.015625 22.4539 0.015625 20.5714V3.42857ZM6.7334 5.9567H8.21008L17.2882 17.9629H15.9131L6.7334 5.9567Z"
        fill={color}
        stroke={color}
        strokeWidth={0.03125}
      />
    </g>
  </svg>
);

export default XIcon;