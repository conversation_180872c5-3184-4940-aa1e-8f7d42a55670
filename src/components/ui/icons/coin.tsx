import React from 'react';

interface CoinIconProps extends React.SVGProps<SVGSVGElement> {
  color?: string;
}

const CoinIcon: React.FC<CoinIconProps> = ({ color = '#FFFFFF', ...props }) => (
  <svg
    width={16}
    height={16}
    viewBox="0 0 16 16"
    fill="none"
    {...props}
  >
    <path
      d="M12.3332 8.43359V10.9003C12.3332 12.9803 10.3932 14.6669 7.99984 14.6669C5.6065 14.6669 3.6665 12.9803 3.6665 10.9003V8.43359C3.6665 10.5136 5.6065 12.0003 7.99984 12.0003C10.3932 12.0003 12.3332 10.5136 12.3332 8.43359Z"
      stroke={color}
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M12.3332 5.09968C12.3332 5.70634 12.1665 6.26634 11.8732 6.74634C11.1598 7.91967 9.69317 8.66634 7.99984 8.66634C6.3065 8.66634 4.83984 7.91967 4.12651 6.74634C3.83317 6.26634 3.6665 5.70634 3.6665 5.09968C3.6665 4.05968 4.15317 3.11967 4.93317 2.43967C5.71983 1.75301 6.79984 1.33301 7.99984 1.33301C9.19984 1.33301 10.2798 1.75301 11.0665 2.43301C11.8465 3.11968 12.3332 4.05968 12.3332 5.09968Z"
      stroke={color}
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M12.3332 5.09968V8.43301C12.3332 10.513 10.3932 11.9997 7.99984 11.9997C5.6065 11.9997 3.6665 10.513 3.6665 8.43301V5.09968C3.6665 3.01968 5.6065 1.33301 7.99984 1.33301C9.19984 1.33301 10.2798 1.75301 11.0665 2.43301C11.8465 3.11968 12.3332 4.05968 12.3332 5.09968Z"
      stroke={color}
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export default CoinIcon;