import React from 'react';

interface CaretDownProps extends React.SVGProps<SVGSVGElement> {}

const CaretDown: React.FC<CaretDownProps> = (props) => (
  <svg
    width={12}
    height={13}
    fill="none"
    {...props}
  >
    <path
      d="M9.75 4.744 6 8.494l-3.75-3.75"
      stroke="currentColor"
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export default CaretDown;