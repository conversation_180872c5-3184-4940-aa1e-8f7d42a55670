import * as React from 'react';

interface SongIconProps extends React.SVGProps<SVGSVGElement> {
  color?: string;
  isRTL?: boolean;
}

const SongIcon: React.FC<SongIconProps> = ({
  color = '#B4A6FF',
  isRTL = false,
  ...props
}) => (
  <svg
    width={16}
    height={16}
    viewBox="0 0 16 16"
    fill="none"
    style={{ transform: isRTL ? 'scaleX(-1)' : undefined }}
    {...props}
  >
    <path
      d="M5.22255 9.55484L4.71523 3.6925C4.62526 2.6529 5.34969 1.71884 6.37899 1.54729L9.53223 1.02174C10.683 0.829948 11.753 1.65979 11.8536 2.8221L12.3337 8.36966M5.22255 9.55484C5.22255 10.5367 4.42661 11.3326 3.44477 11.3326C2.46293 11.3326 1.66699 10.5367 1.66699 9.55484C1.66699 8.573 2.46293 7.77706 3.44477 7.77706C4.42661 7.77706 5.22255 8.573 5.22255 9.55484ZM12.3337 8.36966C12.3337 9.35149 11.5377 10.1474 10.5559 10.1474C9.57404 10.1474 8.7781 9.35149 8.7781 8.36966C8.7781 7.38782 9.57404 6.59188 10.5559 6.59188C11.5377 6.59188 12.3337 7.38782 12.3337 8.36966Z"
      stroke={color}
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export default SongIcon;