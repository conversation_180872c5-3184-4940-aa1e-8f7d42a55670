import React from "react";
import { useController, type FieldValues, type Control, type Path, type RegisterOptions } from "react-hook-form";

export type OptionType = { label: string; value: string | number };

interface SelectProps {
  value?: string | number;
  label?: string;
  error?: string;
  options?: OptionType[];
  placeholder?: string;
  disabled?: boolean;
  onSelect?: (value: string | number) => void;
}

export const Select: React.FC<SelectProps> = ({
  value,
  label,
  error,
  options = [],
  placeholder = "Select...",
  disabled,
  onSelect,
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedValue = e.target.value;
    onSelect?.(selectedValue);
  };

  return (
    <>
      {label && <label style={{ display: "block", marginBottom: "0.5rem" }}>{label}</label>}

      <select
        disabled={disabled}
        value={value ?? ""}
        onChange={handleChange}
        style={{
          border: error ? "1px solid red" : "",
        }}
        className="h-[52px] w-full flex items-center justify-between rounded-md border border-border-subtle-light p-3 dark:border-border-subtle-dark cursor-pointer"
      >
        <option value="" disabled>
          {placeholder}
        </option>
        {options.map((opt) => (
          <option key={opt.value} value={opt.value}>
            {opt.label}
          </option>
        ))}
      </select>

      {error && <p style={{ color: "red", marginTop: "0.25rem" }}>{error}</p>}
    </>
  );
};

interface ControlledSelectProps<T extends FieldValues> extends Omit<SelectProps, "value" | "onSelect" | "error"> {
  name: Path<T>;
  control: Control<T>;
  rules?: RegisterOptions<T, Path<T>>;
}

export const ControlledSelect = <T extends FieldValues>({
  name,
  control,
  rules,
  ...selectProps
}: ControlledSelectProps<T>) => {
  const { field, fieldState } = useController({ control, name, rules });

  return (
    <Select
      {...selectProps}
      value={field.value}
      onSelect={field.onChange}
      error={fieldState.error?.message}
    />
  );
}