import React, { useMemo } from 'react';
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectGroup,
  SelectItem,
  MdRegularLabel,
} from '@/components/ui';

interface QuantitySelectorProps {
  value: number | undefined;
  onValueChange: (quantity: number) => void;
  minimum?: number;
  maximum?: number;
  step?: number;
  options?: number[];
  disabled?: boolean;
  placeholder?: string;
  triggerClassName?: string;
  contentClassName?: string;
  itemClassName?: string;
  itemTextClassName?: string;
  formatter?: (value: number) => string;
  includeZero?: boolean;
}

const QuantitySelector: React.FC<QuantitySelectorProps> = ({
  value,
  onValueChange,
  minimum = 0,
  maximum = 10,
  step = 1,
  options,
  disabled = false,
  placeholder = 'Select quantity',
  triggerClassName,
  contentClassName,
  itemClassName,
  itemTextClassName = 'text-gray-900 dark:text-white',
  formatter,
  includeZero = true,
}) => {
  const quantityOptions = useMemo(() => {
    if (options) return options;
    const opts: number[] = [];
    const start = includeZero ? Math.min(minimum, 0) : Math.max(minimum, 1);
    for (let i = start; i <= maximum; i += step) {
      opts.push(i);
    }
    return opts;
  }, [options, minimum, maximum, step, includeZero]);

  const formatLabel = (val: number) =>
    formatter ? formatter(val) : val.toString();

  return (
    <Select
      value={value !== undefined ? value.toString() : ''}
      onValueChange={(val) => onValueChange(Number(val))}
      disabled={disabled}
    >
      <SelectTrigger className={triggerClassName}>
        <MdRegularLabel>
          {value !== undefined ? formatLabel(value) : placeholder}
        </MdRegularLabel>
        <SelectValue />
      </SelectTrigger>

      <SelectContent className={contentClassName}>
        <SelectGroup>
          {quantityOptions.map((qty) => (
            <SelectItem
              key={qty}
              value={qty.toString()}
              className={itemClassName}
            >
              <span className={itemTextClassName}>{formatLabel(qty)}</span>
            </SelectItem>
          ))}
        </SelectGroup>
      </SelectContent>
    </Select>
  );
};

export default QuantitySelector;
