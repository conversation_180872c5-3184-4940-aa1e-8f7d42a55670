import React from 'react';
import { cn } from '@/lib';

interface RadioGroupProps
  extends Omit<React.FieldsetHTMLAttributes<HTMLFieldSetElement>, 'onChange'> {
  className?: string;
  value?: string;
  onChange?: (value: string) => void;
  children: React.ReactNode;
}

export const RadioGroup: React.FC<RadioGroupProps> = ({
  className,
  value,
  onChange,
  children,
  ...props
}) => {
  return (
  <fieldset
    role="radiogroup"
    className={`grid gap-2 ${className ?? ''}`}
    {...props}
  >
    {React.Children.map(children, (child) => {
      if (!React.isValidElement<{
        value: string;
        checked?: boolean;
        onChange?: () => void;
        name?: string;
      }>(child)) return null;

      return React.cloneElement(child, {
        checked: child.props.value === value,
        onChange: () => onChange?.(child.props.value),
        name: props.id || 'radio-group',
      });
    })}
  </fieldset>
);
};

interface RadioGroupItemProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  className?: string;
  value: string;
  label?: string;
  disabled?: boolean;
}

export const RadioGroupItem: React.FC<RadioGroupItemProps> = ({
  className,
  value,
  label,
  disabled,
  checked,
  onChange,
  name,
  ...props
}) => {
  return (
    <label
      className={cn(
        'inline-flex items-center cursor-pointer gap-2',
        disabled && 'cursor-not-allowed opacity-50',
        className
      )}
      aria-checked={checked}
      role='radio'
    >
      <input
        type='radio'
        value={value}
        checked={checked}
        onChange={onChange}
        disabled={disabled}
        name={name}
        className='sr-only'
        {...props}
      />
      <span
        className={cn(
          'h-6 w-6 rounded-full border border-gray-300 dark:border-gray-700 flex items-center justify-center',
          checked
            ? 'border-4 border-blue-600 dark:border-blue-400'
            : 'border-gray-300 dark:border-gray-700'
        )}
      >
        {checked && (
          <span className='h-2 w-2 rounded-full bg-blue-600 dark:bg-blue-400' />
        )}
      </span>
      {label && <span>{label}</span>}
    </label>
  );
};
