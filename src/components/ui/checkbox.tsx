import React, { useCallback } from 'react';
import { useColorScheme } from '@/lib/utils/index';
import { motion, AnimatePresence } from 'framer-motion';

import { cn } from '@/lib';

// Sizes
const SIZE = 20;
const WIDTH = 56;
const HEIGHT = 32;
const THUMB_HEIGHT = 28;
const THUMB_WIDTH = 28;
const THUMB_OFFSET = 2;

interface RootProps extends Omit<React.LabelHTMLAttributes<HTMLLabelElement>, 'onChange'> {
  checked: boolean;
  onChange: (checked: boolean) => void;
  disabled?: boolean;
  className?: string;
  children?: React.ReactNode;
  accessibilityLabel: string;
}

export const Root: React.FC<RootProps> = ({
  checked,
  onChange,
  disabled,
  className = '',
  children,
  accessibilityLabel,
  ...props
}) => {
  const handleClick = useCallback(() => {
    if (disabled) return;
    onChange(!checked);
  }, [checked, onChange, disabled]);

  return (
    <label
      role="checkbox"
      aria-checked={checked}
      aria-label={accessibilityLabel}
      onClick={handleClick}
      className={cn(
        'inline-flex items-center cursor-pointer select-none',
        disabled && 'opacity-50 cursor-not-allowed',
        className
      )}
      {...props}
    >
      {children}
    </label>
  );
};

interface LabelProps {
  text: string;
  className?: string;
  testId?: string;
}

export const Label: React.FC<LabelProps> = ({ text, testId, className = '' }) => (
  <span data-testid={testId} className={cn('pl-2', className)}>
    {text}
  </span>
);

interface IconProps {
  checked: boolean;
}

export const CheckboxIcon: React.FC<IconProps> = ({ checked }) => {
  const isDark = useColorScheme() === 'dark';
  const borderColor = checked ? 'bg-brand-500 border-brand-500' : 'bg-transparent border-gray-300 dark:border-gray-600';

  return (
    <div
      style={{ width: SIZE, height: SIZE }}
      className={cn('rounded-[5px] border-2 flex items-center justify-center', borderColor)}
    >
      <AnimatePresence>
        {checked && (
          <motion.svg
            key="checkmark"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            className="text-white"
          >
            <path
              d="m16.726 7-.64.633c-2.207 2.212-3.878 4.047-5.955 6.158l-2.28-1.928-.69-.584L6 12.66l.683.577 2.928 2.477.633.535.591-.584c2.421-2.426 4.148-4.367 6.532-6.756l.633-.64L16.726 7Z"
              fill="currentColor"
            />
          </motion.svg>
        )}
      </AnimatePresence>
    </div>
  );
};

export const Checkbox: React.FC<
  RootProps & { label?: string }
> = ({ checked, label, ...props }) => {
  return (
    <Root checked={checked} {...props} accessibilityLabel={label || 'checkbox'}>
      <CheckboxIcon checked={checked} />
      {label && <Label text={label} />}
    </Root>
  );
};

export const RadioIcon: React.FC<IconProps> = ({ checked }) => {
  const isDark = useColorScheme() === 'dark';
  const borderColor = checked ? 'border-brand-600' : 'border-gray-300 dark:border-gray-600';

  return (
    <div
      style={{ width: SIZE, height: SIZE }}
      className={cn(
        'rounded-full border-2 flex items-center justify-center bg-transparent',
        borderColor
      )}
    >
      <AnimatePresence>
        {checked && (
          <motion.div
            key="inner-circle"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="h-2.5 w-2.5 rounded-full bg-brand-600"
          />
        )}
      </AnimatePresence>
    </div>
  );
};

export const Radio: React.FC<
  RootProps & { label?: string }
> = ({ checked, label, ...props }) => {
  return (
    <Root
      checked={checked}
      {...props}
      accessibilityLabel={label || 'radio button'}
      role="radio"
    >
      <RadioIcon checked={checked} />
      {label && <Label text={label} />}
    </Root>
  );
};

export const SwitchIcon: React.FC<IconProps> = ({ checked }) => {
  const isDark = useColorScheme() === 'dark';
  const backgroundColor = checked ? 'bg-brand-600' : isDark ? 'bg-gray-700' : 'bg-gray-300';

  return (
    <div className="relative w-14 h-8 rounded-xl">
      <div className={cn('absolute inset-0 rounded-xl', backgroundColor)} />
      <motion.div
        className="absolute top-[2px] h-[28px] w-[28px] rounded-full bg-white shadow-sm"
        initial={false}
        animate={{
          x: checked ? THUMB_OFFSET : WIDTH - THUMB_WIDTH - THUMB_OFFSET,
        }}
        transition={{ type: 'spring', stiffness: 700, damping: 30 }}
      />
    </div>
  );
};

export const Switch: React.FC<
  RootProps & { label?: string }
> = ({ checked, label, ...props }) => {
  return (
    <Root checked={checked} {...props} accessibilityLabel={label || 'switch'} role="switch">
      <SwitchIcon checked={checked} />
      {label && <Label text={label} />}
    </Root>
  );
};