'use client';

import semanticColors from '@/components/ui/semantic-colors';
import React, { useEffect, useState } from 'react';
import GooglePlacesAutocomplete, {
  geocodeByPlaceId,
  getLatLng,
} from 'react-google-places-autocomplete';

interface LocationInputType {
  city: string;
  state: string;
  street: string;
  country: string;
  address: string;
  landmark?: string;
}

interface LocationInputProps {
  onSelectLocation: (
    location: LocationInputType,
    coordinates: { lat: number; lng: number }
  ) => void;
  defaultValue?: LocationInputType & {
    coordinates: { lat: number; lng: number };
  };
  className?: string;
  shouldDisplayCityAsLocationName?: boolean;
  hasCurrentLocationIcon?: boolean;
}

const LocationInput: React.FC<LocationInputProps> = ({
  onSelectLocation,
  defaultValue,
  className,
  shouldDisplayCityAsLocationName,
  hasCurrentLocationIcon = true,
}) => {
  const [inputValue, setInputValue] = useState<string>('');
  const [coordinates, setCoordinates] = useState<{ lat: number; lng: number }>(
    defaultValue?.coordinates || { lat: 0, lng: 0 }
  );
  const [locationName, setLocationName] = useState<string>(
    shouldDisplayCityAsLocationName
      ? (defaultValue?.city ?? '')
      : (defaultValue?.landmark ?? defaultValue?.address ?? '')
  );

  // When defaultValue changes, update input value and coordinates
  useEffect(() => {
    if (defaultValue) {
      setInputValue(locationName);
      setCoordinates(defaultValue.coordinates || { lat: 0, lng: 0 });
    }
  }, [defaultValue, locationName]);

  // Helper to parse address components
  const parseAddressComponents = (
    addressComponents: google.maps.GeocoderAddressComponent[]
  ) => {
    let city = '';
    let state = '';
    let street = '';
    let country = '';

    addressComponents.forEach((comp) => {
      if (comp.types.includes('locality')) city = comp.long_name;
      else if (comp.types.includes('administrative_area_level_1'))
        state = comp.long_name;
      else if (comp.types.includes('route')) street = comp.long_name;
      else if (comp.types.includes('country')) country = comp.long_name;
    });

    return { city, state, street, country };
  };

  // Handle place select from autocomplete dropdown
  const handleSelect = async (place: any) => {
    if (!place || !place.value || !place.value.place_id) return;

    try {
      const results = await geocodeByPlaceId(place.value.place_id);
      if (!results || !results[0]) return;

      const latLng = await getLatLng(results[0]);
      const { city, state, street, country } = parseAddressComponents(
        results[0].address_components
      );

      const landmark = results[0].formatted_address || '';
      const address = results[0].formatted_address || '';

      const locationObj: LocationInputType = {
        city: city || '',
        state: state || '',
        street: street || '',
        country: country || '',
        address,
        landmark,
      };

      setInputValue(place.label);
      setCoordinates(latLng);
      setLocationName(landmark || address);
      onSelectLocation(locationObj, latLng);
    } catch (error) {
      console.error('Error fetching geocode:', error);
    }
  };

  // Use browser geolocation for current location
  const handleUseCurrentLocation = () => {
    if (!navigator.geolocation) {
      alert('Geolocation is not supported by your browser');
      return;
    }

    navigator.geolocation.getCurrentPosition(
      async (position) => {
        const lat = position.coords.latitude;
        const lng = position.coords.longitude;

        try {
          const geocoder = new window.google.maps.Geocoder();
          const results = await new Promise<google.maps.GeocoderResult[]>(
            (resolve, reject) => {
              geocoder.geocode(
                { location: { lat, lng } },
                (
                  results: google.maps.GeocoderResult[] | null,
                  status: google.maps.GeocoderStatus
                ) => {
                  if (status === 'OK' && results) resolve(results);
                  else reject(status);
                }
              );
            }
          );

          if (!results || !results[0]) return;

          const { city, state, street, country } = parseAddressComponents(
            results[0].address_components
          );

          const landmark = results[0].formatted_address || '';
          const address = landmark;

          const locationObj: LocationInputType = {
            city,
            state,
            street,
            country,
            address,
            landmark,
          };

          setInputValue(landmark);
          setCoordinates({ lat, lng });
          setLocationName(landmark);
          onSelectLocation(locationObj, { lat, lng });
        } catch (error) {
          console.error('Reverse geocoding failed', error);
          alert('Failed to get address from your location');
        }
      },
      (error) => {
        alert('Unable to retrieve your location');
        console.error(error);
      }
    );
  };

  return (
    <div className={className}>
      <GooglePlacesAutocomplete
        apiKey={process.env.NEXT_PUBLIC_GOOGLE_MAPS_PLACES_API_KEY || ''}
        selectProps={{
          inputValue,
          onInputChange: (value) => setInputValue(value),
          onChange: handleSelect,
          placeholder: 'Search Location',
          styles: {
            input: (provided) => ({
              ...provided,
              color: 'inherit',
              height: 52,
              width: '100%',
              borderRadius: '0.375rem',
              borderWidth: '1px',
              borderColor: semanticColors.border.subtle.dark,
              backgroundColor: 'transparent',
              padding: '0.75rem',
              fontFamily: '"Aeonik", sans-serif',
              fontSize: '1rem',
              lineHeight: 1,
              fontWeight: 500,
              boxShadow: 'none',
            }),
            option: (provided) => ({
              ...provided,
              color: 'black',
              fontSize: 14,
            }),
          },
        }}
        autocompletionRequest={{
          componentRestrictions: { country: [] }, // optional: restrict country
          types: ['geocode'],
        }}
      />
      {hasCurrentLocationIcon && (
        <button
          type="button"
          onClick={handleUseCurrentLocation}
          aria-label="Use current location"
          className="ml-2 px-3 py-1 text-white rounded"
        >
          📍 Use Current Location
        </button>
      )}
    </div>
  );
};

export default LocationInput;
