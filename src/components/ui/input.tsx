
'use client';

import React, { useState, useMemo } from 'react';
import type {
  Control,
  FieldValues,
  Path,
  RegisterOptions,
} from 'react-hook-form';
import { useController, useFormContext } from 'react-hook-form';
import { tv } from 'tailwind-variants';
import { FiEye, FiEyeOff } from 'react-icons/fi';

const inputTv = tv({
  slots: {
    container: 'relative',
    label:
      'font-aeonik-regular text-base/100 font-medium text-fg-muted-light dark:text-fg-muted-dark absolute duration-300 transform -translate-y-4 scale-75 top-2 z-10 origin-[0] px-2 peer-focus:px-2 peer-focus:border-accent-moderate peer-focus:dark:border-accent-moderate peer-placeholder-shown:scale-100 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:top-1/2 peer-focus:top-2 peer-focus:scale-75 peer-focus:-translate-y-4 rtl:peer-focus:translate-x-1/4 rtl:peer-focus:left-auto start-1',
    input:
      'h-[52px] w-full rounded-md border border-border-subtle-light bg-transparent p-3 font-aeonik-regular text-base/100 font-medium shadow-none dark:border-border-subtle-dark dark:text-white',
    iconContainer:
      'absolute left-3 top-3.5 text-accent-moderate dark:text-accent-moderate',
  },

  variants: {
    focused: {
      true: {
        input:
          'border border-accent-moderate pb-2 pt-5 dark:border-accent-moderate',
        iconContainer: '',
      },
    },
    filled: {
      true: {
        input: 'pb-2 pt-5',
        iconContainer: '',
      },
    },
    error: {
      true: {
        input: 'border-2 border-red-60 dark:border-red-80',
        label: 'dark:text-danger-80 text-red-80',
        iconContainer: '',
      },
    },
    disabled: {
      true: {
        input: 'inherit',
        iconContainer: '',
      },
    },
  },
  defaultVariants: {
    focused: false,
    filled: false,
    error: false,
    disabled: false,
  },
});

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement | HTMLTextAreaElement> {
  label?: string;
  disabled?: boolean;
  error?: string;
  isPassword?: boolean;
  icon?: any;
  iconClassName?: string;
  containerClassName?: string;
  inputClassName?: string;
  focusedInputClassName?: string;
  handleFieldBlur?: () => void;
  handleFieldUnBlur?: () => void;
  hideErrorMessage?: boolean;
  multiline?: boolean;
}

type TRule<T extends FieldValues> =
  | Omit<
      RegisterOptions<T>,
      'disabled' | 'valueAsNumber' | 'valueAsDate' | 'setValueAs'
    >
  | undefined;

export type RuleType<T extends FieldValues> = { [name in keyof T]: TRule<T> };
export type InputControllerType<T extends FieldValues> = {
  name: Path<T>;
  control: Control<T>;
  rules?: RegisterOptions<T, Path<T>>;
};

interface ControlledInputProps<T extends FieldValues>
  extends Omit<InputProps, 'name'>,
    InputControllerType<T> {}

export const Input = React.forwardRef<
  HTMLInputElement | HTMLTextAreaElement,
  InputProps
>(
  (
    {
      label,
      error,
      isPassword,
      icon,
      hideErrorMessage,
      iconClassName = '',
      containerClassName = '',
      inputClassName = '',
      focusedInputClassName = '',
      value,
      onBlur,
      handleFieldBlur,
      handleFieldUnBlur,
      multiline,
      ...props
    },
    ref
  ) => {
    const [showPassword, setShowPassword] = useState(!isPassword);
    

    const [isFocused, setIsFocused] = useState(false);
    const [isFilled, setIsFilled] = useState(!!value);

    const handleValueChange = (e: any) => {
      setIsFilled(!!e.target.value);
      props.onChange?.(e);
    };

    const handleFocus = (
      e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>
    ) => {
      setIsFocused(true);
      props.onFocus?.(e);
    };

    const handleBlur = (
      e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>
    ) => {
      setIsFocused(false);
      onBlur?.(e);
    };

    const styles = useMemo(
      () =>
        inputTv({
          focused: isFocused,
          filled: isFilled,
          error: !!error,
          disabled: props.disabled,
        }),
      [isFocused, isFilled, error, props.disabled]
    );

    const Element = multiline ? 'textarea' : 'input';

    return (
      <div className={styles.container()}>
        <div className="relative">
          {icon && <div className={styles.iconContainer()}>{icon}</div>}
          <Element
            {...props}
            ref={ref as any}
            id={props.id}
            type={
              isPassword
                ? showPassword
                  ? 'text'
                  : 'password'
                : (props.type ?? 'text')
            }
            className={`${styles.input()} peer`}
            placeholder=""
            onChange={handleValueChange}
            onFocus={handleFocus}
            onBlur={handleBlur}
          />
          {label && (
          <label htmlFor={props.id} className={styles.label()}>
            {label}
          </label>
        )}
          {isPassword && (
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-3 top-3.5 text-gray-500 dark:text-gray-400"
            >
              {showPassword ? <FiEye /> : <FiEyeOff />}
            </button>
          )}
        </div>
        {error && !hideErrorMessage && (
          <p className="text-red-500 dark:text-red-600 text-sm mt-1">{error}</p>
        )}
      </div>
    );
  }
);

export const ControlledInput = <T extends FieldValues>(
  props: ControlledInputProps<T>
) => {
  const { control: contextControl } = useFormContext<T>();
  const {
    name,
    control = contextControl,
    rules,
    onChange: propsOnChange,
    ...rest
  } = props;

  const { field, fieldState } = useController({ name, control, rules });

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    field.onChange(e);
    if (propsOnChange) propsOnChange(e as any);
  };

  return (
    <Input
      {...(rest as React.InputHTMLAttributes<HTMLInputElement>)}
      ref={field.ref}
      value={field.value ?? ''}
      onChange={handleChange}
      onBlur={field.onBlur}
      error={props.hideErrorMessage ? undefined : fieldState.error?.message}
    />
  );
};
