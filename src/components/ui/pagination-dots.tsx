import React from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';

interface PaginationDotsProps {
  total: number;
  activeIndex: number;
  onDotClick?: (index: number) => void;
  className?: string;
  dotClassName?: string;
  renderDot?: (index: number, isActive: boolean) => React.ReactNode;
}

export const PaginationDots: React.FC<PaginationDotsProps> = ({
  total,
  activeIndex,
  onDotClick,
  className,
  dotClassName,
  renderDot,
}) => {
  return (
    <div className={cn('flex items-center gap-2', className)}>
      {Array.from({ length: total }).map((_, i) => {
        const isActive = i === activeIndex;

        if (renderDot) return renderDot(i, isActive);

        return (
          <Button
            key={i}
            onClick={() => onDotClick?.(i)}
            className={cn(
              'size-2 bg-bg-interactive-primary-light dark:bg-bg-interactive-primary-dark p-0',
              isActive && 'bg-accent-moderate dark:bg-accent-moderate',
              dotClassName
            )}
            aria-label={`Go to page ${i + 1}`}
            disabled={!onDotClick}
          />
        );
      })}
    </div>
  );
};
