import countries from '@/lib/constants/country-flag';
import { Button } from '@/components/button/button';
import { cn } from '@/lib/utils';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Popover,
  PopoverContentWithoutPortal,
  PopoverTrigger,
} from '@/components/ui/popover';
import { CaretSortIcon, CheckIcon } from '@radix-ui/react-icons';
import * as React from 'react';

interface Props {
  defaultValue: string;
  onSelect: any;
}

interface CountryProp {
  code: string;
  unicode: string;
  name: string;
  emoji: string;
}

export function CountrySelector({ defaultValue, onSelect }: Props) {
  const [open, setOpen] = React.useState(false);
  const [value, setValue] = React.useState(defaultValue);

  const selected = Object.values(countries).find(
    (country) => country.code === value
  );

  const country_code = Object.keys(countries);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant='outline'
          role='combobox'
          aria-expanded={open}
          className='w-full bg-transparent rounded-lg justify-between font-normal truncate'
        >
          {value ? (
            <div className='flex space-x-2'>
              <span className=''>{selected?.emoji}</span>
              <span>{selected?.name}</span>
            </div>
          ) : (
            'Select country'
          )}
          <CaretSortIcon className='ml-2 h-4 w-4 shrink-0 opacity-50' />
        </Button>
      </PopoverTrigger>
      <PopoverContentWithoutPortal className='w-full p-0'>
        <Command>
          <CommandList>
            <ScrollArea className='max-h-72 w-full bg-gray-300'>
              <CommandInput
                placeholder='Search country...'
                className='h-9 px-2'
              />
              <CommandEmpty>No country found.</CommandEmpty>
              <CommandGroup className='overflow-y-auto max-h-[230px] pt-2'>
                {country_code?.map((code: string) => {
                  // console.log('code===', code);
                  const country = countries[code];
                  // console.log('country', country);
                  return (
                    <CommandItem
                      className='cursor-pointer'
                      key={country.code}
                      value={country.name}
                      onSelect={() => {
                        setValue(country.code);
                        onSelect(country.name);
                        setOpen(false);
                      }}
                    >
                      {country.name}
                      <CheckIcon
                        className={cn(
                          'ml-auto h-4 w-4',
                          value === country.code ? 'opacity-100' : 'opacity-0'
                        )}
                      />
                    </CommandItem>
                  );
                })}
              </CommandGroup>
            </ScrollArea>
          </CommandList>
        </Command>
      </PopoverContentWithoutPortal>
    </Popover>
  );
}
