import Image from 'next/image';
import { cn } from '@/lib';

interface HomeCompletionCardProps {
  title: string;
  imageSource: string;
  onClick?: () => void;
  className?: string;
}

export function HomeCompletionCard({
  title,
  onClick,
  imageSource,
  className,
}: HomeCompletionCardProps) {
  return (
    <button
      onClick={onClick}
      className={cn(
        'relative h-[100px] w-[136px] flex flex-col items-center justify-center gap-y-2 rounded-lg bg-bg-subtle-light p-2 dark:bg-bg-subtle-dark',
        className
      )}
      type="button"
    >
      <Image
        src={imageSource}
        alt={title}
        width={48}
        height={48}
        className="self-center"
      />
      <div className="w-full text-left">
        <h6 className="text-base font-semibold">{title}</h6>
      </div>
    </button>
  );
}