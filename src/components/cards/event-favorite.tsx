import { useState } from 'react';
import Link from 'next/link';
import { FaRegCalendarAlt } from 'react-icons/fa';
import { IoLocationOutline } from 'react-icons/io5';
import { GoHeart, GoHeartFill } from 'react-icons/go';
import dayjs from 'dayjs';

import { EventFormat, type IEvent } from '@/api/events';
import { useFavoriteToggle } from '@/lib';
import { ConfirmationDialog } from '../dialogs';

interface EventFavoriteCardProps extends IEvent {
  attendees: number;
  isFavoriteTab?: boolean;
}

export const EventFavoriteCard: React.FC<EventFavoriteCardProps> = ({
  title,
  bannerUrl,
  startTime,
  location,
  id,
  slug,
  eventFormat,
  onlineEventUrl,
  isfavourite,
  isFavoriteTab,
  status,
}) => {
  const { favourited, handleFavToggle } = useFavoriteToggle({
    initialFavoriteState: isfavourite || false,
    payload: { type: 'EVENT', accountId: null, eventId: id },
  });

  const [confirmVisible, setConfirmVisible] = useState(false);

  const hasPhysicalLocation = eventFormat !== EventFormat.ONLINE;
  const eventAddress = hasPhysicalLocation
    ? location.landmark ||
      location.address.replace(/^CCQH\+9HP\s*,?\s*/, '').trim()
    : onlineEventUrl || 'Online';

  return (
    <Link href={`/events/${id}?slug=${slug}`} className='block'>
      <div className='h-[152px] flex gap-4 rounded-lg bg-gray-100 p-4 dark:bg-gray-900'>
        <img
          src={bannerUrl}
          alt={title}
          className='w-[120px] h-[120px] rounded-lg object-cover'
        />

        <div className='flex flex-1 flex-col gap-2'>
          <div className='flex items-center justify-between gap-2'>
            <h3 className='font-bold text-lg truncate'>{title}</h3>
            <button
              onClick={(e) => {
                e.preventDefault(); // prevent link click
                isFavoriteTab ? setConfirmVisible(true) : handleFavToggle();
              }}
              className='shrink-0'
            >
              {favourited ? (
                <GoHeartFill size={24} className='text-red-500' />
              ) : (
                <GoHeart size={24} className='text-pink-600' />
              )}
            </button>
          </div>

          <div className='flex flex-col gap-2'>
            <div className='flex items-center gap-1 border border-pink-300 rounded-full bg-white px-2 py-1 dark:border-pink-800 dark:bg-gray-800'>
              <FaRegCalendarAlt size={16} className='text-pink-600' />
              <span className='text-pink-700 dark:text-pink-400'>
                {dayjs(startTime).format('D MMM')}
              </span>
            </div>

            <div className='flex items-center gap-1 self-start border border-pink-300 rounded-full bg-white px-2 py-1 dark:border-pink-800 dark:bg-gray-800'>
              <IoLocationOutline size={16} className='text-pink-600' />
              <span className='truncate text-pink-700 dark:text-pink-400'>
                {eventAddress}
              </span>
            </div>

            {status === 'DRAFT' && (
              <div className='px-2 py-1 rounded-full bg-yellow-100 dark:bg-yellow-600 text-sm'>
                Pending
              </div>
            )}
          </div>
        </div>

        <ConfirmationDialog
          visible={confirmVisible}
          message='Are you sure you want to remove this event from your favourites?'
          onCancel={() => setConfirmVisible(false)}
          onConfirm={() => {
            setConfirmVisible(false);
            handleFavToggle();
          }}
        />
      </div>
    </Link>
  );
};
