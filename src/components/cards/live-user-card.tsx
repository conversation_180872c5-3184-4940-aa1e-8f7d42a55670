import Image from 'next/image';
import { cn } from '@/lib';
import { LiveUserAvatar } from '../avatars/live-avatar';

interface LiveUserCardProps {
  username: string;
  avatar: string;
  status: string;
  bgImage: string;
  className?: string;
  onPress?: () => void;
}

export const LiveUserCard: React.FC<LiveUserCardProps> = ({
  username,
  avatar,
  status,
  bgImage,
  className,
  onPress,
}) => {
  return (
    <div
      className={cn(
        'relative h-[200px] w-[150px] rounded-lg flex items-center',
        className
      )}
    >
      <Image
        src={bgImage}
        alt={`${username} background`}
        fill
        className='object-cover rounded-lg'
      />

      <div className='absolute top-2 w-full flex flex-col gap-8'>
        <div className='ml-auto mr-2 w-10 flex items-center justify-center gap-0.5 rounded-md bg-red-50 px-1 py-0.5'>
          <div className='w-1 h-1 rounded-full bg-white' />
          <span className='text-[10px] font-bold text-white'>{status}</span>
        </div>

        <LiveUserAvatar
          username={username}
          avatar={avatar}
          hideStatusPill
          textClassName='text-white'
          onPress={onPress}
        />
      </div>
    </div>
  );
};
