import { Icons } from '@/components/icons/icons';
import moment from 'moment';
import Link from 'next/link';
import React from 'react';
import { MdDateRange, MdLocationOn } from 'react-icons/md';

type EventCardProps = {
  image: string;
  title: string;
  date: string;
  location: string;
  slug: string
  id: string
  updateFavourite?: (id: string, isfavourite: boolean) => void;
  owner?: boolean;
  endTime?: string;
  status?: string;
  isTicketed?: boolean;
  isfavourite?: boolean;
};

const EventCard: React.FC<EventCardProps> = ({ 
  id, 
  image, 
  title, 
  date, 
  location, 
  isTicketed, 
  isfavourite, 
  slug, 
  owner=false, 
  updateFavourite,
  endTime,
  status
}) => {

  return (
    <div className="overflow-hidden">
      <Link href={`/event/${slug}`}>
      <div className="relative w-full h-auto flex flex-1 flex-col object-cover rounded-lg bg-gray-300 bg-cover bg-center"
          style={{ aspectRatio: '8/10',  backgroundImage: `url(${image}?${new Date().getTime()})` }}>
          <div className='flex flex-1'>
          {!owner ? (
              <>
              {!isTicketed && (
                  <div className='flex absolute right-[15px] top-[11px] gap-2 px-[12px] py-[6px] rounded-[50px] justify-center items-center bg-[rgba(0,0,0,0.25)]'>
                    <Icons.freeIcon className='w-[18px] h-[18px]' />
                    <p className=' text-[12px] text-[#FDFDFD]'>Free</p>
                  </div>
                )}
              </>
            ): (
              <div className=' absolute flex right-[17px] justify-center items-center top-[17px] gap-[2px] text-[10px] text-[#FFFFFF] bg-[rgba(0,0,0,0.25)] z-50 p-[2px] rounded-sm'>
                {
                  status === "DRAFT" ? (
                    <>
                      <div className=' h-[8px] w-[8px] bg-[#FFA800] rounded-full' />
                      <p>Pending</p>
                    </>
                  ) : moment(endTime).isAfter(moment()) ? (
                    <>
                      <div className=' h-[8px] w-[8px] bg-[#34F36A] rounded-full' />
                      <p>Active</p>
                    </>
                  ): (
                    <>
                      <div className=' h-[8px] w-[8px] bg-[#EC0E0E] rounded-full' />
                      <p>Expired</p>
                    </>
                  )
                }
              </div>
            )}
          </div>
          <div className='flex flex-1 justify-end items-end'>
            {!owner && (
              <div onClick={(e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
                e.preventDefault(); // Prevent the default navigation of the Link
                if(updateFavourite){
                  updateFavourite(id, !!isfavourite)
                }
              }} className='cursor-pointer flex mr-[15px] mb-[20px] rounded-full justify-center items-center h-[24px] w-[24px] hover:bg-[rgba(255,255,255,0.2)] bg-[rgba(26,26,26,0.5)]'>
                {isfavourite ? <Icons.heart className='w-[15px] h-[15px]' /> : <Icons.heartLine className='w-[15px] h-[15px]' />}
              </div>
            )}
          </div>
      </div>
      </Link>
      <div className="px-1">
        <div className='text-[#1B1B1B] font-medium text-[14px]'>{title}</div>
        <div className='flex items-center gap-1 text-[10px] text-[#3F3F46]'>
          <MdDateRange className='icon' color='#3F3F46' />
          <span>{moment.utc(date).format("D MMM [at] HH:mm")}</span>
        </div>
        <div className='flex items-center text-[10px] gap-1 text-[#3F3F46]'>
          <MdLocationOn className='icon' color='#3F3F46' />
          <span>{location}</span>
        </div>
      </div>
    </div>
  );
};

export default EventCard;
