import { MapPin } from 'lucide-react';
import { format, isPast, parseISO } from 'date-fns';
import Link from 'next/link';
import Image from 'next/image';
import React from 'react';

import { type IEvent } from '@/api/events';

interface EventCardWithActionProps
  extends Partial<IEvent> {
  attendees: number;
  id: string;
}

export const EventCardWithAction: React.FC<EventCardWithActionProps> = ({
  bannerUrl,
  title,
  startTime,
  endTime,
  id,
  slug,
  location,
}) => {
  const date = parseISO(startTime || '');
  const month = format(date, 'MMM');
  const day = format(date, 'd');
  const isEventInThePast = endTime ? isPast(endTime) : true;

  return (
    <Link
      href={{
        pathname: '/events/[id]',
        query: { id, slug },
      }}
      className="flex items-center gap-x-2 rounded-md bg-grey-30 pr-3 dark:bg-grey-80"
    >
      <Image
        src={bannerUrl || '/fallback-image.jpg'}
        width={136}
        height={136}
        alt={title || 'Event Banner'}
        className="rounded-l-md object-cover"
      />
      <div className="flex flex-1 flex-col gap-y-2">
        <h3 className="text-lg font-bold">{title}</h3>
        <div className="flex items-center gap-x-0.5 py-1">
          <MapPin size={10} className="text-black dark:text-white" />
          <span className="text-sm">{location?.landmark}</span>
        </div>
        {!isEventInThePast && (
          <Link
            href={{
              pathname: '/events/[id]',
              query: { id, slug },
            }}
            className="inline-flex h-7 w-24 items-center justify-center rounded bg-brand-60 text-xs font-bold text-white"
          >
            Get Tickets
          </Link>
        )}
      </div>
      <div className="flex size-14 flex-col items-center justify-center gap-1 rounded-sm bg-brand-60 px-2 py-1">
        <h3 className="text-grey-100 dark:text-white">
          {month.toUpperCase()}
        </h3>
        <h3 className="text-grey-100 dark:text-white">{day}</h3>
      </div>
    </Link>
  );
};