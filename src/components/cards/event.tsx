import { Calendar, Heart, MapPin } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import React from 'react';

import { type IEventLocation } from '@/api/events';
import { useFavoriteToggle } from '@/lib';

interface EventCardProps {
  image: string;
  title: string;
  date: string;
  location?: IEventLocation;
  id: string;
  slug: string;
  onlineEventUrl?: string;
  hasPhysicalLocation: boolean;
  isfavourite?: boolean;
}

export const EventCard: React.FC<EventCardProps> = ({
  image,
  title,
  date,
  location,
  id,
  slug,
  hasPhysicalLocation,
  onlineEventUrl,
  isfavourite,
}) => {
  const locationText = hasPhysicalLocation
    ? location?.landmark ||
      location?.address.replace(/^CCQH\+9HP\s*,?\s*/, '').trim()
    : onlineEventUrl || 'Online';

  const { favourited, handleFavToggle } = useFavoriteToggle({
    initialFavoriteState: isfavourite || false,
    payload: { type: 'EVENT', accountId: null, eventId: id },
  });

  return (
    <Link
      href={{
        pathname: '/events/[id]',
        query: { id, slug },
      }}
      className="relative block h-[278px] rounded-lg overflow-hidden"
    >
      {/* Event Image */}
      <Image
        src={image}
        alt={title}
        fill
        className="object-cover rounded-lg"
      />

      {/* Overlay */}
      <div className="absolute inset-0 flex flex-col justify-between p-3">
        {/* Favourite button */}
        <button
          type="button"
          onClick={(e) => {
            e.preventDefault(); // Prevent Link navigation
            handleFavToggle();
          }}
          className="flex items-center justify-center w-8 h-8 rounded-full bg-brand-20 dark:bg-brand-90"
        >
          <Heart
            size={16}
            className={
              favourited ? 'fill-red-500 text-red-500' : 'text-brand-40'
            }
          />
        </button>

        {/* Title & Details */}
        <div className="rounded-lg bg-black/40 backdrop-blur-md p-4">
          <h2 className="text-white font-bold truncate">{title}</h2>
          <div className="flex gap-4 mt-2">
            <div className="flex items-center gap-1 text-white text-sm">
              <Calendar size={16} />
              <span>{date}</span>
            </div>
            <div className="flex items-center gap-1 text-white text-sm truncate">
              <MapPin size={16} />
              <span className="truncate">{locationText}</span>
            </div>
          </div>
        </div>
      </div>
    </Link>
  );
};