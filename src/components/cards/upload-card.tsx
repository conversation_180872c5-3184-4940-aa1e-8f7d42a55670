import Image from 'next/image';
import { cn } from '@/lib/utils';
import { semanticColors } from '@/components/ui';

interface UploadCardProps {
  onClick: () => void;
  icon: string;
  title: string;
  className?: string;
}

export const UploadCard: React.FC<UploadCardProps> = ({
  onClick,
  icon,
  title,
  className,
}) => {
  return (
    <button
      onClick={onClick}
      type="button"
      className={cn(
        'h-[360px] w-full flex flex-col items-center justify-center p-6',
        'border-2 border-dashed rounded-lg',
        'bg-bg-subtle-light dark:bg-bg-subtle-dark',
        className
      )}
      style={{
        borderColor: semanticColors.border.subtle.dark,
        backgroundColor: semanticColors.bg.subtle.dark,
      }}
    >
      <div className="flex flex-col items-center justify-center gap-4">
        <Image
          src={icon}
          alt={title}
          width={64}
          height={64}
          className="object-contain"
        />

        <p className="text-center text-accent-on-accent dark:text-accent-on-accent">
          {title}
        </p>

        <div className="flex flex-wrap justify-center">
          <span className="mx-1 text-xs text-fg-muted-light dark:text-fg-muted-dark">
            Supported Format: JPG, PNG (Max 10mb)
          </span>
        </div>
      </div>
    </button>
  );
};