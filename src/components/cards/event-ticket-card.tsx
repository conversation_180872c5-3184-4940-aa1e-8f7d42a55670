import { MoreHorizontal } from 'lucide-react';
import React from 'react';
import Image from 'next/image';

import { type TicketCategories } from '@/api/events';
import { formatCurrency } from '@/lib/utils/blanket';

type TicketCategory = TicketCategories[keyof TicketCategories];

type EventTicketCardProps = {
  ticket: TicketCategory;
  index: number;
  category: string;
  isMenuOpen: boolean;
  setMenuVisible: (id: string | number | null) => void;
  onEdit: () => void;
  onDelete?: () => void;
  isDark: boolean;
};

function PresaleBadge() {
  return (
    <div className="absolute right-2 top-1 z-10 h-5 w-[84px] flex items-center justify-center rounded-full bg-green-10 dark:bg-green-80">
      <span className="text-xs font-bold text-green-60 dark:text-green-40">
        Presale incl.
      </span>
    </div>
  );
}

export function EventTicketCard({
  ticket,
  index,
  category,
  isMenuOpen,
  setMenuVisible,
  onEdit,
  onDelete,
  isDark,
}: EventTicketCardProps) {
  return (
    <div className="relative h-[98px] flex items-center justify-between rounded-md bg-bg-subtle-light p-3 dark:bg-bg-subtle-dark">
      <div className="flex flex-1 items-center gap-2">
        <Image
          src="/assets/images/session/disc.png"
          width={40}
          height={40}
          alt="Ticket"
          className="size-10"
        />
        <div className="gap-2 flex flex-col">
          <span className="font-bold text-fg-base-light dark:text-fg-base-dark">
            {category}
          </span>
          <span className="text-fg-subtle-light dark:text-fg-subtle-dark">
            {formatCurrency(ticket.cost)}
          </span>
          {ticket.description && (
            <span className="text-fg-subtle-light dark:text-fg-subtle-dark">
              {ticket.description.length > 30
                ? ticket.description.slice(0, 30) + '...'
                : ticket.description}
            </span>
          )}
        </div>
      </div>
      {ticket.hasPresale && <PresaleBadge />}
      <div className="relative flex items-center gap-2">
        <button
          onClick={() => setMenuVisible(index)}
          aria-label="Open ticket options"
        >
          <MoreHorizontal
            size={24}
            className={isDark ? 'text-white' : 'text-gray-100'}
          />
        </button>

        {isMenuOpen && (
          <>
            <div
              className="z-9999 absolute inset-0"
              onClick={() => setMenuVisible(null)}
            />
            <div className="z-60 absolute right-0 top-8 w-[120px] rounded-md bg-bg-interactive-secondary-light p-2 shadow-md dark:bg-bg-interactive-secondary-light">
              <button
                onClick={onEdit}
                className="py-2 w-full text-left"
                aria-label="Edit ticket"
              >
                <span className="text-fg-base-light dark:text-fg-base-light">
                  Edit ticket
                </span>
              </button>
              {onDelete && (
                <button
                  onClick={onDelete}
                  className="py-2 w-full text-left"
                  aria-label="Delete ticket"
                >
                  <span className="text-fg-danger-light dark:text-fg-danger-light">
                    Delete ticket
                  </span>
                </button>
              )}
            </div>
          </>
        )}
      </div>
    </div>
  );
}