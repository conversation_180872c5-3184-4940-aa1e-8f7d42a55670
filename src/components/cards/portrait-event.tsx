import { Calendar, Heart, MapPin } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import moment from 'moment';
import React from 'react';

import { type IEvent } from '@/api/events';
import { cn, useFavoriteToggle } from '@/lib';

interface PortraitEventCardProps extends IEvent {
  isEven: boolean;
  hasPhysicalLocation: boolean;
}

export const PortraitEventCard: React.FC<PortraitEventCardProps> = ({
  bannerUrl,
  title,
  startTime,
  location,
  isEven,
  id,
  slug,
  isfavourite,
  hasPhysicalLocation,
  onlineEventUrl,
}) => {
  const { favourited, handleFavToggle } = useFavoriteToggle({
    initialFavoriteState: isfavourite,
    payload: { type: 'EVENT', accountId: null, eventId: id },
  });

  const locationText = hasPhysicalLocation
    ? location.landmark ||
      location.address.replace(/^CCQH\+9HP\s*,?\s*/, '').trim()
    : onlineEventUrl || 'Online';

  return (
    <Link
      href={{
        pathname: '/events/[id]',
        query: { id, slug },
      }}
      className={cn(
        'relative h-[300px] flex-1 rounded-lg overflow-hidden group',
        isEven ? 'mr-2' : 'ml-2'
      )}
    >
      {/* Event banner */}
      <Image
        src={bannerUrl}
        alt={title}
        fill
        className="object-cover rounded-lg"
      />

      {/* Top right favourite button */}
      <div className="absolute top-2 right-2 flex justify-between items-end w-full p-2">
        <button
          type="button"
          onClick={(e) => {
            e.preventDefault();
            handleFavToggle();
          }}
          className="flex items-center justify-center w-8 h-8 rounded-full bg-brand-20 dark:bg-brand-90"
        >
          <Heart
            size={16}
            className={
              favourited
                ? 'fill-red-500 text-red-500'
                : 'text-brand-40'
            }
          />
        </button>
      </div>

      {/* Bottom overlay with blur */}
      <div className="absolute bottom-0 left-0 right-0 p-4 bg-black/40 backdrop-blur-md rounded-b-lg">
        <h4 className="text-white font-bold">{title}</h4>
        <div className="flex flex-col gap-2 mt-2">
          <div className="flex items-center gap-1 text-white text-sm">
            <Calendar size={16} />
            <span>{moment.utc(startTime).format('D MMM YYYY [at] HH:mm')}</span>
          </div>
          <div className="flex items-center gap-1 text-white text-sm">
            <MapPin size={16} />
            <span className="truncate">{locationText}</span>
          </div>
        </div>
      </div>
    </Link>
  );
};