import Image from 'next/image';
import Link from 'next/link';
import React from 'react';
import { Heart } from 'lucide-react';

import { cn, useAccountFavourite } from '@/lib';
import { ConfirmationDialog } from '@/components/dialogs';

interface FavouriteAccountCardProps {
  image: string;
  fullname: string;
  username: string;
  id: string;
}

export const FavouriteAccountCard: React.FC<FavouriteAccountCardProps> = ({
  image,
  fullname,
  username,
  id,
}) => {
  const { toggleFavourite } = useAccountFavourite(id);
  const [confirmVisible, setConfirmVisible] = React.useState(false);

  return (
    <div
      className={cn(
        'h-16 flex-1 flex flex-row gap-3.5 items-center cursor-pointer'
      )}
    >
      {/* User profile image */}
      <Link
        href={{
          pathname: '/users/[id]',
          query: { id },
        }}
        className="flex flex-row flex-1 gap-3.5 items-center"
      >
        <Image
          src={image}
          alt={fullname}
          width={40}
          height={40}
          className="rounded-full object-cover"
        />
        <div className="flex flex-col flex-1 gap-1">
          <span className="font-bold truncate">{fullname}</span>
          <span className="text-sm truncate">{username}</span>
        </div>
      </Link>

      {/* Favourite icon */}
      <button
        type="button"
        className="size-10 flex items-center justify-center"
        onClick={() => setConfirmVisible(true)}
      >
        <Heart className="fill-red-500 text-red-500" size={18} />
      </button>

      {/* Confirmation modal */}
      <ConfirmationDialog
        visible={confirmVisible}
        message="Are you sure you want to remove this account from your favourites?"
        onCancel={() => setConfirmVisible(false)}
        onConfirm={() => {
          setConfirmVisible(false);
          toggleFavourite();
        }}
      />
    </div>
  );
};