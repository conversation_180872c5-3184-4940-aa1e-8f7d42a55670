'use client';

import React from 'react';
import Image from 'next/image';

interface CompleteCreatorOnboardingCardProps {
  title: string;
  description: string;
  imageSource: string;
  onPress?: () => void;
}

export function CompletionCard({
  title,
  description,
  onPress,
  imageSource,
}: CompleteCreatorOnboardingCardProps) {
  return (
    <button
      onClick={onPress}
      className="relative w-full rounded-lg bg-white px-4 py-3 dark:bg-gray-100 text-left"
      type="button"
    >
      <CardContent
        imageSource={imageSource}
        title={title}
        description={description}
      />
    </button>
  );
}

interface CardContentProps {
  title: string;
  description: string;
  imageSource: string;
}

const CardContent = ({ imageSource, title, description }: CardContentProps) => (
  <div className="flex w-full flex-row gap-4">
    <div className="my-auto w-10 h-10 flex items-center justify-center border border-gray-300 dark:border-gray-600 rounded">
      <Image
        src={imageSource}
        alt={title}
        width={40}
        height={40}
        className="object-cover rounded"
      />
    </div>
    <div className="w-full max-w-[273px] flex flex-col gap-2">
      <strong className="font-bold text-base">{title}</strong>
      <small className="text-gray-600 dark:text-gray-500">{description}</small>
    </div>
  </div>
);