import { getAmountValueInMajor } from '@/lib/utils/blanket';
import React from 'react';
import {
  AreaChart,
  CartesianGrid,
  XAxis,
  YAxis,
  Tooltip,
  ResponsiveContainer,
  Area,
} from 'recharts';

interface LineChartProps {
  data: { month: string; amount: number }[];
}

export const LineChart: React.FC<LineChartProps> = ({ data }) => {
  // Function to format numbers with commas
  const formatNumber = (value: number) => {
    return getAmountValueInMajor(value).toLocaleString('en-NG', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });
  };

  return (
    <ResponsiveContainer width='100%' height={220}>
      <AreaChart
        width={500}
        height={200}
        data={data}
        syncId='anyId'
        margin={{
          top: 10,
          right: 30,
          left: 20,
          bottom: 0,
        }}
      >
        <CartesianGrid strokeDasharray='3 3' />
        <XAxis
          dataKey='month'
          tick={{ fill: '#718EBF' }}
          axisLine={false}
          fontSize={12}
        />
        <YAxis
          tick={{ fill: '#718EBF' }}
          tickFormatter={formatNumber} // Format the Y-axis values with commas
          domain={[0, 'dataMax + 200']}
          interval='preserveStartEnd'
          axisLine={false}
          fontSize={12}
        />
        <Tooltip
          formatter={(value: number) => formatNumber(value)} // Format tooltip values with commas
        />
        <Area
          type='monotone'
          dataKey='amount'
          stroke='#6600CC'
          strokeWidth={2}
          fill='#2D60FF40'
        />
      </AreaChart>
    </ResponsiveContainer>
  );
};
