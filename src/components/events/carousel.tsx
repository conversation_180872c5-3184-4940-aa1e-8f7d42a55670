'use client';

import { useState, useEffect, useCallback } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
  type CarouselApi,
} from '@/components/ui/carousel';
import { cn } from '@/lib/utils';
import { H3, Tiny } from '@/components/ui/typography';
import NextImage from '@/components/ui/NextImage';

interface EventData {
  id: string;
  title: string;
  artist: string;
  attending: string;
  date: {
    month: string;
    day: string;
  };
  image: string;
  gradient: string;
}

interface EventCarouselProps {
  events?: EventData[];
  className?: string;
  autoPlay?: boolean;
  autoPlayInterval?: number;
}

const defaultEvents: EventData[] = [
  {
    id: '1',
    title: 'Wizkid Live',
    artist: 'Wizkid',
    attending: '60,000 attending',
    date: { month: 'MAY', day: '23' },
    image: '/placeholder.svg?height=200&width=200',
    gradient: 'from-purple-600 via-purple-500 to-pink-500',
  },
  {
    id: '2',
    title: 'Drake World Tour',
    artist: 'Drake',
    attending: '85,000 attending',
    date: { month: 'JUN', day: '15' },
    image: '/placeholder.svg?height=200&width=200',
    gradient: 'from-blue-600 via-blue-500 to-cyan-500',
  },
  {
    id: '3',
    title: 'Taylor Swift Eras',
    artist: 'Taylor Swift',
    attending: '70,000 attending',
    date: { month: 'JUL', day: '08' },
    image: '/placeholder.svg?height=200&width=200',
    gradient: 'from-pink-600 via-rose-500 to-orange-500',
  },
  {
    id: '4',
    title: 'The Weeknd Live',
    artist: 'The Weeknd',
    attending: '55,000 attending',
    date: { month: 'AUG', day: '12' },
    image: '/placeholder.svg?height=200&width=200',
    gradient: 'from-red-600 via-red-500 to-pink-500',
  },
];

export default function EventCarousel({
  events = defaultEvents,
  className = '',
  autoPlay = true,
  autoPlayInterval = 4000,
}: EventCarouselProps) {
  const [api, setApi] = useState<CarouselApi>();
  const [current, setCurrent] = useState(0);
  const [count, setCount] = useState(0);
  const [isHovered, setIsHovered] = useState(false);

  useEffect(() => {
    if (!api) return;

    setCount(api.scrollSnapList().length);
    setCurrent(api.selectedScrollSnap());

    const onSelect = () => {
      setCurrent(api.selectedScrollSnap());
    };

    api.on('select', onSelect);

    return () => {
      api.off('select', onSelect);
    };
  }, [api]);

  useEffect(() => {
    if (!api || !autoPlay || isHovered) return;

    const interval = setInterval(() => {
      if (current === count - 1) {
        api.scrollTo(0);
      } else {
        api.scrollNext();
      }
    }, autoPlayInterval);

    return () => clearInterval(interval);
  }, [api, autoPlay, autoPlayInterval, current, count, isHovered]);

  const handleDotClick = useCallback(
    (index: number) => {
      if (api) {
        api.scrollTo(index);
      }
    },
    [api]
  );

  return (
    <div
      className={cn('mx-auto py-2', className)}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <Carousel
        className='max-w-72 lg:max-w-[350px] w-full mx-auto'
        setApi={setApi}
        opts={{
          align: 'center',
          loop: true,
        }}
      >
        <CarouselContent className='-ml-2 md:-ml-4'>
          {events.map((event) => (
            <CarouselItem key={event.id} className='pl-2 md:pl-4'>
              <Card className='border-none shadow-none flex flex-1 min-h-[112px] rounded-lg bg-bg-disabled-light dark:bg-bg-disabled-dark pr-2'>
                <CardContent className='p-0 flex flex-1 items-center gap-6'>
                  <NextImage
                    src='/images/artists/wizkid.png'
                    alt='Wizkid in an Event'
                    width={130}
                    height={112}
                    className='w-20 md:w-[130px]'
                    classNames={{ image: 'rounded-l-lg' }}
                  />
                  <CardHeader className='flex-1 flex flex-col gap-2 p-0 space-y-0'>
                    <H3 themed weight='bold' asChild>
                      <CardTitle>Wizkid Live</CardTitle>
                    </H3>
                    <Tiny
                      themed
                      className='text-fg-muted-light dark:text-fg-base-dark'
                      asChild
                    >
                      <CardDescription>60,000 attending</CardDescription>
                    </Tiny>
                    <Button size='xs' className='self-start'>
                      Get Ticket
                    </Button>
                  </CardHeader>
                  <CardFooter className='rounded-sm bg-brand-60 size-14 flex flex-col gap-1 px-2 py-1 items-center'>
                    <H3 weight='bold'>MAY</H3>
                    <H3 weight='bold'>23</H3>
                  </CardFooter>
                </CardContent>
              </Card>
            </CarouselItem>
          ))}
        </CarouselContent>

        <CarouselPrevious className='hidden md:flex bg-bg-subtle-light/30 dark:bg-bg-subtle-dark/60 border-border-subtle-light/20 dark:border-border-subtle-light/20 text-fg-subtle-light dark:text-fg-subtle-dark hover:bg-bg-subtle-light/80 dark:hover:bg-bg-subtle-dark/80' />
        <CarouselNext className='hidden md:flex bg-bg-subtle-light/30 dark:bg-bg-subtle-dark/60 border-border-subtle-light/20 dark:border-border-subtle-light/20 text-fg-subtle-light dark:text-fg-subtle-dark hover:bg-bg-subtle-light/80 dark:hover:bg-bg-subtle-dark/80' />
      </Carousel>

      <div className='flex justify-center gap-[5px] mt-4 md:mt-2'>
        {events.map((_, index) => (
          <button
            key={index}
            className={cn(
              'size-2 rounded-full transition-all duration-300',
              index === current
                ? 'w-8 bg-brand-60 scale-110'
                : 'bg-bg-interactive-primary-light dark:bg-bg-interactive-primary-dark hover:bg-gray-400 hover:scale-105'
            )}
            onClick={() => handleDotClick(index)}
            aria-label={`Go to slide ${index + 1}`}
          />
        ))}
      </div>
    </div>
  );
}
