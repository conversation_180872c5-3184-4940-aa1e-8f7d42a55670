'use client';
import { FreeEvent } from '@/components/events/free-event';
import { TicketedEventPage } from '@/components/events/ticketed-event';
import { ISingleEvent } from '@/api';

export function EventCheckoutPage({ event }: { event: ISingleEvent }) {
  return (
    <main className='min-h-screen'>
      <div className='text-sm mb-4 text-fg-muted-light'>
        Event / <span className='text-fg-muted-light'>{event.title}</span> /{' '}
        <span className='text-white'>Checkout</span>
      </div>

      {event.isTicketed ? (
        <TicketedEventPage event={event} />
      ) : (
        <FreeEvent
          eventId={event.id}
          freeEventRegistration={event.freeEventRegistration ?? false}
          registrationRequired={event.registrationRequired ?? false}
          onSubmit={() => {}}
        />
      )}
    </main>
  );
}
