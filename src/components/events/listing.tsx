'use client';
import {
  Calendar,
  MapPin,
  Heart,
  ChevronLeft,
  ChevronRight,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { MdBoldLabel } from '@/components/ui/typography';
import { useGetEvents } from '@/api';
import { formatDateTime } from '@/lib';
import Link from 'next/link';

export default function EventListing() {

  const { data: eventsData, isLoading, isError, error } = useGetEvents({ variables: { take: 12 } });

  if (isLoading) return <div className='flex justify-center items-center h-screen'>Loading events...</div>;
  if (isError) return <div className='flex justify-center items-center h-screen'>Error: {error?.message}</div>;

  const events = eventsData?.events ?? [];

  if (!events.length) return <div className='flex justify-center items-center h-screen'>No events found</div>;

   return (
    <div className='text-white py-4'>
      <div className='max-w-7xl mx-auto'>
        <Tabs defaultValue='all' className='w-full flex flex-col gap-4'>

          <TabsList className="grid w-full grid-cols-5 gap-8 bg-transparent rounded-none h-auto p-0 px-4">
            {['all', 'webinar', 'music', 'rave', 'concerts'].map((tab) => (
              <MdBoldLabel weight="bold" asChild key={tab}>
                <TabsTrigger
                  value={tab}
                  className="data-[state=active]:bg-transparent data-[state=active]:border-b-2 data-[state=active]:border-brand-60 rounded-none py-4 px-0 text-fg-subtle-light dark:text-fg-subtle-dark data-[state=active]:text-brand-60 hover:text-white transition-colors"
                >
                  {tab.charAt(0).toUpperCase() + tab.slice(1)}
                </TabsTrigger>
              </MdBoldLabel>
            ))}
          </TabsList>

          <TabsContent value="all">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {events.map((event) => (
                <Card
                  key={event.id}
                  className="bg-gray-800 border-gray-700 overflow-hidden group hover:scale-105 transition-transform duration-200"
                >
                  <Link href={`/events/${event.slug}`} className="block h-full">
                  <div className="relative">
                    <div
                    className="h-48 bg-cover bg-center relative overflow-hidden"
                    style={{
                      backgroundImage:
                        `url(${event.bannerUrl})`,
                    }}
                  >
                    <div className="absolute inset-0 bg-linear-to-br from-purple-900/50 to-transparent"></div>

                    <div className="absolute bottom-0 left-0 w-full h-1/2 bg-linear-to-t from-black/60 to-transparent"></div>

                    <Button
                      size="icon"
                      variant="ghost"
                      className="absolute top-3 right-3 h-8 w-8 rounded-full bg-blue-600/80 hover:bg-blue-600 text-white"
                    >
                      <Heart className="h-4 w-4" />
                    </Button>
                  </div>
                  </div>
                  <CardContent className="p-4 space-y-2">
                    <h3 className="font-semibold text-lg text-white">{event.title}</h3>
                    <div className="flex items-center gap-2 text-gray-400 text-sm">
                      <Calendar className="h-4 w-4" />
                      <span>{formatDateTime(new Date(event.startTime))}</span>
                    </div>
                    <div className="flex items-center gap-2 text-gray-400 text-sm">
                      <MapPin className="h-4 w-4" />
                      <span>{event.location?.landmark ?? 'Online'}</span>
                    </div>
                  </CardContent>
                  </Link>
                </Card>
              ))}
            </div>
            <div className='flex items-center justify-center gap-2 mt-12'>
              <Button
                variant='ghost'
                size='sm'
                className='text-gray-400 hover:text-white hover:bg-gray-800'
              >
                <ChevronLeft className='h-4 w-4 mr-1' />
                Prev
              </Button>

              <div className='flex items-center gap-1'>
                <Button
                  variant='ghost'
                  size='sm'
                  className='w-8 h-8 rounded-full bg-blue-600 text-white hover:bg-blue-700'
                >
                  1
                </Button>
                <Button
                  variant='ghost'
                  size='sm'
                  className='w-8 h-8 rounded-full text-gray-400 hover:text-white hover:bg-gray-800'
                >
                  2
                </Button>
                <Button
                  variant='ghost'
                  size='sm'
                  className='w-8 h-8 rounded-full text-gray-400 hover:text-white hover:bg-gray-800'
                >
                  3
                </Button>
                <span className='text-gray-400 px-2'>...</span>
                <Button
                  variant='ghost'
                  size='sm'
                  className='w-8 h-8 rounded-full text-gray-400 hover:text-white hover:bg-gray-800'
                >
                  9
                </Button>
              </div>

              <Button
                variant='ghost'
                size='sm'
                className='text-brand-60 hover:text-blue-300 hover:bg-gray-800'
              >
                Next
                <ChevronRight className='brandborder-brand-60 w-4 my-1 px-0' />
              </Button>
            </div>
          </TabsContent>

          {['webinar', 'music', 'rave', 'concerts'].map((tab) => (
            <TabsContent key={tab} value={tab} className="mt-8">
              <div className="text-center py-12">
                <p className="text-gray-400">No {tab} events found.</p>
              </div>
            </TabsContent>
          ))}
        </Tabs>
      </div>
    </div>
  );
}
