'use client';
import { apiEventRegistration } from '@/api/events/requests';
import { Button } from '@/components/ui';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/lib';
import { useCallback, useState } from 'react';
import toast from 'react-hot-toast';

export function FreeEvent({
  eventId,
  registrationRequired,
  freeEventRegistration,
  onSubmit,
  userEmail,
  userFullName,
}: {
  eventId: string;
  registrationRequired: boolean;
  freeEventRegistration: boolean;
  onSubmit: () => void;
  userEmail?: string;
  userFullName?: string;
}) {
  const route = useRouter();
  const { isAuthenticated } = useAuth();
  const [fullname, setFullname] = useState('');
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);

  const eventRegistration = useCallback(
    async (e: React.FormEvent) => {
      e.preventDefault();
      try {
        setLoading(true);
        const response = await apiEventRegistration({
          eventId: eventId,
          fullName: fullname || userFullName || '',
          email: email || userEmail || '',
        });

        if (response.data.data) {
          toast.success('Registered successfully');
          route.push(`/my-tickets`);
        }
      } catch (error: any) {
        if (error?.response?.data?.message) {
          toast.error(error?.response?.data?.message);
        }
        console.error('Failed to register', error);
      } finally {
        setLoading(false);
      }
    },
    [fullname, email]
  );

  return (
    <>
      <h1 className=' text=[24px] font-semibold text-gray-900'>Free event</h1>
      <div className='mt-8 flex text-[16px] justify-center px-4 py-2 text-gray-900 bg-gray-100 border border-[#1A1A1A1A] rounded-[8px]'>
        <p>
          {registrationRequired
            ? 'Event organizer requires registration'
            : 'This event does not require registration'}
        </p>
      </div>
      {registrationRequired && (
        <>
          {!isAuthenticated ? (
            <form onSubmit={eventRegistration} className='space-y-4 mt-4'>
              <div className='w-full max-w-sm min-w-[200px]'>
                <label className='text-[16px] text-gray-900'>Full name</label>
                <input
                  type='text'
                  value={fullname}
                  onChange={(e) => setFullname(e.target.value)}
                  required
                  className='w-full placeholder:text-gray-500 placeholder:text-sm text-slate-700 text-sm border border-gray-200 shadow-xs rounded-[8px] px-3 py-2 focus:outline-hidden'
                  placeholder='Enter your full name'
                />
              </div>
              <div className='w-full max-w-sm min-w-[200px] mt-3'>
                <label className='text-[16px] text-gray-900'>Email</label>
                <input
                  type='email'
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  className='w-full placeholder:text-gray-500 placeholder:text-sm text-slate-700 text-sm border border-gray-200 shadow-xs rounded-[8px] px-3 py-2 focus:outline-hidden'
                  placeholder='Enter your email'
                />
              </div>
              <div className='flex justify-end space-x-4'>
                <Button loading={loading} className=' w-full'>
                  Register
                </Button>
              </div>
            </form>
          ) : (
            <div className=' mt-8'>
              <Button
                disabled={freeEventRegistration}
                loading={loading}
                className=' w-full'
                onClick={eventRegistration}
              >
                {freeEventRegistration ? 'Registered' : 'Register'}
              </Button>
            </div>
          )}
        </>
      )}
    </>
  );
}
