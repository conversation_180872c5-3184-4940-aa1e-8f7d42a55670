'use client';

import Link from 'next/link';
import { Button } from '@/components/ui/button';

interface RouteButtonProps {
  href: string;
  disabled?: boolean;
  label?: string;
  variant?:
    | 'default'
    | 'secondary'
    | 'outline'
    | 'destructive'
    | 'ghost'
    | 'link'
    | undefined;
  size?: 'default' | 'sm' | 'lg' | undefined;
}

export function RouteButton({
  href,
  disabled,
  label = 'Continue',
  variant = 'default',
  size = 'sm',
}: RouteButtonProps) {
  if (disabled) {
    return (
      <Button
        type="button"
        disabled
        size={size}
        className="h-[52px] w-full justify-center"
        label={label}
        variant={variant}
      />
    );
  }

  return (
    <Link href={href} prefetch className="w-full">
      <Button
        type="button"
        className="h-[52px] w-full justify-center"
        label={label}
      />
    </Link>
  );
}
