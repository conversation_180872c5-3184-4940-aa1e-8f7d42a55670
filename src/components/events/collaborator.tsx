import Image from 'next/image';
import { X } from 'lucide-react';
import { cn } from '@/lib/utils';

interface CollaboratorCardProps {
  artist: { id: string; avatar: string; name: string };
  onRemove?: () => void;
}

export const CollaboratorCard = ({ artist, onRemove }: CollaboratorCardProps) => {
  return (
    <div
      key={artist.id}
      className="relative h-[92px] w-[95px] rounded-md overflow-hidden"
    >
      {/* Gradient overlay */}
      <div
        className="absolute top-0 left-0 right-0 h-full z-5 rounded-md"
        style={{
          background: 'linear-gradient(to bottom, rgba(255,255,255,0.05), rgba(255,255,255,0))',
        }}
      />

      {/* Remove button */}
      {onRemove && (
        <button
          onClick={onRemove}
          className="absolute -right-1 -top-1 z-10 flex items-center justify-center rounded-full bg-red-600 w-6 h-6"
        >
          <div className="flex items-center justify-center rounded-full border-2 border-white w-4 h-4">
            <X size={12} color="#fff" />
          </div>
        </button>
      )}

      {/* Avatar and name */}
      <div className="flex flex-col items-center justify-center gap-2.5 h-full z-10 relative">
        <div className="w-[51px] h-[51px] relative rounded-full overflow-hidden">
          <Image
            src={artist.avatar || '/placeholder-avatar.png'}
            alt={artist.name}
            fill
            className="object-cover"
          />
        </div>
        <span
          className="text-md text-white truncate max-w-full"
          title={artist.name}
        >
          {artist.name}
        </span>
      </div>
    </div>
  );
}