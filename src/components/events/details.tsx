'use client';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { Calendar, MapPin, Plus, Heart } from 'lucide-react';
import { formatDate, formatTime, formatDateTime } from '@/lib/utils';
import { isSameDay } from 'date-fns';
import { useAuth } from '@/lib/hooks/use-auth';
import { useCallback, useState } from 'react';
import { hasTicketCategories, isPresaleActive } from '@/lib/utils';
import { isPast } from 'date-fns';
import toast from 'react-hot-toast';
import { OptionType } from '@/types';
import {
  ISingleEvent,
  EventFormat,
  type ExportListType,
  getEventGuestList,
  type GuestListResponse,
  useDeleteEvent,
  useDeleteEventArtist,
  useGetEventCategories,
  useGetEventDiscounts,
  useGetEventGuestList,
  useReserveEventTicket,
} from '@/api/events';
import { useDownloadFile } from '@/lib/hooks/use-download-file';
import { useQueryClient } from '@tanstack/react-query';
import {
  Button,
  H1,
  H4,
  H5,
  Md<PERSON>oldLabel,
  SmRegularLabel,
  XsBoldLabel,
} from '@/components/ui';

const exportGuestsModalOptions: OptionType[] = [
  { label: 'PDF', value: 'pdf' },
  { label: 'Excel', value: 'excel' },
];

export function EventDetailsPage({
  event,
  slug,
}: {
  event: ISingleEvent;
  slug: string;
}) {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  const eventId = event?.id;

  const [confirmRAVisible, setConfirmRAVisible] = useState(false);

  const [artistToDelete, setArtistToDelete] = useState<string | null>(null);

  const isCreator = user?.id === (event?.organizer.id || event?.organizerId);
  const isSoldOut = hasTicketCategories(event?.ticketCategories)
    ? Object.values(event?.ticketCategories).every(
        ({ quantity }) => quantity === 0
      )
    : false;

  const hasEventEnded = event?.endTime ? isPast(event.endTime) : true;

  const { mutate: deleteEvent, isPending: deletingEvent } = useDeleteEvent();

  const handleDeleteEvent = () => {
    const confirmed = window.confirm(
      'Are you sure you want to delete this event?'
    );
    if (confirmed) {
      deleteEvent(
        { id: event?.id },
        {
          onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['getEvents'] });
            toast.success('Event deleted successfully');
            router.back();
          },
          onError: (error: any) => toast.error(error.message),
        }
      );
    }
  };

  const [fileType, setFileType] = useState<ExportListType>();

  const { isFetching: isExportingList } = useGetEventGuestList({
    variables: { eventId, fileType: fileType || 'excel' },
    enabled: false,
  });

  const { downloadFile, isDownloading } = useDownloadFile();

  const handleDownloadGuestList = useCallback(
    async (type: ExportListType) => {
      setFileType(type);

      const queryKey = useGetEventGuestList.getKey({
        eventId,
        fileType: type,
      });

      try {
        const data = await queryClient.fetchQuery<GuestListResponse>({
          queryKey,
          queryFn: () => getEventGuestList({ eventId, fileType: type }),
          staleTime: 5 * 60 * 1000,
        });

        if (data?.link) {
          await downloadFile({
            url: data.link,
            fileName:
              data.link.split('/').pop() || `${event?.title} - guest list`,
            mimeType:
              type === 'excel' ? 'application/vnd.ms-excel' : 'application/pdf',
          });
        }
      } catch (error) {
        console.error('❌ Error downloading guest list:', error);
      } finally {
        setFileType(undefined);
      }
    },
    [eventId, event?.title, queryClient, downloadFile]
  );

  const onSelectOption = useCallback(
    (option: OptionType) => {
      handleDownloadGuestList(option.value as ExportListType);
    },
    [handleDownloadGuestList]
  );

  const { data: categories } = useGetEventCategories();

  const eventCategory = categories?.find(
    (categories: any) => categories.id === event?.categoryId
  );
  const hasActivePresale = (): boolean => {
    if (!event?.presaleConfig || event.presaleConfig.length === 0) {
      return false;
    }

    return event.presaleConfig.some((presaleTicket) =>
      isPresaleActive(presaleTicket)
    );
  };

  const hasPhysicalLocation = event?.eventFormat !== EventFormat.ONLINE;

  const eventAddress = hasPhysicalLocation
    ? event?.location.landmark ||
      event?.location.address.replace(/^CCQH\+9HP\s*,?\s*/, '').trim()
    : event.onlineEventUrl || 'Online';

  const { data: discounts } = useGetEventDiscounts({
    variables: { eventId },
    enabled: isCreator,
  });

  const { mutate: removeArtist, isPending: isRemovingArtist } =
    useDeleteEventArtist({
      onSuccess: () => {
        queryClient.invalidateQueries({
          queryKey: [
            'getEventWithSlug',
            { slug, targetCurrency: 'NGN', userId: user?.id },
          ],
        });
        toast.success('Artist removed from event successfully');
      },
      onError: (error: any) => toast.error(error.message),
    });

  const { isPending: reservingTicket, mutate: reserveTicket } =
    useReserveEventTicket({
      onSuccess: ({ expiresAt }: { expiresAt: string }) => {
        router.push(
          `/events/${eventId}/tickets/checkout?expiresAt=${encodeURIComponent(
            expiresAt
          )}`
        );
      },
      onError: (error: any) => toast.error(error.message),
    });

  // if (isEventLoading || deletingEvent || isRemovingArtist)
  //   return <LoadingScreen />;
  const router = useRouter();

  const ticketCategories = event?.ticketCategories || {};
  const prices = Object.values(ticketCategories).map(
    (ticket) => ticket.convertedCost || ticket.cost
  );
  const startingPrice = prices.length > 0 ? Math.min(...prices) : null;

  return (
    <main className='min-h-screen bg-black text-white px-6 py-6'>
      <div className='text-sm text-muted-foreground mb-6'>
        <span className='font-medium text-white'>Events</span>
        <span className='mx-1'>/</span>
        <span className='text-muted-foreground'>{event.title}</span>
      </div>

      <div className='grid lg:grid-cols-2'>
        <div className='relative h-full w-full overflow-hidden'>
          <Image
            src={event.bannerUrl}
            alt='Event Banner'
            fill
            className='object-cover'
            unoptimized
          />
          <div className='absolute top-4 left-4 bg-bg-info-light dark:bg-bg-info-dark px-4 py-2 text-sm rounded-full'>
            <MdBoldLabel className='text-fg-info-light dark:text-fg-info-dark'>
              {eventCategory?.category}
            </MdBoldLabel>
          </div>
          <div className='absolute top-4 right-4'>
            <Heart className='size-8 text-red-500' />
          </div>
        </div>

        <div className='px-4 py-6 flex flex-col gap-y-4'>
          <div className='flex items-center justify-between'>
            <H1>{event.title}</H1>
            {!hasActivePresale() && (
              <span className='bg-bg-success-light dark:bg-bg-success-dark px-2 py-1 rounded-full justify-center items-center'>
                <XsBoldLabel className='text-fg-success-light dark:text-fg-success-dark'>
                  Presale
                </XsBoldLabel>
              </span>
            )}
          </div>

          <div className='flex items-start gap-x-3'>
            <div className='size-6'>
              <Calendar className='size-4' />
            </div>

            <div className='gap-y-1'>
              <H5>
                {isSameDay(
                  new Date(event.startTime),
                  new Date(event.endTime)
                ) ? (
                  <>
                    {formatDate(event.startTime)} {formatTime(event.startTime)}{' '}
                    – {formatTime(event.endTime)}
                  </>
                ) : (
                  <>
                    {formatDateTime(new Date(event.startTime))} –{' '}
                    {formatDateTime(new Date(event.endTime))}
                  </>
                )}
              </H5>
              <span className='text-xs text-fg-muted-light dark:text-fg-muted-dark'>
                Times are shown in your local timezone
              </span>
            </div>
          </div>

          {/* Calendar Button */}
          <button className='inline-flex items-center gap-2 text-sm pl-2 pr-3 py-2 border border-accent-bold-light text-accent-bold-dark rounded-full hover:bg-purple-600 hover:text-white w-max'>
            Add to calendar
          </button>

          {/* Location */}
          <div className='flex items-start gap-3 text-sm'>
            <MapPin className='w-5 h-5 mt-1' />
            <div>
              <H5 className='text-white font-medium'>
                {event.location.landmark}
              </H5>
              <p className='text-fg-muted-light dark:text-fg-muted-dark text-xs'>
                {event.location.address}
              </p>
            </div>
          </div>

          {/* Map Button */}
          <button className='inline-flex items-center gap-2 text-sm px-4 py-2 border border-purple-600 text-purple-400 rounded-full hover:bg-purple-600 hover:text-white w-max'>
            View on map
          </button>

          {/* Organizer */}
          <div className='flex items-center gap-4 mt-2'>
            <Image
              src={event.organizer.profileImageUrl || '/user.png'}
              alt='Organizer'
              width={40}
              height={40}
              className='rounded-full'
            />
            <div>
              <p className='font-medium'>{event.organizer.fullName}</p>
              <p className='text-muted-foreground text-sm'>Organizer</p>
            </div>
          </div>

          {(event.artists?.length ?? 0) > 0 && (
            <div>
              <h3 className='font-semibold mb-2'>Line up</h3>
              <div className='flex gap-3 overflow-x-auto'>
                {event.artists?.map((artist, idx) => (
                  <div
                    key={idx}
                    className='rounded-xl bg-[#1c1c1e] p-3 flex flex-col items-center justify-center'
                  >
                    <Image
                      src={artist.avatar || '/default-avatar.jpg'}
                      alt={artist.artistName}
                      width={60}
                      height={60}
                      className='rounded-full object-cover'
                    />
                    <p className='text-xs mt-1'>{artist.artistName}</p>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Description */}
          <div>
            <h3 className='font-semibold mb-2'>About this event</h3>
            <p className='text-sm text-muted-foreground'>{event.description}</p>
          </div>

          {/* Pricing */}
          <div className='flex items-center justify-between p-4 border-t'>
            <div>
              <H4 className='text-lg font-bold'>
                {Number(startingPrice).toLocaleString('en-NG')} NGN
              </H4>
              <SmRegularLabel className='text-fg-muted-light dark:text-fg-muted-dark'>
                Starting from
              </SmRegularLabel>
            </div>
            <Button
              label='Get Ticket'
              className='w-[256px]'
              onClick={() => router.push(`/events/${event.slug}/checkout`)}
            />
          </div>
        </div>
      </div>
    </main>
  );
}
