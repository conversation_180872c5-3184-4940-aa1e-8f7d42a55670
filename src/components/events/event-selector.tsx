import React, { useMemo, useState } from 'react';
import './event.css';
import { IEventCategories } from '@/api';
import menu from '@/components/icons/MenuIcon';

export function EventSelector({
  className = '',
  categories,
  onSelected,
}: {
  className?: string;
  categories?: IEventCategories[];
  onSelected?: (v: IEventCategories) => void;
}) {
  const [show, setShow] = useState<Number>(0);

  const icon = (value: string) => {
    const obj = menu.find((item) => item.category === value);
    return obj;
  };

  return (
    <main className={`md:flex justify-center ${className}`}>
      <div className='max-w-[1440px] flex flex-1 md:px-[100px] overflow-scroll hide-scroll-bar'>
        {[{ id: 'all', category: 'All' }, ...(categories || [])].map(
          (item, i) => (
            <div
              className={`flex flex-row gap-1 items-center hover:bg-gray-100 cursor-pointer justify-center text-[14px] md:text-[16px] px-5 py-2 rounded-[50px] ${
                show === i &&
                'bg-linear-to-r from-[#B098FF] to-[#6600CC] text-[#FDFDFD]'
              }`}
              onClick={() => {
                if (show !== i) {
                  setShow(i);
                  if (onSelected) {
                    onSelected(item);
                  }
                }
              }}
            >
              {show === i
                ? icon(item?.category)?.iconWhite
                : icon(item?.category)?.iconBlack}
              <p className='text-nowrap'>{item.category}</p>
            </div>
          )
        )}
      </div>
    </main>
  );
}
