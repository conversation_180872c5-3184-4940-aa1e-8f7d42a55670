import React, {
  Dispatch,
  forwardRef,
  SetStateAction,
  useCallback,
  useRef,
  useState,
} from 'react';
import { format, addDays, subDays, startOfWeek } from 'date-fns';
import { Button } from '@/components/ui';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { AllEventsQueryParams } from '@/types';
import Picker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import './datepicker.css';
import { Icons } from '@/components/icons/icons';
import moment from 'moment';

interface DatePickerProp {
  config: AllEventsQueryParams;
  setConfig: Dispatch<SetStateAction<AllEventsQueryParams>>;
  onSelected?: (v: Date) => void;
}

const initDays = (date: Date) => {
  // Start 3 days before the selected date to center it
  const startDate = subDays(date, 3);
  const newDays: Date[] = [];
  for (let i = 0; i < 7; i++) {
    newDays.push(addDays(startDate, i));
  }
  return newDays;
};

const DatePicker = ({ onSelected }: DatePickerProp) => {
  const [currentDate, setCurrentDate] = useState<Date>(new Date());
  const [days, setDays] = useState<Date[]>(initDays(new Date()));
  const datePickerRef = useRef<any>(null);

  const handleInputClick = () => {
    if (datePickerRef.current) {
      datePickerRef.current.setOpen(!datePickerRef.current.isCalendarOpen()); // Close the date picker when input is clicked
    }
  };

  const handlePrev = (): void => {
    const newDate = subDays(currentDate, 7);
    setDays(initDays(newDate));
    setCurrentDate(newDate);
    if (onSelected) onSelected(newDate);
  };

  const handleNext = (): void => {
    const newDate = addDays(currentDate, 7);
    setDays(initDays(newDate));
    setCurrentDate(newDate);
    if (onSelected) onSelected(newDate);
  };

  console.log('currentDate', currentDate);

  const CustomInput = forwardRef(({ value, onClick, className }: any, ref) => (
    <button
      className={className}
      onClick={() => {
        onClick();
        handleInputClick();
      }}
    >
      {format(currentDate, 'MMMM yyy')}
      <Icons.down />
    </button>
  ));

  const addNewDays = useCallback((date: Date) => {
    setDays(initDays(date));
  }, []);

  return (
    <>
      <main className='flex justify-center'>
        <div className='max-w-[1440px] flex flex-1 flex-col px-[25px] md:px-[100px] py-[20px] md:py-[40px]'>
          <div className=' border border-[#6600CC] text-[#6600CC] text-[14px] py-[5px] px-[2px]'>
            <p
              onClick={() => {
                const newDate = new Date();
                setCurrentDate(newDate);
                setDays(initDays(newDate));
                if (onSelected) onSelected(newDate);
              }}
              className='max-w-max cursor-pointer'
            >
              Today {moment(new Date()).format('Do MMM')}
            </p>
          </div>
        </div>
      </main>
      <main className='flex justify-center'>
        <div className={`flex flex-1 flex-col`}>
          <div className='flex flex-col items-center mb-6'>
            <div className='flex items-center justify-center py-2 mb-4 border-b border-gray-200'>
              <Picker
                toggleCalendarOnIconClick
                ref={datePickerRef}
                selected={currentDate}
                onChange={(date) => {
                  const newDate = date || new Date();
                  setCurrentDate(newDate);
                  setDays(initDays(newDate));
                  if (onSelected) onSelected(newDate);
                }}
                customInput={
                  <CustomInput className=' flex justify-center items-center gap-2' />
                }
              />
            </div>
            <div className='flex w-full justify-center gap-2  md:gap-4 items-center'>
              <Button
                variant='ghost'
                onClick={handlePrev}
                className='px-2 py-1'
              >
                <ChevronLeft className='text-gray-500' />
              </Button>
              {days.map((day, index) => (
                <div
                  key={index}
                  className={`flex flex-col flex-1 max-w-[80px]  justify-center rounded-lg text-[12px] md:text-[14px] items-center bg-gray-100 border  ${
                    format(day, 'dd') === format(currentDate, 'dd')
                      ? 'shadow-lg h-[72px] md:h-[100px] text-[#6600CC]'
                      : 'text-[#71717A] h-[60px] md:h-[84px] '
                  } rounded cursor-pointer`}
                  onClick={() => {
                    setCurrentDate(day);
                    // Always center the clicked date by moving 3 days back from it
                    setDays(initDays(day));
                    if (onSelected) {
                      onSelected(day);
                    }
                  }}
                >
                  <div>{format(day, 'dd')}</div>
                  <div
                    className={` flex w-full${
                      format(day, 'dd') === format(currentDate, 'dd')
                        ? 'underline'
                        : 'no-underline'
                    }`}
                  >
                    {format(day, 'EEE')}
                  </div>
                </div>
              ))}
              <Button
                variant='ghost'
                onClick={handleNext}
                className='px-2 py-1'
              >
                <ChevronRight className='text-gray-500' />
              </Button>
            </div>
          </div>
        </div>
      </main>
    </>
  );
};

export default DatePicker;
