.hide-scroll-bar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
.hide-scroll-bar::-webkit-scrollbar {
  display: none;
}

.react-datepicker__input-container input {
  width: 100%;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 8px;
  outline: none;
  transition: border-color 0.3s;
}

.react-datepicker__input-container input:focus {
  border-color: #6600cc;
}

/* Calendar styles */
.react-datepicker {
  border-radius: 12px;
  border: 1px solid #ddd;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Header styles */
.react-datepicker__header {
  background-color: white;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
}

/* Calendar days */
.react-datepicker__day {
  width: 36px;
  line-height: 36px;
  border-radius: 50%;
  transition: background-color 0.3s, color 0.3s;
}

.react-datepicker__day--selected {
  /* background-color: #6600cc; */
  background: linear-gradient(135deg, #b098ff, #6600cc);
  color: white;
}

.react-datepicker__day--keyboard-selected {
  background-color: #c9abe7;
  color: white;
}

.react-datepicker__day:hover {
  background-color: #c9abe7; /* Lighter blue for hover */
  border-radius: 50%;
}
