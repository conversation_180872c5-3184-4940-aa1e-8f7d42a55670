import React from 'react';

interface BlurredBackgroundProps {
  isDark: boolean;
  blurStyle?: React.CSSProperties;
  overlayStyle?: React.CSSProperties;
  intensity?: number; // roughly matches RN's "intensity"
}

export const BlurredBackground: React.FC<BlurredBackgroundProps> = ({
  isDark,
  blurStyle,
  overlayStyle,
  intensity = isDark ? 20 : 70,
}) => {
  const blurPx = Math.round((intensity / 100) * 20); // convert RN intensity scale to px blur

  return (
    <div
      style={{
        position: 'relative',
        backdropFilter: `blur(${blurPx}px)`,
        WebkitBackdropFilter: `blur(${blurPx}px)`,
        ...blurStyle,
      }}
    >
      <div
        style={{
          backgroundColor: isDark
            ? 'rgba(0,0,0,0.3)'
            : 'rgba(255,255,255,0.3)',
          ...overlayStyle,
        }}
      />
    </div>
  );
};