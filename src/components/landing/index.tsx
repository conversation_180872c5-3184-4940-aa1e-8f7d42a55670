import Image from 'next/image';
import {
  Calendar,
  Radio,
  Shield,
  DollarSign,
} from 'lucide-react';
import Navigation from '@/components/landing/navigation';
import HeroSection from '@/components/landing/hero';
import { D1, P } from '@/components/ui/typography';
import Footer from '@/components/landing/footer';

export default function PoplaLanding() {
  return (
    <div className='min-h-screen relative bg-[#0A0A0A] text-white'>
      <Navigation className='lg:absolute relative bg-[#121212] lg:bg-transparent lg:rounded-none rounded-t-[42px]' />

      {/* Hero Section */}
      <HeroSection />

      {/* QR Code Section */}
      <section className='mt-1.5 pt-[103px] flex flex-col gap-[86px] rounded-[42px] bg-[#121212] text-center'>
        <D1 weight='bold'>Scan To Download Popla App</D1>

        <div className='flex justify-center'>
          <div className='w-[516px] h-[668px] relative'>
            <Image
              src={
                process.env.NEXT_PUBLIC_URL
                  ? `${process.env.NEXT_PUBLIC_URL}/images/scan-popla-app.png`
                  : '/images/scan-popla-app.png'
              }
              fill
              alt='Scan QR Code to download Popla app'
              className='w-full h-full'
            />
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className='mt-[124px] mb-[97px] gap-24 flex flex-col items-center px-6 lg:px-20'>
        <div className='max-w-[1116px] text-center flex flex-col gap-6'>
          <D1 weight='bold'>Enhance Your Entertainment Journey with Popla</D1>
          <P className='text-[#CCCCCC] mx-auto'>
            Lorem ipsum dolor sit amet consectetur. Et tellus massa vitae
            ullamcorper hendrerit lobortis. Elit gravida egestas duis ut lacus.
            Venenatis posuere aliquam consectetur purus sagittis sollicitudin a
            maecenas. Sed iaculis imperdiet tristique vestibulum ac pharetra.
            Quam vitae diam.
          </P>
        </div>

        <div className='grid md:grid-cols-2 lg:grid-cols-4 gap-6 mx-auto'>
          <div className='text-center max-w-[318px] flex flex-col gap-9'>
            <div className='size-[124px] bg-[#121212] rounded-full flex items-center justify-center mx-auto'>
              <Calendar className='size-12 text-white' />
            </div>
            <div className='flex flex-col gap-3'>
              <P className='font-semibold'>Event Discovery</P>
              <P>
                Explore concerts, raves, and secret shows with smart filters
              </P>
            </div>
          </div>

          <div className='text-center max-w-[318px] flex flex-col gap-9'>
            <div className='size-[124px] bg-[#121212] rounded-full flex items-center justify-center mx-auto'>
              <Radio className='size-12 text-white' />
            </div>
            <div className='flex flex-col gap-3'>
              <P className='font-semibold'>Live Sessions</P>
              <P>Request songs, chat with DJs, and shape live performances</P>
            </div>
          </div>

          <div className='text-center max-w-[318px] flex flex-col gap-9'>
            <div className='size-[124px] bg-[#121212] rounded-full flex items-center justify-center mx-auto'>
              <Shield className='size-12 text-white' />
            </div>
            <div className='flex flex-col gap-3'>
              <P className='font-semibold'>Secure Ticketing</P>
              <P>Buy and sell tickets with blockchain-verified QR codes</P>
            </div>
          </div>

          <div className='text-center max-w-[318px] flex flex-col gap-9'>
            <div className='size-[124px] bg-[#121212] rounded-full flex items-center justify-center mx-auto'>
              <DollarSign className='size-12 text-white' />
            </div>
            <div className='flex flex-col gap-3'>
              <P className='font-semibold'>Earn while Creating</P>
              <P>Artists get 85%+ of revenue - highest in the industry</P>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
