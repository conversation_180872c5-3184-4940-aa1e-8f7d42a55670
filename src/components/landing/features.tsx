'use client';
import React from 'react';
import NextImage from '@/components/ui/NextImage';
import { D1, H2, P } from '@/components/ui/typography';
import { Button } from '@/components/ui/button';

const Features = () => (
  <main>
    <article className='text-center pt-20'>
      <div className='max-w-[1147px] mx-auto flex flex-col gap-9'>
        <D1 weight='bold'>
          Explore some of our exciting features designed just for you
        </D1>
        <P className='max-w-[770px] mx-auto'>
          Technology strategy encompasses a full set of consulting capabilities
          to help you think through the strategic issues and fully align
          technology to your overall purpose and business objectives.
        </P>
      </div>

      <section className='max-w-[1147px] mx-auto flex flex-col gap-28 my-[120px]'>
        <div className='flex flex-row gap-16 flex-wrap justify-center'>
          <div className='flex flex-col gap-[18px]'>
            <div className='flex text-start flex-col gap-8 max-w-[448px]'>
              <H2 className='text-2xl' weight='bold'>
                Lorem ipsum dolor sit amet consectetur. Enim tristique.
              </H2>
              <P>
                Lorem ipsum dolor sit amet consectetur. Egestas egestas amet et
                dui sed. Nulla ultrices faucibus sed dictum sed tellus placerat
                sapien. Interdum fermentum amet commodo leo a pretium etiam
                amet. Magna porttitor justo suspendisse eget elit nulla.
                Pellentesque aliquam viverra.
              </P>
            </div>
            <Button
              variant='default'
              className='rounded-full text-white px-6 py-3 self-start text-base leading-[16px] font-bold'
            >
              Get the app
            </Button>
          </div>
          <div className='relative w-[386px] h-[870px]'>
            <div className='absolute inset-0 -top-10 bg-linear-to-br from-[#A9C9FF] to-[#FFBBEC] opacity-80 transform -rotate-10 blur-[50px] custom-blob'></div>

            <div className='relative z-10 w-full h-full'>
              <NextImage
                src={
                  process.env.NEXT_PUBLIC_URL
                    ? `${process.env.NEXT_PUBLIC_URL}/images/Home-User-complete-setup.png`
                    : '/images/Home-User-complete-setup.png'
                }
                fill
                alt='Home User - complete account setup'
                className='object-cover'
              />
            </div>
          </div>
        </div>

        <div className='flex flex-row-reverse gap-16 flex-wrap items-center justify-center'>
          <div className='flex flex-col gap-[18px]'>
            <div className='flex text-start flex-col gap-8 max-w-[448px]'>
              <H2 className='text-2xl' weight='bold'>
                Lorem ipsum dolor sit amet consectetur. Eu sit a molestie
                blandit accumsan amet elit.
              </H2>
              <P>
                Lorem ipsum dolor sit amet consectetur. Vel mauris id urna leo
                blandit sed. Leo tempus volutpat semper at mauris est quam
                mauris cursus. Eu diam pulvinar congue tristique mauris iaculis
                nunc placerat amet. Feugiat nibh laoreet.
              </P>
            </div>
            <Button
              variant='default'
              className='rounded-full text-white px-6 py-3 self-start text-base leading-[16px] font-bold'
            >
              Get the app
            </Button>
          </div>
          <div className='relative w-[386px] h-[870px]'>
            <div className='absolute inset-0 -top-10 bg-linear-to-br from-[#FEE140] to-[#FA709A] opacity-80 transform -rotate-10 blur-[50px] custom-blob'></div>

            <div className='relative z-10 w-full h-full'>
              <NextImage
                src={
                  process.env.NEXT_PUBLIC_URL
                    ? `${process.env.NEXT_PUBLIC_URL}/images/Welcome-Screen-with-Multi-Option-Sign-In.png`
                    : '/images/Welcome-Screen-with-Multi-Option-Sign-In.png'
                }
                fill
                alt='Welcome Screen with Multi Option Sign-In'
                className='object-cover'
              />
            </div>
          </div>
        </div>

        <div className='flex flex-row gap-16 flex-wrap justify-center'>
          <div className='flex flex-col gap-[18px] mt-[113px]'>
            <div className='flex text-start flex-col gap-8 max-w-[448px]'>
              <H2 className='text-2xl' weight='bold'>
                Lorem ipsum dolor sit amet consectetur. Enim tristique.
              </H2>
              <P>
                Lorem ipsum dolor sit amet consectetur. Egestas egestas amet et
                dui sed. Nulla ultrices faucibus sed dictum sed tellus placerat
                sapien. Interdum fermentum amet commodo leo a pretium etiam
                amet. Magna porttitor justo suspendisse eget elit nulla.
                Pellentesque aliquam viverra.
              </P>
            </div>
            <Button
              variant='default'
              className='rounded-full text-white px-6 py-3 self-start text-base leading-[16px] font-bold'
            >
              Get the app
            </Button>
          </div>
          <div className='relative w-[386px] h-[870px]'>
            <div className='relative z-10 w-full h-full'>
              <NextImage
                src={
                  process.env.NEXT_PUBLIC_URL
                    ? `${process.env.NEXT_PUBLIC_URL}/images/Home-User-complete-setup.png`
                    : '/images/Home-User-complete-setup.png'
                }
                fill
                alt='Home User - complete account setup'
                className='object-cover'
              />
            </div>
          </div>
        </div>
      </section>

      <section className='bg-[#121212] w-full my-4 pt-[103px] flex flex-col gap-[86px] rounded-[42px] text-center'>
        <D1 weight='bold'>Scan To Download Popla App</D1>

        <div className='flex justify-center'>
          <div className='w-[516px] h-[668px] relative'>
            <NextImage
              src={
                process.env.NEXT_PUBLIC_URL
                  ? `${process.env.NEXT_PUBLIC_URL}/images/scan-popla-app.png`
                  : '/images/scan-popla-app.png'
              }
              fill
              alt='Scan QR Code to download Popla app'
              className='w-full h-full'
            />
          </div>
        </div>
      </section>
    </article>
  </main>
);

export default Features;
