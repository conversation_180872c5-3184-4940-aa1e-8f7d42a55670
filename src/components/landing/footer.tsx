import Link from 'next/link';
import { Instagram, Twitter, Facebook } from 'lucide-react';
import PoplaLogo from '~/svg/PoplaSplash.svg';
import { P } from '@/components/ui/typography';

export default function Footer() {
  return (
    <footer className='bg-[#121212] py-24 px-4 rounded-[42px]'>
      <div className='mx-auto w-full max-w-[1344px] px-4 sm:px-6 lg:px-8'>
        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-y-16 gap-x-12 text-center md:text-left place-items-center md:place-items-start'>
          {/* Logo */}
          <Link href='/' className='h-full max-h-12 w-full max-w-28'>
            <PoplaLogo className='mx-auto md:mx-0' />
          </Link>

          <div className='space-y-8'>
            <P className='text-[#CCCCCC]' weight='semibold'>
              Company
            </P>
            <ul className='space-y-8'>
              <P as='li'>
                <Link
                  href='/features'
                  className='text-white/70 hover:text-white transition-colors duration-200'
                >
                  Features
                </Link>
              </P>
              <P as='li'>
                <Link
                  href='/jobs'
                  className='text-white/70 hover:text-white transition-colors duration-200'
                >
                  Jobs
                </Link>
              </P>
            </ul>
          </div>

          <div className='space-y-8'>
            <P weight='semibold' className='text-[#CCCCCC]'>
              Legal
            </P>
            <ul className='space-y-8'>
              <P as='li'>
                <Link
                  href='/privacy'
                  className='text-white/70 hover:text-white transition-colors duration-200'
                >
                  Privacy
                </Link>
              </P>
              <P as='li'>
                <Link
                  href='/faq'
                  className='text-white/70 hover:text-white transition-colors duration-200'
                >
                  FAQ
                </Link>
              </P>
            </ul>
          </div>

          <div className='flex items-start justify-center md:justify-start lg:justify-end space-x-6'>
            <Link
              href='https://instagram.com'
              target='_blank'
              rel='noopener noreferrer'
              className='text-white/70 hover:text-white transition-colors duration-200'
              aria-label='Follow us on Instagram'
            >
              <Instagram className='h-8 w-8' />
            </Link>
            <Link
              href='https://twitter.com'
              target='_blank'
              rel='noopener noreferrer'
              className='text-white/70 hover:text-white transition-colors duration-200'
              aria-label='Follow us on Twitter'
            >
              <Twitter className='h-8 w-8' />
            </Link>
            <Link
              href='https://facebook.com'
              target='_blank'
              rel='noopener noreferrer'
              className='text-white/70 hover:text-white transition-colors duration-200'
              aria-label='Follow us on Facebook'
            >
              <Facebook className='h-8 w-8' />
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
}
