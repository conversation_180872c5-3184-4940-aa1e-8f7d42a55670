import React, { useEffect, useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';

interface CheckoutTimerProps {
  initialTime?: number; // Time in seconds (default: 10 minutes 59 seconds)
  onTimeExpired?: () => void;
}

export const CheckoutTimer: React.FC<CheckoutTimerProps> = ({
  initialTime = 659, // 10:59 in seconds
  onTimeExpired,
}) => {
  const router = useRouter();
  const [timeLeft, setTimeLeft] = useState(initialTime);

  // Format seconds to MM:SS
  const formatTime = useCallback((seconds: number) => {
    const m = Math.floor(seconds / 60);
    const s = seconds % 60;
    return `${m.toString().padStart(2, '0')}:${s.toString().padStart(2, '0')}`;
  }, []);

  useEffect(() => {
    if (timeLeft <= 0) {
      if (onTimeExpired) {
        onTimeExpired();
      } else {
        alert('Your ticket reservation has expired. Please try again.');
        router.back();
      }
      setTimeLeft(initialTime); // Reset timer (optional)
      return;
    }

    const timerId = setInterval(() => {
      setTimeLeft((prev) => prev - 1);
    }, 1000);

    return () => clearInterval(timerId);
  }, [timeLeft, onTimeExpired, router, initialTime]);

  return (
    <div
      style={{
        maxWidth: 331,
        backgroundColor: '#fee2e2', // red-100 approx
        padding: '4px 8px',
        borderRadius: 6,
        color: '#b91c1c', // red-700 approx
        textAlign: 'center',
        fontSize: '0.875rem',
        fontWeight: 500,
        margin: 'auto',
      }}
    >
      We've reserved your ticket. Please complete checkout within{' '}
      <span style={{ color: '#1e40af' /* blue-800 approx */, fontWeight: 600 }}>
        {formatTime(timeLeft)}
      </span>{' '}
      to secure your tickets.
    </div>
  );
};