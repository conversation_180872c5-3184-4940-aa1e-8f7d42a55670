import { useRouter } from 'next/navigation';
import React from 'react';
import QRCode from 'react-qr-code';

import { type ITicketExtension } from '@/api/events';

interface TicketItemProps extends ITicketExtension {
  isPast?: boolean;
  hasPresale?: boolean;
}

export const TicketItem: React.FC<TicketItemProps> = ({
  id,
  title,
  hasPresale,
  isPast,
  bannerUrl,
  transactionId,
}) => {
  const router = useRouter();

  const isDark = true;

  const qrColor = '#000'; // black

  return (
    <div
      onClick={() =>
        router.push(`/events/tickets/${id}?transactionId=${transactionId}`)
      }
      style={{
        cursor: 'pointer',
        display: 'flex',
        height: 103,
        borderRadius: 12,
        overflow: 'hidden',
        boxShadow: '0 1px 4px rgba(0,0,0,0.1)',
        userSelect: 'none',
      }}
    >
      {/* Left side - image and info */}
      <div style={{ position: 'relative', flex: 1 }}>
        <img
          src={
            isDark
              ? '/assets/ticket/ticket-bg-subtle-dark.png'
              : '/assets/ticket/ticket-bg-subtle-light.png'
          }
          alt="Ticket Background"
          style={{ width: '100%', height: '100%', objectFit: 'cover', borderTopLeftRadius: 12, borderBottomLeftRadius: 12 }}
          draggable={false}
        />
        <div
          style={{
            position: 'absolute',
            left: 14,
            top: 10,
            display: 'flex',
            alignItems: 'center',
            gap: 8,
          }}
        >
          <img
            src={bannerUrl}
            alt={title}
            style={{ width: 68, height: 68, borderRadius: 8, objectFit: 'cover' }}
            draggable={false}
          />
          <div>
            <h5 style={{ margin: 0, fontWeight: 'bold', fontSize: 16 }}>{title}</h5>
            {hasPresale && (
              <div
                style={{
                  marginTop: 4,
                  width: 58,
                  height: 20,
                  borderRadius: 9999,
                  backgroundColor: isDark ? '#166534' : '#bbf7d0', // green-80 / green-10
                  color: isDark ? '#bbf7d0' : '#166534',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  fontWeight: 700,
                  fontSize: 12,
                  userSelect: 'none',
                }}
              >
                Presale
              </div>
            )}
            <div
              style={{
                marginTop: 4,
                width: 94,
                height: 28,
                borderRadius: 9999,
                border: `1px solid ${isDark ? '#60a5fa' : '#1e40af'}`, // brand colors
                color: isDark ? '#60a5fa' : '#1e40af',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                fontWeight: 700,
                fontSize: 12,
                userSelect: 'none',
              }}
            >
              See details
            </div>
          </div>
        </div>
      </div>

      {/* Right side - QR code */}
      <div
        style={{
          position: 'relative',
          width: 74,
          display: 'flex',
          justifyContent: 'flex-end',
          backgroundColor: !isPast
            ? '#2563eb' // brand-500
            : isDark
            ? '#374151' // grey-700
            : '#d1d5db', // grey-300
          borderTopRightRadius: 12,
          borderBottomRightRadius: 12,
          alignItems: 'center',
          paddingRight: 6,
        }}
      >
        <QRCode
          value={id}
          size={58}
          bgColor="transparent"
          fgColor={qrColor}
          level="H"
        />
      </div>
    </div>
  );
};