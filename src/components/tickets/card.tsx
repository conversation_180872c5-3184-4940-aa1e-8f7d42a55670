'use client';

import React from 'react';
import moment from 'moment';
import QRCode from 'react-qr-code';
import { useColorScheme } from '@/lib/utils/index';

import { EventFormat, type ITicketExtension } from '@/api/events';

import { H2, Image, MdBoldLabel, P, colors } from '@/components/ui';


const WIDTH = 375;

interface TicketCardProps {
  ticket: ITicketExtension;
}

export const TicketCard: React.FC<TicketCardProps> = ({ ticket }) => {
  const isDark = useColorScheme() === 'dark';

  const isPhysicalLocation = ticket.event.eventFormat !== EventFormat.ONLINE;
  const locationText =
    isPhysicalLocation && ticket?.location
      ? ticket.location.landmark ||
        ticket.location.address.replace(/^CCQH\+9HP\s*,?\s*/, '').trim()
      : ticket?.event.onlineEventUrl || 'Online';

  return (
    <div
      style={{
        width: WIDTH - 64,
        borderRadius: 16,
        background: `linear-gradient(180deg, #131214 82%, #53575A 100%)`,
        color: isDark ? colors.grey[100] : colors.white,
      }}
      className='overflow-hidden'
    >
      <div className='relative h-[270px] rounded-t-lg overflow-hidden'>
        <Image
          src={typeof ticket.bannerUrl === 'string' ? ticket.bannerUrl : ''}
          alt='Event Banner'
          className='w-full h-full object-cover rounded-t-lg'
        />
        <div
          style={{
            position: 'absolute',
            inset: 0,
            background:
              'linear-gradient(180deg, rgba(0,0,0,0) 0%, rgba(0,0,0,1) 100%)',
          }}
        />
        <div className='absolute bottom-3 mx-3 gap-3 text-white'>
          <H2>{ticket.title}</H2>
          <div className='flex justify-between w-full'>
            <div className='flex-1 space-y-1'>
              <P className='text-gray-400 dark:text-gray-300'>DATE</P>
              <MdBoldLabel>
                {moment.utc(ticket.startTime).format('MMM D · LT')}
              </MdBoldLabel>
            </div>
            <div className='flex-1 space-y-1'>
              <P className='text-gray-400 dark:text-gray-300'>LOCATION</P>
              <MdBoldLabel>{locationText}</MdBoldLabel>
            </div>
          </div>
        </div>
      </div>

      <div className='p-3 pb-6 space-y-3 bg-linear-to-b from-transparent to-black/50'>
        {ticket.isUsed &&
          moment().isAfter(moment.utc(ticket.event.endTime)) && (
            <div className='h-8 w-[102px] flex items-center justify-center rounded-full bg-green-100 dark:bg-green-900'>
              <MdBoldLabel className='text-green-600 dark:text-green-400'>
                Attended
              </MdBoldLabel>
            </div>
          )}

        <div className='space-y-5'>
          <P className='text-gray-400 dark:text-gray-300'>BARCODE</P>
          <div className='mx-auto'>
            <QRCode
              value={ticket.id}
              size={215}
              bgColor='transparent'
              fgColor={colors.white}
              level='Q'
            />
          </div>
        </div>

        <div className='flex justify-between'>
          <div className='flex-1 space-y-1'>
            <P className='text-gray-400 dark:text-gray-300'>NAME</P>
            <MdBoldLabel>{ticket?.user?.fullName}</MdBoldLabel>
          </div>
          <div className='flex-1 space-y-1'>
            <P className='text-gray-400 dark:text-gray-300'>TICKET GRADE</P>
            <MdBoldLabel>
              {Object.keys(ticket?.meta.breakdown || {})[0]}
            </MdBoldLabel>
          </div>
        </div>

        <div className='flex justify-between'>
          <div className='flex-1 space-y-1'>
            <P className='text-gray-400 dark:text-gray-300'>TICKET ID</P>
            <MdBoldLabel>{ticket?.ticketId}</MdBoldLabel>
          </div>
          <div className='flex-1 space-y-1'>
            <P className='text-gray-400 dark:text-gray-300'>QUANTITY</P>
            <MdBoldLabel>
              {Object.values(ticket?.meta.breakdown || {})[0]}
            </MdBoldLabel>
          </div>
        </div>
      </div>
    </div>
  );
};
