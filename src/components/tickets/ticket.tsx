import React, { useEffect, useRef, useState } from 'react';
import Image from 'next/image';
import html2canvas from 'html2canvas';
import { Icons } from '@/components/icons/icons';
import { MdDateRange, MdLocationOn } from 'react-icons/md';
import { format } from 'date-fns';
import QRCode from 'react-qr-code';

interface TicketProps {
  eventName: string;
  eventDate: string;
  eventLocation: string;
  userName: string;
  ticketId: string;
  ticketType: string;
  imageUrl: string;
  id: string
}

const imageUrlToBase64 = async (imageUrl: string): Promise<string> => {
  console.log('imageUrl', imageUrl)
  const response = await fetch(imageUrl,  {
    method: 'GET',
    mode: 'no-cors'
  });
  const blob = await response.blob();

  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onloadend = () => {
      resolve(reader.result as string); // This will return the base64 string
    };
    reader.onerror = reject;
    reader.readAsDataURL(blob);
  });
};

const Ticket: React.FC<TicketProps> = ({
  eventName,
  eventDate,
  eventLocation,
  userName,
  ticketId,
  ticketType,
  imageUrl,
  id,
}) => {
  const ticketRef = useRef<HTMLDivElement>(null);
  const [base64Image, setBase64Image] = useState<string | null>(null);

  const handleDownload2 = () => {
    if (ticketRef.current === null) {
      return;
    }
    html2canvas(ticketRef.current)
      .then((canvas) => {
        const dataUrl = canvas.toDataURL('image/png');
        const link = document.createElement('a');
        link.href = dataUrl;
        link.download = `${eventName}-ticket.png`; // Set the name for the downloaded file
        link.click();
      })
      .catch((err) => {
        console.error('Failed to download ticket as image', err);
      });
  };

  useEffect(() => {
    const fetchImageAsBase64 = async () => {
      const base64 = await imageUrlToBase64(imageUrl);
      setBase64Image(base64);
      
    };

    fetchImageAsBase64();
  }, []);

  return (
    <div ref={ticketRef} className="relative max-w-md mx-auto bg-purple-100 p-4 rounded-xl">
      {/* Top section with image, event name, and download icon */}
      <div className=" relative flex justify-between items-center pb-10">
        <div className='relative w-[102px] h-[120px]'>
          <Image
            src={imageUrl}
            alt="Event Image"
            layout="fill"
            unoptimized
            objectFit="cover" // Ensures the image fills the container while maintaining aspect ratio
            className="w-full h-full rounded-md"
          />
        </div>
        <div className="ml-4 grow pr-4">
          <h2 className="text-lg font-bold">{eventName}</h2>
          <div className='detail-item flex items-center gap-1 text-[16px] text-[#71717A] mt-3'>
            <MdDateRange className='icon' color='#71717A' />
            <span>{format(eventDate, "d MMM 'at' HH:mm")}</span>
          </div>
          <div className='detail-item flex items-center gap-1 text-[16px] text-[#71717A] mt-2'>
            <MdLocationOn className='icon w-4 h-4' size={40} color='#71717A'  />
            <div className=" flex flex-col">
              <span>{eventLocation}</span>
            </div>
          </div>


          {/* <p className="text-sm text-gray-600">{eventDate}</p>
          <p className="text-sm text-gray-600">{eventLocation}</p> */}
        </div>
        <div data-html2canvas-ignore="true" className=" absolute top-px right-px z-50">
          <button onClick={handleDownload2} className="text-purple-700">
            <Icons.download />
          </button>
        </div>
      </div>

      {/* QR Code */}
      <div className=' absolute -left-14 h-[70px] top-[156px] w-[70px] bg-white rounded-full'/>
      <div className=' absolute -right-14 h-[70px] top-[156px] w-[70px] bg-white rounded-full'/>
      <div className="my-4 border-t flex flex-col items-center border-dashed border-gray-300 pt-10 pb-4">
        {/* <Image src={qrCodeSrc} alt="QR Code" width={200} height={200} /> */}
        <QRCode 
          value={id}
          bgColor="#F2E5FF"
          size={200}
        />
        <p className="text-center text-sm text-gray-500 mt-2">
          Scan this QR code or show at the entrance
        </p>
      </div>

      {/* Bottom section with ticket info */}
      <div className=' absolute -left-14 h-[70px] bottom-[156px] w-[70px] bg-white rounded-full'/>
      <div className=' absolute -right-14 h-[70px] bottom-[156px] w-[70px] bg-white rounded-full'/>
      <div className="border-t  border-dashed border-gray-300 pt-10 pb-10">
        <div className="flex justify-between">
          <div>
            <p className="text-sm font-medium">Name</p>
            <p className="text-sm font-light">{userName}</p>
          </div>
          <div>
            <p className="text-sm font-medium">Ticket ID</p>
            <p className="text-sm font-light">{ticketId}</p>
          </div>
        </div>
        <div className='flex justify-center'>
          <div className=" flex flex-col items-center mt-4">
            <p className="text-sm font-medium">Ticket Type</p>
            <p className="text-sm font-light">{ticketType}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Ticket;
