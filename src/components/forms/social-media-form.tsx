import { useFormContext } from 'react-hook-form';
import { ControlledInput } from '@/components/ui';
import { type EditAccountFormType } from '@/lib';

// Import the most accurate react-icons for each brand
import { FaInstagram, FaFacebook, FaTiktok, FaYoutube } from 'react-icons/fa';
import { SiX, SiSnapchat } from 'react-icons/si';

export type SocialMediaFormProps = {};

export const SocialMediaForm = () => {
  const { control } = useFormContext<EditAccountFormType>();

  return (
    <div className="flex-1 gap-4 px-4">
      <ControlledInput
        control={control}
        name="socials.instagram"
        label="Instagram"
        icon={<FaInstagram size={24} color="#bc2a8d" />}
      />
      <ControlledInput
        control={control}
        name="socials.x"
        label="X"
        icon={<SiX size={24} color="#14171a" />}
      />
      <ControlledInput
        control={control}
        name="socials.snapchat"
        label="Snapchat"
        icon={<SiSnapchat size={24} color="#fffc00" />}
      />
      <ControlledInput
        control={control}
        name="socials.facebook"
        label="Facebook"
        icon={<FaFacebook size={24} color="#1877F2" />}
      />
      <ControlledInput
        control={control}
        name="socials.tiktok"
        label="TikTok"
        icon={<FaTiktok size={24} color="#000000" />}
      />
      <ControlledInput
        control={control}
        name="socials.youtube"
        label="YouTube"
        icon={<FaYoutube size={24} color="#FF0000" />}
      />
    </div>
  );
};