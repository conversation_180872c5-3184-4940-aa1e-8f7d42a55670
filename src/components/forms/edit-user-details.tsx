import React from 'react';
import { useFormContext } from 'react-hook-form';

import {
  Spinner,
  AtIcon,
  colors,
  ControlledInput,
  MessageFilledIcon,
  P,
  UserFilledIcon,
} from '@/components/ui';
import { type EditAccountFormType, useLoggedInUser, useAccountSetup } from '@/lib';

export type EditUserDetailsFormProps = {};

export const EditUserDetailsForm = () => {
  const { usernameRefinement, isValidatingUsername, setIsValidatingUsername } =
    useAccountSetup();
  const { control, watch, formState } = useFormContext<EditAccountFormType>();

  const { data: user } = useLoggedInUser();

  const username = watch('username');

  const [usernameValid, setUsernameValid] = React.useState<boolean | null>(null);

const handleUsernameChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
  const value = e.target.value;
  setIsValidatingUsername(true);
  usernameRefinement.invalidate();
  const valid = await usernameRefinement(value);
  setUsernameValid(valid);
  setIsValidatingUsername(false);
};
  const UsernameStatusIndicator = () => {
    const originalUsername = user?.username;

    const hasChanged = username && username !== originalUsername;

    if (!hasChanged && username.trim().length < 3) return null;
    return (
      <div className="flex flex-row items-center">
        {isValidatingUsername ? (
          <Spinner size="small" className="text-brand-60" />
        ) : (
          <P>
  {usernameValid === null
    ? ''
    : usernameValid
    ? '✅ Username available'
    : '❌ Username unavailable'}
</P>
        )}
        <P className="ml-1 text-grey-50 dark:text-grey-60">
          {isValidatingUsername
            ? 'Checking username'
            : usernameValid
            ? 'Username available'
            : 'Username unavailable'}
        </P>
      </div>
    );
  };

  return (
    <div className="flex-1 gap-4 px-4">
      <ControlledInput
        control={control}
        name="firstName"
        readOnly
        label="First name"
        icon={<UserFilledIcon color={colors.brand['60']} />}
      />
      <ControlledInput
        control={control}
        readOnly
        name="lastName"
        label="Last name"
        icon={<UserFilledIcon color={colors.brand['60']} />}
      />
      <ControlledInput
        control={control}
        name="email"
        readOnly
        label="Email"
        icon={<MessageFilledIcon color={colors.brand['60']} />}
      />
      <div>
        <ControlledInput
          control={control}
          name="username"
          label="Username"
          hideErrorMessage={
            formState.errors.username?.message === 'Username is already taken'
          }
          onChange={handleUsernameChange}
          icon={<AtIcon color={colors.brand['60']} />}
        />
        <UsernameStatusIndicator />
      </div>
    </div>
  );
};
