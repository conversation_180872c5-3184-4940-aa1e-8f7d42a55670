import React from 'react';
import { useFormContext } from 'react-hook-form';
import { type FormType as ForgotPasswordFormType } from '@/lib';
import { colors, ControlledInput, MessageFilledIcon, MessageIcon } from '@/components/ui';

export const ForgotPasswordForm = () => {
  const { control, watch } = useFormContext<ForgotPasswordFormType>();
  const emailValue = watch('email');

  const isFilled = emailValue?.trim().length > 0;

  return (
    <ControlledInput
      control={control}
      name="email"
      label="Email"
      icon={
        isFilled ? (
          <MessageFilledIcon color={colors.brand['60']} />
        ) : (
          <MessageIcon color={colors.brand['60']} />
        )
      }
    />
  );
};