import React from 'react';
import { useFormContext } from 'react-hook-form';

import { ControlledInput } from '@/components/ui';
import { type PurchaseTicketFormType } from '@/lib/hooks';

export const AddDiscountForm = () => {
  const { control } = useFormContext<PurchaseTicketFormType>();

  return (
    <ControlledInput
      control={control}
      name="discountCode"
      label="Discount code"
    />
  );
};
