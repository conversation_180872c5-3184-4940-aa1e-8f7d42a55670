'use client';

import React from 'react';
import clsx from 'clsx';
import { motion, AnimatePresence } from 'framer-motion';
import { IconType } from '@/types';
import IconComponent from '../common/icon-component';

const SHARE_OPTIONS = [
  { id: 'copy', name: 'Copy', iconType: 'material' as IconType, iconName: 'link' },
  { id: 'message', name: 'Message', iconType: 'material' as IconType, iconName: 'mail-outline' },
  { id: 'whatsapp', name: 'WhatsApp', iconType: 'ionicons' as IconType, iconName: 'logo-whatsapp', bgColor: '#25D366' },
  { id: 'instagram', name: 'Instagram', iconType: 'custom-svg' as IconType, iconName: 'Instagram', bgColor: '#DA337A' },
  { id: 'twitter', name: 'X (Twitter)', iconType: 'custom-svg' as IconType, iconName: 'X', bgColor: '#3D3D3D' },
  { id: 'facebook', name: 'Facebook', iconType: 'ionicons' as IconType, iconName: 'logo-facebook', bgColor: '#1877F2' },
  { id: 'menu', name: 'More', size: '16', color: '#5A2D82', iconType: 'entypo' as IconType, iconName: 'dots-three-horizontal', bgColor: '#EDE9FE' },
];

interface ShareModalProps {
  visible: boolean;
  onDismiss: () => void;
  content: string;
}

export const ShareModal: React.FC<ShareModalProps> = ({ visible, onDismiss, content }) => {
  const handleShare = async (platform: string) => {
    onDismiss();

    try {
      switch (platform) {
        case 'copy':
          await navigator.clipboard.writeText(content);
          console.log('Link copied!');
          break;
        case 'message':
          window.location.href = `sms:&body=${encodeURIComponent(content)}`;
          break;
        case 'whatsapp':
          window.open(`https://wa.me/?text=${encodeURIComponent(content)}`, '_blank');
          break;
        case 'twitter':
          window.open(`https://twitter.com/intent/tweet?text=${encodeURIComponent(content)}`, '_blank');
          break;
        case 'facebook':
          window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(content)}`, '_blank');
          break;
        case 'instagram':
        case 'menu':
          if (navigator.share) {
            await navigator.share({ text: content });
          } else {
            console.warn('Sharing not supported on this browser.');
          }
          break;
        default:
          console.warn(`${platform} sharing is not supported.`);
      }
    } catch (error) {
      console.error('Error sharing:', error);
    }
  };

  return (
    <AnimatePresence>
      {visible && (
        <motion.div
          role="dialog"
          aria-modal="true"
          className="fixed inset-0 z-50 flex items-end bg-black/50"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={onDismiss}
        >
          <motion.div
            className="w-full max-w-lg flex flex-wrap justify-center gap-7 rounded-t-lg bg-white p-6 dark:bg-gray-900"
            initial={{ y: '100%' }}
            animate={{ y: 0 }}
            exit={{ y: '100%' }}
            transition={{ type: 'spring', stiffness: 300, damping: 30 }}
            onClick={(e) => e.stopPropagation()}
          >
            {SHARE_OPTIONS.map((option) => (
              <button
                key={option.id}
                onClick={() => handleShare(option.id)}
                className="flex flex-col items-center justify-center gap-2 px-px"
                aria-label={`Share via ${option.name}`}
              >
                <div
                  className={clsx(
                    'flex h-12 w-12 items-center justify-center rounded-full',
                    'bg-purple-500 dark:bg-purple-600'
                  )}
                  style={{ backgroundColor: option.bgColor ?? undefined }}
                >
                  <IconComponent
                    iconType={option.iconType}
                    iconName={option.iconName}
                    size={option.size ? parseInt(option.size, 10) : 24}
                    color={option.color ?? '#fff'}
                  />
                </div>
                <span className="text-center text-xs font-normal text-gray-500 dark:text-gray-300">
                  {option.name}
                </span>
              </button>
            ))}
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};