'use client';
import { useState, useCallback, useMemo } from 'react';
import { GoogleMap, useJsApi<PERSON>oa<PERSON>, <PERSON><PERSON> } from '@react-google-maps/api';
import { Button } from '@/components/ui';

const containerStyle = {
  width: '100%',
  height: '400px',
};

const center = {
  lat: 6.5244, // Default latitude (San Francisco)
  lng: 3.3792, // Default longitude (San Francisco)
};

type LatLng = {
  lat: number;
  lng: number;
};

// Extract Google Maps API key as a constant to keep it stable
const googleMapsApiKey = process.env.NEXT_PUBLIC_GOOGLE_MAP_API_KEY || '';

const LocationPickerModal = ({onSelect}:{onSelect: (v:any) => void}) => {
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [selectedLocation, setSelectedLocation] = useState<LatLng>(center);
  const [address, setAddress] = useState<string>('');
  const [place, setPlace] = useState<any>();

  // const loaderOptions = useMemo(() => ({
  //   googleMapsApiKey,
  // }), []);

  // const { isLoaded } = useJsApiLoader(loaderOptions);

  const onMapClick = useCallback(async (event: google.maps.MapMouseEvent) => {
    if (event.latLng) {
      const newLocation = {
        lat: event.latLng.lat(),
        lng: event.latLng.lng(),
      };
      setSelectedLocation(newLocation);

      // Fetch the address using the Geocoding API
      try {
        const response = await fetch(
          `https://maps.googleapis.com/maps/api/geocode/json?latlng=${newLocation.lat},${newLocation.lng}&key=${process.env.NEXT_PUBLIC_GOOGLE_MAP_API_KEY}`
        );
        const data = await response.json();

        if (data.status === 'OK' && data.results.length > 0) {
          setAddress(data.results[0].formatted_address);
          setPlace(data.results[0])
        } else {
          setAddress('');
        }
      } catch (error) {
        console.error('Error fetching address:', error);
      }
    }
  }, []);

  const openModal = () => setIsModalOpen(true);
  const closeModal = () => {
    if(onSelect && place){
      onSelect(place)
    }
    setIsModalOpen(false);
  } 

  // if (!isLoaded) {
  //   return <div className="text-center">Loading...</div>;
  // }

  const customMarkerIcon = {
    path: 'M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5a2.5 2.5 0 110-5 2.5 2.5 0 010 5z',
    fillColor: '#6600CC',
    fillOpacity: 1,
    scale: 2,
    strokeColor: '#6600CC',
    strokeWeight: 2,
  };

  return (
    <>
      <p onClick={openModal} className=' max-w-max cursor-pointer'>+ Pick location on map </p>
      {isModalOpen && (
        <div className="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50">
          <div className="bg-white rounded-lg w-full max-w-lg mx-4 sm:mx-auto p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold">Select Location</h2>
              <button
                onClick={closeModal}
                className="text-gray-600 hover:text-gray-800 focus:outline-hidden"
              >
                ✕
              </button>
            </div>

            <div className="mb-4">
              <GoogleMap
                mapContainerStyle={containerStyle}
                center={selectedLocation}
                zoom={18}
                onClick={onMapClick}
              >
                <Marker position={selectedLocation} />
              </GoogleMap>
            </div>

            <div className="mt-4">
              <h3 className="text-lg font-semibold">Selected Location</h3>
              {/* <p className="text-sm text-gray-800">
                Latitude: {selectedLocation.lat}, Longitude: {selectedLocation.lng}
              </p> */}
              <p className="text-sm text-gray-800 mt-2">
                {address}
              </p>
            </div>

            <div className="flex justify-end mt-4">
              <Button
                // size='lg'
                className='rounded-full md:w-[200px] h-10'
                onClick={closeModal}
              >
                Confirm Location
              </Button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default LocationPickerModal;
