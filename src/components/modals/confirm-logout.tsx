'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { useQueryClient } from '@tanstack/react-query';

import { useAuth } from '@/lib';

import { Button, H2, P } from '../ui';

interface ConfirmLogoutModalProps {
  visible: boolean;
  onClose: () => void;
}

const ConfirmLogoutModal: React.FC<ConfirmLogoutModalProps> = ({
  visible,
  onClose,
}) => {
  const router = useRouter();
  const queryClient = useQueryClient();
  const signOut = useAuth.use.signOut();

  if (!visible) return null;

  return (
    <div
      aria-modal="true"
      role="dialog"
      className="fixed inset-0 z-50 flex items-center justify-center"
      onClick={onClose}
    >
      {/* Backdrop with blur and semi-transparent overlay */}
      <div
        className="absolute inset-0 bg-black bg-opacity-70 backdrop-blur-xs"
        onClick={(e) => e.stopPropagation()}
      />

      {/* Modal content */}
      <div
        className="relative z-10 w-full max-w-md rounded-lg bg-white p-8 dark:bg-gray-900 flex flex-col items-center gap-7"
        onClick={(e) => e.stopPropagation()}
      >
        <H2>Are you sure?</H2>
        <P className="text-gray-600 dark:text-gray-300">You will be logged out</P>

        <div className="flex w-full gap-4 px-5">
          <Button
            label="Cancel"
            className="flex-1 bg-brand-100 dark:bg-brand-900 text-brand-700 dark:text-brand-400"
            onPress={onClose}
          />
          <Button
            label="Logout"
            className="flex-1 bg-red-600 text-white hover:bg-red-700"
            onPress={() => {
              signOut();
              queryClient.clear();
              router.replace('/onboarding');
            }}
          />
        </div>
      </div>
    </div>
  );
};

export default ConfirmLogoutModal;