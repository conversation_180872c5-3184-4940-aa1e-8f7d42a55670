import { FC } from 'react';

interface DeleteActionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onDelete: () => void;
}

const DeleteActionModal: FC<DeleteActionModalProps> = ({ isOpen, onClose, onDelete }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
      <div className="bg-white rounded-lg p-6 max-w-sm mx-auto text-center shadow-lg">
        <h2 className="text-lg font-semibold mb-2">Delete Event</h2>
        <p className="text-gray-600 mb-4">
          Are you sure you want to delete this Event? This action cannot be undone.
        </p>
        <div className="flex justify-between space-x-4">
          <button
            className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-full w-full"
            onClick={onClose}
          >
            Cancel
          </button>
          <button
            className="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-full w-full"
            onClick={onDelete}
          >
            Delete
          </button>
        </div>
      </div>
    </div>
  );
};

export default DeleteActionModal;
