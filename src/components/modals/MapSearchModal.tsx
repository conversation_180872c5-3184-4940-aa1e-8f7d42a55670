import React, { useState, useRef } from 'react';
import { Load<PERSON>, GoogleMap, Marker, Autocomplete } from '@react-google-maps/api';
import Modal from 'react-modal';

const containerStyle = {
  width: '100%',
  height: '400px',
};

const defaultCenter = {
  lat: 37.7749, // Default Latitude
  lng: -122.4194, // Default Longitude
};

interface LocationData {
  address: string;
  street: string;
  city: string;
  state: string;
  country: string;
  coordinates: { lat: number; lng: number };
  description: string;
  landmark: string;
}

interface MapSearchModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectLocation: (data: LocationData) => void;
}

const MapSearchModal: React.FC<MapSearchModalProps> = ({ isOpen, onClose, onSelectLocation }) => {
  const [markerPosition, setMarkerPosition] = useState(defaultCenter);
  const [locationDetails, setLocationDetails] = useState<LocationData | null>(null);
  const autocompleteRef = useRef<google.maps.places.Autocomplete | null>(null);
  const mapRef = useRef<google.maps.Map | null>(null);

  const handlePlaceSelected = (place: google.maps.places.PlaceResult) => {
    const lat = place.geometry?.location?.lat() || 0;
    const lng = place.geometry?.location?.lng() || 0;
    setMarkerPosition({ lat, lng });

    const addressComponents = place.address_components || [];
    const getAddressComponent = (type: string) =>
      addressComponents.find((component) => component.types.includes(type))?.long_name || '';

    const locationData: LocationData = {
      address: place.formatted_address || '',
      street: getAddressComponent('route'),
      city: getAddressComponent('locality'),
      state: getAddressComponent('administrative_area_level_1'),
      country: getAddressComponent('country'),
      coordinates: { lat, lng },
      description: place.name || '',
      landmark: place.types?.[0] || '',
    };

    setLocationDetails(locationData);
  };

  const handleMapClick = async (e: google.maps.MapMouseEvent) => {
    if (e.latLng && mapRef.current) {
      const lat = e.latLng.lat();
      const lng = e.latLng.lng();
      setMarkerPosition({ lat, lng });
  
      const geocoder = new google.maps.Geocoder();
      const placesService = new google.maps.places.PlacesService(mapRef.current);
  
      // Reverse Geocode to get the address
      const response = await geocoder.geocode({ location: { lat, lng } });
      
      let locationData: LocationData = {
        address: '',
        street: '',
        city: '',
        state: '',
        country: '',
        coordinates: { lat, lng },
        description: '',
        landmark: '',
      };
  
      if (response.results.length > 0) {
        const addressResult = response.results[0];
        const placeId = addressResult.place_id; // Extract the Place ID for further details
  
        // Fetch detailed information using Places Service
        if (placeId) {
          placesService.getDetails({ placeId }, (place, status) => {
            if (status === google.maps.places.PlacesServiceStatus.OK && place) {
              // Use the exact name from the place details as description
              locationData = {
                address: addressResult.formatted_address || '',
                street: place.address_components?.find((component) => component.types.includes('route'))?.long_name || '',
                city: place.address_components?.find((component) => component.types.includes('locality'))?.long_name || '',
                state: place.address_components?.find((component) => component.types.includes('administrative_area_level_1'))?.long_name || '',
                country: place.address_components?.find((component) => component.types.includes('country'))?.long_name || '',
                coordinates: { lat, lng },
                description: place.name || `Selected Location (${lat.toFixed(5)}, ${lng.toFixed(5)})`, // Prioritize place name
                landmark: place.name || '',
              };
  
              // Update state with location details
              setLocationDetails(locationData);
            }
          });
        } else {
          // Fallback if Place ID is not found
          locationData.description = `Selected Location (${lat.toFixed(5)}, ${lng.toFixed(5)})`;
          setLocationDetails(locationData);
        }
      }
    }
  };
  
  
  

  const handleConfirmLocation = () => {
    if (locationDetails) {
      onSelectLocation(locationDetails);
      onClose();
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onRequestClose={onClose}
      contentLabel="Map Search"
      className="flex items-center justify-center w-full h-full bg-gray-700 bg-opacity-75"
    >
      <div className="bg-white p-4 rounded-lg max-w-xl w-full">
        <h2 className="text-lg font-semibold mb-4">Select Location</h2>
        {TextTrackCueList ? (
          <div>
            <Autocomplete
              onLoad={(autocomplete) => (autocompleteRef.current = autocomplete)}
              onPlaceChanged={() => {
                const place = autocompleteRef.current?.getPlace();
                if (place) {
                  handlePlaceSelected(place);
                }
              }}
            >
              <input
                type="text"
                placeholder="Search a location"
                className="w-full p-2 mb-2 border rounded"
              />
            </Autocomplete>
            <GoogleMap
              mapContainerStyle={containerStyle}
              center={markerPosition}
              zoom={14}
              // onLoad={(map) => (mapRef.current = map)}
              onLoad={(map) => {
                mapRef.current = map;
              }}
              onClick={handleMapClick}
            >
              <Marker position={markerPosition} />
            </GoogleMap>
          </div>
        ) : (
          <p>Loading Map...</p>
        )}
        <div className="mt-4">
          {locationDetails && (
            <div className="mb-4">
              <p><strong>Address:</strong> {locationDetails.address}</p>
              <p><strong>Street:</strong> {locationDetails.street}</p>
              <p><strong>City:</strong> {locationDetails.city}</p>
              <p><strong>State:</strong> {locationDetails.state}</p>
              <p><strong>Country:</strong> {locationDetails.country}</p>
              <p><strong>Description:</strong> {locationDetails.description}</p>
              <p><strong>Landmark:</strong> {locationDetails.landmark}</p>
            </div>
          )}
          <button
            onClick={handleConfirmLocation}
            className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
          >
            Confirm Location
          </button>
          <button
            onClick={onClose}
            className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600 ml-2"
          >
            Cancel
          </button>
        </div>
      </div>
    </Modal>
  );
};

export default MapSearchModal;
