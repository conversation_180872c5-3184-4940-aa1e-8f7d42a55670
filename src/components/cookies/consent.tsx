'use client';
import { useState, useEffect } from 'react';
import './cookieConsent.css';
import { Button } from '@/components/ui/button';
import Link from 'next/link';

const CookieConsent = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const hasConsented = localStorage.getItem('cookieConsent');
    if (!hasConsented) {
      setIsVisible(true);
    }
  }, []);

  const handleAccept = () => {
    localStorage.setItem('cookieConsent', 'true');
    setIsVisible(false);
  };

  const handleDeny = () => {
    localStorage.setItem('cookieConsent', 'false');
    setIsVisible(false);
  };

  if (!isVisible) return null;

  return (
    <div className='md:flex-row flex-col md:flex justify-center gap-[40px] fixed bottom-0 left-0 right-0 z-50 bg-[#D0C9EA] py-4 px-[20px] rounded-t-lg shadow-lg transition-transform transform translate-y-full animate-slideUp '>
      <div className='w-full md:max-w-[700px] text-[#616161] text-[12px]'>
        By clicking <strong>"Accept All Cookies"</strong>, you agree to the
        storing of cookies on your device to enhance site navigation, analyze
        site usage, and assist in our marketing efforts. View our
        <span className='border-b-2 border-[#6600CC] px-[5px]'>
          <Link
            href='/policy#privacy_policy'
            className='fs-cc-banner_text-link'
          >
            Privacy Policy
          </Link>
        </span>
        for more information.
      </div>
      <div className='flex md:flex-row flex-col md:flex w-full md:max-w-[500px] md:gap-[20px] justify-center items-center gap-[4px] mt-[10px]'>
        {/* <span className='border-b-2 border-[#6600CC] md:block hidden'>
          <Link href='/privacy-policy' className='fs-cc-banner_text-link'>
            Preferences
          </Link>
        </span> */}
        <button
          onClick={handleDeny}
          className='outline rounded-[20px] py-[8px] text-[14px] md:w-[150px] w-full font-bold text-[#6600CC]'
        >
          Deny
        </button>
        <Button
          onClick={handleAccept}
          className='text-[14px] font-bold text-white  md:w-[150px] w-full'
        >
          Accept
        </Button>
        {/* <span className='border-b-2 border-[#6600CC] md:hidden'>
          <Link href='/privacy-policy' className='fs-cc-banner_text-link'>
            Preferences
          </Link>
        </span> */}
      </div>
    </div>
  );
};

export default CookieConsent;
