import { useEffect, useState } from "react";
import { Icons } from '@/components/icons/icons';
import { Button } from '@/components/button/button';


type GoogleLoginProps = {
  onLoginSuccess: (code: string) => void;
  text: string;
  loading?: boolean;
};

const GoogleLogin: React.FC<GoogleLoginProps> = ({ onLoginSuccess, text, loading = false }) => {
  useEffect(() => {
    // Load the Google Identity Services script
    const script = document.createElement("script");
    script.src = "https://accounts.google.com/gsi/client";
    script.async = true;
    script.defer = true;
    document.body.appendChild(script);

    return () => {
      document.body.removeChild(script);
    };
  }, []);

  const handleGoogleSignInPopup = () => {
    if (window.google) {
      const client = window.google.accounts.oauth2.initCodeClient({
        client_id: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID!,
        scope: "openid email profile",
        ux_mode: "popup",
        redirect_uri: process.env.NEXT_PUBLIC_GOOGLE_REDIRECT_URL!,
        callback: (response: any) => {
          if (response.code) {
            onLoginSuccess(response.code);
          }
        },
      });

      client.requestCode();
    }
  };

  return (
    <Button onClick={handleGoogleSignInPopup} type='button' disabled={loading} loading={loading} variant='outline' className='mt-2 w-full'>
      <Icons.google_color className='mr-2 w-5' />
      {text}
    </Button>
  );
};

export default GoogleLogin;
