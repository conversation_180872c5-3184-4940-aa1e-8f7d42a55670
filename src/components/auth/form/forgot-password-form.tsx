import UnstyledLink from '@/components/links/UnstyledLink';
import { Button } from '@/components/ui/button';
import { FloatingInput } from '@/components/ui/floating-input';
import { H1 } from '@/components/ui/typography';
import { EmailSentDialog } from '@/components/auth/dialog/email-sent';
import { cn } from '@/lib/utils';
import React from 'react';

export function ForgotPasswordForm({
  className,
  ...props
}: React.ComponentPropsWithoutRef<'form'>) {
  const [showEmailSent, setShowEmailSent] = React.useState(false);

  return (
    <React.Fragment>
      <form className={cn('flex flex-col gap-8', className)} {...props}>
        <H1 themed weight='bold'>
          Reset your password
        </H1>
        <div className='flex flex-col gap-6'>
          <FloatingInput type='email' label='Email' required />
          <Button onClick={() => setShowEmailSent(true)} type='button'>
            Send reset instruction
          </Button>
        </div>

        <div className='text-center text-fg-subtle-light dark:text-fg-subtle-dark text-sm mt-2'>
          Back to{' '}
          <UnstyledLink href='/login' className='text-accent-moderate'>
            Log in
          </UnstyledLink>
        </div>
      </form>
      <EmailSentDialog open={showEmailSent} setOpen={setShowEmailSent} />
    </React.Fragment>
  );
}
