import UnstyledLink from '@/components/links/UnstyledLink';
import { Button } from '@/components/ui/button';
import { FloatingInput } from '@/components/ui/floating-input';
import { H1, Small } from '@/components/ui/typography';
import { EmailVerificationDialog } from '@/components/auth/dialog/email-verification';
import { cn } from '@/lib/utils';
import React from 'react';
import AppleIcon from '~/svg/socials/apple.svg';
import FacebookIcon from '~/svg/socials/facebook.svg';

export function RegisterForm({
  className,
  ...props
}: React.ComponentPropsWithoutRef<'form'>) {
  const [showEmailVerification, setShowEmailVerification] =
    React.useState(false);

  return (
    <React.Fragment>
      <form className={cn('flex flex-col gap-8', className)} {...props}>
        <H1 themed weight='bold'>
          Join the vibe!
        </H1>
        <div className='grid gap-6'>
          <FloatingInput
            type='email'
            placeholder='<EMAIL>'
            label='Email'
            required
          />
          <div className='grid gap-4'>
            <Small themed className='max-w-[331px]' weight='regular'>
              By signing up, you agree to our{' '}
              <Small themed asChild className='underline'>
                <a href='#'>Terms</a>
              </Small>{' '}
              and have read and acknowledge the{' '}
              <Small themed asChild className='underline'>
                <a href='#'>Privacy Policies</a>
              </Small>
              .
            </Small>
            <Button
              className='h-12 bg-accent-moderate rounded-full font-bold text-base/none py-4 text-white'
              onClick={() => setShowEmailVerification(true)}
              type='button'
            >
              Sign up
            </Button>
          </div>
        </div>

        <div className='flex flex-col gap-4'>
          <Button className='h-12 justify-between bg-social-apple-primary-light rounded-full dark:text-grey-100 font-bold text-base/none dark:bg-social-apple-primary-dark text-white hover:bg-social-apple-tertiary-dark'>
            <AppleIcon />
            Continue with Apple
            <div className='size-6' />
          </Button>
          <Button className='h-12 justify-between bg-social-facebook-primary rounded-full text-white font-bold text-base/none hover:bg-social-facebook-secondary'>
            <FacebookIcon />
            Continue with Facebook
            <div className='size-6' />
          </Button>
        </div>

        <div className='text-center text-fg-subtle-light dark:text-fg-subtle-dark text-sm mt-2'>
          Have an account?{' '}
          <UnstyledLink href='/login' className='text-accent-moderate'>
            Log in
          </UnstyledLink>
        </div>
      </form>
      <EmailVerificationDialog
        open={showEmailVerification}
        setOpen={setShowEmailVerification}
      />
    </React.Fragment>
  );
}
