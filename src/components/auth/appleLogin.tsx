import { Icons } from '@/components/icons/icons';
import { Button } from '@/components/ui';
import AppleSignin from 'react-apple-signin-auth';

type AppleLoginProps = {
  onLoginSuccess: (res: any) => void;
  text: string;
  loading?: boolean;
};

const AppleLogin: React.FC<AppleLoginProps> = ({ onLoginSuccess, text, loading = false }) => {
  return (
    <AppleSignin
      authOptions={{
        clientId: process.env.NEXT_PUBLIC_APPLE_CLIENT_ID!,
        scope: "email name",
        redirectURI: process.env.NEXT_PUBLIC_APPLE_REDIRECT_URL!,
        state: "state",
        usePopup: true,
      }}
      onSuccess={(response: any) => {
        if (response.authorization?.code) {
          onLoginSuccess(response);
        }
      }}
      onError={(error: any) => console.error("Apple Sign In Error:", error)}
      render={(props: any) => (
        <Button {...props} type='button'  disabled={loading} loading={loading} variant='outline' className='mt-5 w-full'>
          <Icons.apple className='mr-2 w-5' />
          {text}
        </Button>
      )}
      uiType="light"
    />
  );
};

export default AppleLogin;