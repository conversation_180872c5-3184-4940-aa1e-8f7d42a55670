import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/dialogs/dialog';
import { FloatingInput } from '@/components/ui/floating-input';
import { P } from '@/components/ui/typography';

export function EmailVerificationDialog({
  open,
  setOpen,
}: {
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
}) {
  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <form>
        <DialogContent className='sm:max-w-[425px] p-6 rounded-2xl! bg-bg-canvas-light dark:bg-bg-canvas-dark border-0'>
          <DialogHeader className='space-y-4'>
            <DialogTitle>Check your inbox!</DialogTitle>
            <DialogDescription>
              We sent you a verification code.
            </DialogDescription>
          </DialogHeader>
          <div className='grid gap-2.5'>
            <FloatingInput
              type='number'
              placeholder='123456'
              label='Verification code'
              required
            />
            <P className='text-fg-muted-light dark:text-fg-muted-dark'>
              Didn’t receive it?{' '}
              <P as='span' asChild className='text-accent-moderate'>
                <a href='#'>Tap to resend</a>
              </P>
            </P>
          </div>
          <DialogFooter className='mt-2'>
            <DialogClose asChild>
              <Button variant='secondary'>Cancel</Button>
            </DialogClose>
            <Button type='submit'>Save changes</Button>
          </DialogFooter>
        </DialogContent>
      </form>
    </Dialog>
  );
}
