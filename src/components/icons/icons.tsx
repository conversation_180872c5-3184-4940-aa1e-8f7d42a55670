import { DownloadIcon } from '@radix-ui/react-icons';
import { BsBackpack } from 'react-icons/bs';

type IconProps = React.HTMLAttributes<SVGElement>;

export const Icons = {
  logo: (props: IconProps) => (
    <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 256 256' {...props}>
      <rect width='256' height='256' fill='none' />
      <line
        x1='208'
        y1='128'
        x2='128'
        y2='208'
        fill='none'
        stroke='currentColor'
        strokeLinecap='round'
        strokeLinejoin='round'
        strokeWidth='16'
      />
      <line
        x1='192'
        y1='40'
        x2='40'
        y2='192'
        fill='none'
        stroke='currentColor'
        strokeLinecap='round'
        strokeLinejoin='round'
        strokeWidth='16'
      />
    </svg>
  ),

  barcode: (props: IconProps) => (
    <svg
      width='80'
      height='80'
      viewBox='0 0 80 80'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M0 0H26.6667V3.80952H0V0ZM45.7143 0H49.5238V3.80952H45.7143V0ZM53.3333 0H80V3.80952H53.3333V0ZM0 3.80952H3.80952V7.61905H0V3.80952ZM22.8571 3.80952H26.6667V7.61905H22.8571V3.80952ZM34.2857 3.80952H38.0952V7.61905H34.2857V3.80952ZM53.3333 3.80952H57.1429V7.61905H53.3333V3.80952ZM76.1905 3.80952H80V7.61905H76.1905V3.80952ZM0 7.61905H3.80952V11.4286H0V7.61905ZM7.61905 7.61905H19.0476V11.4286H7.61905V7.61905ZM22.8571 7.61905H26.6667V11.4286H22.8571V7.61905ZM30.4762 7.61905H34.2857V11.4286H30.4762V7.61905ZM41.9048 7.61905H45.7143V11.4286H41.9048V7.61905ZM53.3333 7.61905H57.1429V11.4286H53.3333V7.61905ZM60.9524 7.61905H72.381V11.4286H60.9524V7.61905ZM76.1905 7.61905H80V11.4286H76.1905V7.61905ZM0 11.4286H3.80952V15.2381H0V11.4286ZM7.61905 11.4286H19.0476V15.2381H7.61905V11.4286ZM22.8571 11.4286H26.6667V15.2381H22.8571V11.4286ZM30.4762 11.4286H41.9048V15.2381H30.4762V11.4286ZM45.7143 11.4286H49.5238V15.2381H45.7143V11.4286ZM53.3333 11.4286H57.1429V15.2381H53.3333V11.4286ZM60.9524 11.4286H72.381V15.2381H60.9524V11.4286ZM76.1905 11.4286H80V15.2381H76.1905V11.4286ZM0 15.2381H3.80952V19.0476H0V15.2381ZM7.61905 15.2381H19.0476V19.0476H7.61905V15.2381ZM22.8571 15.2381H26.6667V19.0476H22.8571V15.2381ZM30.4762 15.2381H49.5238V19.0476H30.4762V15.2381ZM53.3333 15.2381H57.1429V19.0476H53.3333V15.2381ZM60.9524 15.2381H72.381V19.0476H60.9524V15.2381ZM76.1905 15.2381H80V19.0476H76.1905V15.2381ZM0 19.0476H3.80952V22.8571H0V19.0476ZM22.8571 19.0476H26.6667V22.8571H22.8571V19.0476ZM30.4762 19.0476H34.2857V22.8571H30.4762V19.0476ZM45.7143 19.0476H49.5238V22.8571H45.7143V19.0476ZM53.3333 19.0476H57.1429V22.8571H53.3333V19.0476ZM76.1905 19.0476H80V22.8571H76.1905V19.0476ZM0 22.8571H26.6667V26.6667H0V22.8571ZM30.4762 22.8571H34.2857V26.6667H30.4762V22.8571ZM38.0952 22.8571H41.9048V26.6667H38.0952V22.8571ZM45.7143 22.8571H49.5238V26.6667H45.7143V22.8571ZM53.3333 22.8571H80V26.6667H53.3333V22.8571ZM30.4762 26.6667H34.2857V30.4762H30.4762V26.6667ZM38.0952 26.6667H49.5238V30.4762H38.0952V26.6667ZM0 30.4762H3.80952V34.2857H0V30.4762ZM7.61905 30.4762H26.6667V34.2857H7.61905V30.4762ZM34.2857 30.4762H41.9048V34.2857H34.2857V30.4762ZM45.7143 30.4762H49.5238V34.2857H45.7143V30.4762ZM53.3333 30.4762H72.381V34.2857H53.3333V30.4762ZM0 34.2857H3.80952V38.0952H0V34.2857ZM15.2381 34.2857H22.8571V38.0952H15.2381V34.2857ZM26.6667 34.2857H30.4762V38.0952H26.6667V34.2857ZM38.0952 34.2857H41.9048V38.0952H38.0952V34.2857ZM45.7143 34.2857H49.5238V38.0952H45.7143V34.2857ZM57.1429 34.2857H72.381V38.0952H57.1429V34.2857ZM76.1905 34.2857H80V38.0952H76.1905V34.2857ZM7.61905 38.0952H15.2381V41.9048H7.61905V38.0952ZM19.0476 38.0952H26.6667V41.9048H19.0476V38.0952ZM34.2857 38.0952H45.7143V41.9048H34.2857V38.0952ZM49.5238 38.0952H53.3333V41.9048H49.5238V38.0952ZM64.7619 38.0952H76.1905V41.9048H64.7619V38.0952ZM0 41.9048H7.61905V45.7143H0V41.9048ZM15.2381 41.9048H22.8571V45.7143H15.2381V41.9048ZM34.2857 41.9048H41.9048V45.7143H34.2857V41.9048ZM57.1429 41.9048H60.9524V45.7143H57.1429V41.9048ZM64.7619 41.9048H80V45.7143H64.7619V41.9048ZM22.8571 45.7143H30.4762V49.5238H22.8571V45.7143ZM38.0952 45.7143H45.7143V49.5238H38.0952V45.7143ZM53.3333 45.7143H57.1429V49.5238H53.3333V45.7143ZM60.9524 45.7143H68.5714V49.5238H60.9524V45.7143ZM72.381 45.7143H76.1905V49.5238H72.381V45.7143ZM30.4762 49.5238H38.0952V53.3333H30.4762V49.5238ZM45.7143 49.5238H72.381V53.3333H45.7143V49.5238ZM76.1905 49.5238H80V53.3333H76.1905V49.5238ZM0 53.3333H26.6667V57.1429H0V53.3333ZM38.0952 53.3333H41.9048V57.1429H38.0952V53.3333ZM45.7143 53.3333H49.5238V57.1429H45.7143V53.3333ZM53.3333 53.3333H57.1429V57.1429H53.3333V53.3333ZM68.5714 53.3333H76.1905V57.1429H68.5714V53.3333ZM0 57.1429H3.80952V60.9524H0V57.1429ZM22.8571 57.1429H26.6667V60.9524H22.8571V57.1429ZM30.4762 57.1429H53.3333V60.9524H30.4762V57.1429ZM57.1429 57.1429H60.9524V60.9524H57.1429V57.1429ZM64.7619 57.1429H72.381V60.9524H64.7619V57.1429ZM0 60.9524H3.80952V64.7619H0V60.9524ZM7.61905 60.9524H19.0476V64.7619H7.61905V60.9524ZM22.8571 60.9524H26.6667V64.7619H22.8571V60.9524ZM30.4762 60.9524H34.2857V64.7619H30.4762V60.9524ZM38.0952 60.9524H45.7143V64.7619H38.0952V60.9524ZM53.3333 60.9524H60.9524V64.7619H53.3333V60.9524ZM64.7619 60.9524H68.5714V64.7619H64.7619V60.9524ZM72.381 60.9524H80V64.7619H72.381V60.9524ZM0 64.7619H3.80952V68.5714H0V64.7619ZM7.61905 64.7619H19.0476V68.5714H7.61905V64.7619ZM22.8571 64.7619H26.6667V68.5714H22.8571V64.7619ZM30.4762 64.7619H34.2857V68.5714H30.4762V64.7619ZM38.0952 64.7619H41.9048V68.5714H38.0952V64.7619ZM60.9524 64.7619H64.7619V68.5714H60.9524V64.7619ZM68.5714 64.7619H72.381V68.5714H68.5714V64.7619ZM0 68.5714H3.80952V72.381H0V68.5714ZM7.61905 68.5714H19.0476V72.381H7.61905V68.5714ZM22.8571 68.5714H26.6667V72.381H22.8571V68.5714ZM30.4762 68.5714H57.1429V72.381H30.4762V68.5714ZM68.5714 68.5714H72.381V72.381H68.5714V68.5714ZM0 72.381H3.80952V76.1905H0V72.381ZM22.8571 72.381H26.6667V76.1905H22.8571V72.381ZM34.2857 72.381H45.7143V76.1905H34.2857V72.381ZM49.5238 72.381H53.3333V76.1905H49.5238V72.381ZM60.9524 72.381H72.381V76.1905H60.9524V72.381ZM0 76.1905H26.6667V80H0V76.1905ZM30.4762 76.1905H45.7143V80H30.4762V76.1905ZM53.3333 76.1905H57.1429V80H53.3333V76.1905ZM60.9524 76.1905H64.7619V80H60.9524V76.1905ZM72.381 76.1905H76.1905V80H72.381V76.1905Z'
        fill='black'
      />
    </svg>
  ),

  walletIcon: (props: IconProps) => (
    <svg
      width='14'
      height='15'
      viewBox='0 0 14 15'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M9.66667 5.33322V3.00032C9.66667 2.44583 9.66667 2.16858 9.54986 1.9982C9.44781 1.84934 9.28976 1.74822 9.11184 1.71794C8.90819 1.68328 8.65646 1.79947 8.153 2.03183L2.23934 4.76121C1.79034 4.96845 1.56583 5.07206 1.4014 5.23276C1.25604 5.37483 1.14508 5.54825 1.077 5.73977C1 5.95641 1 6.20367 1 6.69819V9.99988M10 9.66655H10.0067M1 7.46655L1 11.8666C1 12.6133 1 12.9867 1.14532 13.2719C1.27316 13.5228 1.47713 13.7267 1.72801 13.8546C2.01323 13.9999 2.3866 13.9999 3.13333 13.9999H10.8667C11.6134 13.9999 11.9868 13.9999 12.272 13.8546C12.5229 13.7267 12.7268 13.5228 12.8547 13.2719C13 12.9867 13 12.6133 13 11.8666V7.46655C13 6.71982 13 6.34645 12.8547 6.06123C12.7268 5.81035 12.5229 5.60638 12.272 5.47854C11.9868 5.33322 11.6134 5.33322 10.8667 5.33322L3.13333 5.33322C2.3866 5.33322 2.01323 5.33322 1.72801 5.47854C1.47713 5.60637 1.27316 5.81035 1.14532 6.06123C1 6.34645 1 6.71982 1 7.46655ZM10.3333 9.66655C10.3333 9.85065 10.1841 9.99988 10 9.99988C9.81591 9.99988 9.66667 9.85065 9.66667 9.66655C9.66667 9.48246 9.81591 9.33322 10 9.33322C10.1841 9.33322 10.3333 9.48246 10.3333 9.66655Z'
        stroke='#FDFDFD'
        stroke-width='1.5'
        stroke-linecap='round'
        stroke-linejoin='round'
      />
    </svg>
  ),

  headPhones: (props: IconProps) => (
    <svg
      width='16'
      height='14'
      viewBox='0 0 16 14'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M14.6654 10.3333V7.66667C14.6654 3.98477 11.6806 1 7.9987 1C4.3168 1 1.33203 3.98477 1.33203 7.66667V10.3333M4.9987 13C4.07822 13 3.33203 12.2538 3.33203 11.3333V9.33333C3.33203 8.41286 4.07822 7.66667 4.9987 7.66667C5.91917 7.66667 6.66536 8.41286 6.66536 9.33333V11.3333C6.66536 12.2538 5.91917 13 4.9987 13ZM10.9987 13C10.0782 13 9.33203 12.2538 9.33203 11.3333V9.33333C9.33203 8.41286 10.0782 7.66667 10.9987 7.66667C11.9192 7.66667 12.6654 8.41286 12.6654 9.33333V11.3333C12.6654 12.2538 11.9192 13 10.9987 13Z'
        stroke='#FDFDFD'
        stroke-width='1.5'
        stroke-linecap='round'
        stroke-linejoin='round'
      />
    </svg>
  ),

  dollarBag: (props: IconProps) => (
    <svg
      width='20'
      height='20'
      viewBox='0 0 20 20'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <g clip-path='url(#clip0_9166_61880)'>
        <path
          d='M6.66057 3.75H13.3406L14.7822 2.59667C15.2731 2.205 15.4564 1.56917 15.2489 0.975833C15.0414 0.3825 14.5022 0 13.8756 0H6.12557C5.49891 0 4.95974 0.383333 4.75224 0.975833C4.54474 1.56833 4.72807 2.205 5.21807 2.59583L6.66057 3.75Z'
          fill='url(#paint0_linear_9166_61880)'
        />
        <path
          d='M13.4542 5H6.54583C4.27 7.23417 2.5 11.1542 2.5 14.375C2.5 17.1742 3.98167 20 7.29167 20H12.9167C15.7442 20 17.5 17.8442 17.5 14.375C17.5 11.1542 15.73 7.23417 13.4542 5ZM9.68333 11.875H10.3167C11.2908 11.875 12.0833 12.6675 12.0833 13.6417C12.0833 14.5175 11.4517 15.2383 10.625 15.385V16.0408C10.625 16.3858 10.345 16.6658 10 16.6658C9.655 16.6658 9.375 16.3858 9.375 16.0408V15.4167H8.54167C8.19667 15.4167 7.91667 15.1367 7.91667 14.7917C7.91667 14.4467 8.19667 14.1667 8.54167 14.1667H10.3167C10.6017 14.1667 10.8333 13.935 10.8333 13.65C10.8333 13.3567 10.6017 13.125 10.3167 13.125H9.68333C8.70917 13.125 7.91667 12.3325 7.91667 11.3583C7.91667 10.4825 8.54833 9.76167 9.375 9.615V8.95833C9.375 8.61333 9.655 8.33333 10 8.33333C10.345 8.33333 10.625 8.61333 10.625 8.95833V9.58333H11.4583C11.8033 9.58333 12.0833 9.86333 12.0833 10.2083C12.0833 10.5533 11.8033 10.8333 11.4583 10.8333H9.68333C9.39833 10.8333 9.16667 11.065 9.16667 11.35C9.16667 11.6433 9.39833 11.875 9.68333 11.875Z'
          fill='url(#paint1_linear_9166_61880)'
        />
      </g>
      <defs>
        <linearGradient
          id='paint0_linear_9166_61880'
          x1='11.707'
          y1='4.7561'
          x2='6.15007'
          y2='-3.40354'
          gradientUnits='userSpaceOnUse'
        >
          <stop stop-color='#6600CC' />
          <stop offset='0.8' stop-color='#B098FF' />
        </linearGradient>
        <linearGradient
          id='paint1_linear_9166_61880'
          x1='12.4'
          y1='24.0244'
          x2='-7.0753'
          y2='13.9694'
          gradientUnits='userSpaceOnUse'
        >
          <stop stop-color='#6600CC' />
          <stop offset='0.8' stop-color='#B098FF' />
        </linearGradient>
        <clipPath id='clip0_9166_61880'>
          <rect width='20' height='20' fill='white' />
        </clipPath>
      </defs>
    </svg>
  ),
  twitter: (props: IconProps) => (
    <svg
      {...props}
      height='23'
      viewBox='0 0 1200 1227'
      width='23'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path d='M714.163 519.284L1160.89 0H1055.03L667.137 450.887L357.328 0H0L468.492 681.821L0 1226.37H105.866L515.491 750.218L842.672 1226.37H1200L714.137 519.284H714.163ZM569.165 687.828L521.697 619.934L144.011 79.6944H306.615L611.412 515.685L658.88 583.579L1055.08 1150.3H892.476L569.165 687.854V687.828Z' />
    </svg>
  ),

  money: (props: IconProps) => (
    <svg
      width='16'
      height='14'
      viewBox='0 0 16 14'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M3.9987 6.33366V9.00032M11.9987 5.00032V7.66699M11.332 1.66699C12.9645 1.66699 13.8475 1.91683 14.2868 2.11062C14.3453 2.13643 14.3745 2.14933 14.459 2.22991C14.5096 2.2782 14.602 2.41992 14.6257 2.48572C14.6654 2.59548 14.6654 2.65549 14.6654 2.77549V9.94109C14.6654 10.5469 14.6654 10.8499 14.5745 11.0056C14.4821 11.1639 14.393 11.2376 14.22 11.2984C14.0499 11.3583 13.7067 11.2923 13.0201 11.1604C12.5396 11.0681 11.9697 11.0003 11.332 11.0003C9.33203 11.0003 7.33203 12.3337 4.66536 12.3337C3.03289 12.3337 2.14994 12.0838 1.71062 11.89C1.65211 11.8642 1.62286 11.8513 1.53843 11.7707C1.48782 11.7224 1.39544 11.5807 1.37168 11.5149C1.33203 11.4052 1.33203 11.3452 1.33203 11.2252L1.33203 4.05956C1.33203 3.45372 1.33203 3.15079 1.42288 2.99509C1.5153 2.8367 1.60443 2.76307 1.7774 2.7022C1.94746 2.64236 2.29072 2.70831 2.97725 2.84022C3.45778 2.93255 4.02768 3.00032 4.66536 3.00032C6.66536 3.00032 8.66536 1.66699 11.332 1.66699ZM9.66536 7.00032C9.66536 7.9208 8.91917 8.66699 7.9987 8.66699C7.07822 8.66699 6.33203 7.9208 6.33203 7.00032C6.33203 6.07985 7.07822 5.33366 7.9987 5.33366C8.91917 5.33366 9.66536 6.07985 9.66536 7.00032Z'
        stroke='#FDFDFD'
        stroke-width='1.5'
        stroke-linecap='round'
        stroke-linejoin='round'
      />
    </svg>
  ),

  appleB: (props: IconProps) => (
    <svg
      width='18'
      height='18'
      viewBox='0 0 160 180'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <path
        fill-rule='evenodd'
        clip-rule='evenodd'
        d='M119.1 0C119.1 0 101.16 0.0800002 88.28 15.66H88.29C76.82 29.52 79.74 41.38 79.74 41.38C79.74 41.38 95.3 43.86 108.96 28.78C121.76 14.66 119.1 0 119.1 0ZM91.65 48.8C87.43 50.36 83.97 51.64 81.37 51.64C78.13 51.64 74.22 50.26 69.68 48.66C63.38 46.44 55.88 43.79 47.25 43.79C24.39 43.79 0 62.55 0 98.35C0 134.74 28.79 180 51.6 180C55.14 180 59.53 178.54 64.4 176.92C70.46 174.92 77.26 172.65 84.09 172.65C90.2 172.65 95.51 174.47 100.75 176.27C105.85 178.02 110.89 179.74 116.55 179.74C141.51 179.74 160 132.02 160 132.02C160 132.02 133.57 122.62 133.57 95.4C133.57 71.24 154.79 61.28 154.79 61.28C154.79 61.28 143.93 43.28 116.33 43.28C106.58 43.28 98.2 46.38 91.65 48.8Z'
        fill='white'
      />
    </svg>
  ),
  gitHub: (props: IconProps) => (
    <svg viewBox='0 0 438.549 438.549' {...props}>
      <path
        fill='currentColor'
        d='M409.132 114.573c-19.608-33.596-46.205-60.194-79.798-79.8-33.598-19.607-70.277-29.408-110.063-29.408-39.781 0-76.472 9.804-110.063 29.408-33.596 19.605-60.192 46.204-79.8 79.8C9.803 148.168 0 184.854 0 224.63c0 47.78 13.94 90.745 41.827 128.906 27.884 38.164 63.906 64.572 108.063 79.227 5.14.954 8.945.283 11.419-1.996 2.475-2.282 3.711-5.14 3.711-8.562 0-.571-.049-5.708-.144-15.417a2549.81 2549.81 0 01-.144-25.406l-6.567 1.136c-4.187.767-9.469 1.092-15.846 1-6.374-.089-12.991-.757-19.842-1.999-6.854-1.231-13.229-4.086-19.13-8.559-5.898-4.473-10.085-10.328-12.56-17.556l-2.855-6.57c-1.903-4.374-4.899-9.233-8.992-14.559-4.093-5.331-8.232-8.945-12.419-10.848l-1.999-1.431c-1.332-.951-2.568-2.098-3.711-3.429-1.142-1.331-1.997-2.663-2.568-3.997-.572-1.335-.098-2.43 1.427-3.289 1.525-.859 4.281-1.276 8.28-1.276l5.708.853c3.807.763 8.516 3.042 14.133 6.851 5.614 3.806 10.229 8.754 13.846 14.842 4.38 7.806 9.657 13.754 15.846 17.847 6.184 4.093 12.419 6.136 18.699 6.136 6.28 0 11.704-.476 16.274-1.423 4.565-.952 8.848-2.383 12.847-4.285 1.713-12.758 6.377-22.559 13.988-29.41-10.848-1.14-20.601-2.857-29.264-5.14-8.658-2.286-17.605-5.996-26.835-11.14-9.235-5.137-16.896-11.516-22.985-19.126-6.09-7.614-11.088-17.61-14.987-29.979-3.901-12.374-5.852-26.648-5.852-42.826 0-23.035 7.52-42.637 22.557-58.817-7.044-17.318-6.379-36.732 1.997-58.24 5.52-1.715 13.706-.428 24.554 3.853 10.85 4.283 18.794 7.952 23.84 10.994 5.046 3.041 9.089 5.618 12.135 7.708 17.705-4.947 35.976-7.421 54.818-7.421s37.117 2.474 54.823 7.421l10.849-6.849c7.419-4.57 16.18-8.758 26.262-12.565 10.088-3.805 17.802-4.853 23.134-3.138 8.562 21.509 9.325 40.922 2.279 58.24 15.036 16.18 22.559 35.787 22.559 58.817 0 16.178-1.958 30.497-5.853 42.966-3.9 12.471-8.941 22.457-15.125 29.979-6.191 7.521-13.901 13.85-23.131 18.986-9.232 5.14-18.182 8.85-26.84 11.136-8.662 2.286-18.415 4.004-29.263 5.146 9.894 8.562 14.842 22.077 14.842 40.539v60.237c0 3.422 1.19 6.279 3.572 8.562 2.379 2.279 6.136 2.95 11.276 1.995 44.163-14.653 80.185-41.062 108.068-79.226 27.88-38.161 41.825-81.126 41.825-128.906-.01-39.771-9.818-76.454-29.414-110.049z'
      ></path>
    </svg>
  ),
  radix: (props: IconProps) => (
    <svg viewBox='0 0 25 25' fill='none' {...props}>
      <path
        d='M12 25C7.58173 25 4 21.4183 4 17C4 12.5817 7.58173 9 12 9V25Z'
        fill='currentcolor'
      ></path>
      <path d='M12 0H4V8H12V0Z' fill='currentcolor'></path>
      <path
        d='M17 8C19.2091 8 21 6.20914 21 4C21 1.79086 19.2091 0 17 0C14.7909 0 13 1.79086 13 4C13 6.20914 14.7909 8 17 8Z'
        fill='currentcolor'
      ></path>
    </svg>
  ),
  aria: (props: IconProps) => (
    <svg role='img' viewBox='0 0 24 24' fill='currentColor' {...props}>
      <path d='M13.966 22.624l-1.69-4.281H8.122l3.892-9.144 5.662 13.425zM8.884 1.376H0v21.248zm15.116 0h-8.884L24 22.624Z' />
    </svg>
  ),
  npm: (props: IconProps) => (
    <svg viewBox='0 0 24 24' {...props}>
      <path
        d='M1.763 0C.786 0 0 .786 0 1.763v20.474C0 23.214.786 24 1.763 24h20.474c.977 0 1.763-.786 1.763-1.763V1.763C24 .786 23.214 0 22.237 0zM5.13 5.323l13.837.019-.009 13.836h-3.464l.01-10.382h-3.456L12.04 19.17H5.113z'
        fill='currentColor'
      />
    </svg>
  ),
  yarn: (props: IconProps) => (
    <svg viewBox='0 0 24 24' {...props}>
      <path
        d='M12 0C5.375 0 0 5.375 0 12s5.375 12 12 12 12-5.375 12-12S18.625 0 12 0zm.768 4.105c.183 0 .363.053.525.157.125.083.287.185.755 1.154.31-.088.468-.042.551-.019.204.056.366.19.463.375.477.917.542 2.553.334 3.605-.241 1.232-.755 2.029-1.131 2.576.324.329.778.899 1.117 1.825.278.774.31 1.478.273 2.015a5.51 5.51 0 0 0 .602-.329c.593-.366 1.487-.917 2.553-.931.714-.009 1.269.445 1.353 1.103a1.23 1.23 0 0 1-.945 1.362c-.649.158-.95.278-1.821.843-1.232.797-2.539 1.242-3.012 1.39a1.686 1.686 0 0 1-.704.343c-.737.181-3.266.315-3.466.315h-.046c-.783 0-1.214-.241-1.45-.491-.658.329-1.51.19-2.122-.134a1.078 1.078 0 0 1-.58-1.153 1.243 1.243 0 0 1-.153-.195c-.162-.25-.528-.936-.454-1.946.056-.723.556-1.367.88-1.71a5.522 5.522 0 0 1 .408-2.256c.306-.727.885-1.348 1.32-1.737-.32-.537-.644-1.367-.329-2.21.227-.602.412-.936.82-1.08h-.005c.199-.074.389-.153.486-.259a3.418 3.418 0 0 1 2.298-1.103c.037-.093.079-.185.125-.283.31-.658.639-1.029 1.024-1.168a.94.94 0 0 1 .328-.06zm.006.7c-.507.016-1.001 1.519-1.001 1.519s-1.27-.204-2.266.871c-.199.218-.468.334-.746.44-.079.028-.176.023-.417.672-.371.991.625 2.094.625 2.094s-1.186.839-1.626 1.881c-.486 1.144-.338 2.261-.338 2.261s-.843.732-.899 1.487c-.051.663.139 1.2.343 1.515.227.343.51.176.51.176s-.561.653-.037.931c.477.25 1.283.394 1.71-.037.31-.31.371-1.001.486-1.283.028-.065.12.111.209.199.097.093.264.195.264.195s-.755.324-.445 1.066c.102.246.468.403 1.066.398.222-.005 2.664-.139 3.313-.296.375-.088.505-.283.505-.283s1.566-.431 2.998-1.357c.917-.598 1.293-.76 2.034-.936.612-.148.57-1.098-.241-1.084-.839.009-1.575.44-2.196.825-1.163.718-1.742.672-1.742.672l-.018-.032c-.079-.13.371-1.293-.134-2.678-.547-1.515-1.413-1.881-1.344-1.997.297-.5 1.038-1.297 1.334-2.78.176-.899.13-2.377-.269-3.151-.074-.144-.732.241-.732.241s-.616-1.371-.788-1.483a.271.271 0 0 0-.157-.046z'
        fill='currentColor'
      />
    </svg>
  ),
  pnpm: (props: IconProps) => (
    <svg viewBox='0 0 24 24' {...props}>
      <path
        d='M0 0v7.5h7.5V0zm8.25 0v7.5h7.498V0zm8.25 0v7.5H24V0zM8.25 8.25v7.5h7.498v-7.5zm8.25 0v7.5H24v-7.5zM0 16.5V24h7.5v-7.5zm8.25 0V24h7.498v-7.5zm8.25 0V24H24v-7.5z'
        fill='currentColor'
      />
    </svg>
  ),
  react: (props: IconProps) => (
    <svg viewBox='0 0 24 24' {...props}>
      <path
        d='M14.23 12.004a2.236 2.236 0 0 1-2.235 2.236 2.236 2.236 0 0 1-2.236-2.236 2.236 2.236 0 0 1 2.235-2.236 2.236 2.236 0 0 1 2.236 2.236zm2.648-10.69c-1.346 0-3.107.96-4.888 2.622-1.78-1.653-3.542-2.602-4.887-2.602-.41 0-.783.093-1.106.278-1.375.793-1.683 3.264-.973 6.365C1.98 8.917 0 10.42 0 12.004c0 1.59 1.99 3.097 5.043 4.03-.704 3.113-.39 5.588.988 6.38.32.187.69.275 1.102.275 1.345 0 3.107-.96 4.888-2.624 1.78 1.654 3.542 2.603 4.887 2.603.41 0 .783-.09 1.106-.275 1.374-.792 1.683-3.263.973-6.365C22.02 15.096 24 13.59 24 12.004c0-1.59-1.99-3.097-5.043-4.032.704-3.11.39-5.587-.988-6.38-.318-.184-.688-.277-1.092-.278zm-.005 1.09v.006c.225 0 .406.044.558.127.666.382.955 1.835.73 3.704-.054.46-.142.945-.25 1.44-.96-.236-2.006-.417-3.107-.534-.66-.905-1.345-1.727-2.035-2.447 1.592-1.48 3.087-2.292 4.105-2.295zm-9.77.02c1.012 0 2.514.808 4.11 2.28-.686.72-1.37 1.537-2.02 2.442-1.107.117-2.154.298-3.113.538-.112-.49-.195-.964-.254-1.42-.23-1.868.054-3.32.714-3.707.19-.09.4-.127.563-.132zm4.882 3.05c.455.468.91.992 1.36 1.564-.44-.02-.89-.034-1.345-.034-.46 0-.915.01-1.36.034.44-.572.895-1.096 1.345-1.565zM12 8.1c.74 0 1.477.034 2.202.093.406.582.802 1.203 1.183 1.86.372.64.71 1.29 1.018 1.946-.308.655-.646 1.31-1.013 1.95-.38.66-.773 1.288-1.18 1.87-.728.063-1.466.098-2.21.098-.74 0-1.477-.035-2.202-.093-.406-.582-.802-1.204-1.183-1.86-.372-.64-.71-1.29-1.018-1.946.303-.657.646-1.313 1.013-1.954.38-.66.773-1.286 1.18-1.868.728-.064 1.466-.098 2.21-.098zm-3.635.254c-.24.377-.48.763-.704 1.16-.225.39-.435.782-.635 1.174-.265-.656-.49-1.31-.676-1.947.64-.15 1.315-.283 2.015-.386zm7.26 0c.695.103 1.365.23 2.006.387-.18.632-.405 1.282-.66 1.933-.2-.39-.41-.783-.64-1.174-.225-.392-.465-.774-.705-1.146zm3.063.675c.484.15.944.317 1.375.498 1.732.74 2.852 1.708 2.852 2.476-.005.768-1.125 1.74-2.857 2.475-.42.18-.88.342-1.355.493-.28-.958-.646-1.956-1.1-2.98.45-1.017.81-2.01 1.085-2.964zm-13.395.004c.278.96.645 1.957 1.1 2.98-.45 1.017-.812 2.01-1.086 2.964-.484-.15-.944-.318-1.37-.5-1.732-.737-2.852-1.706-2.852-2.474 0-.768 1.12-1.742 2.852-2.476.42-.18.88-.342 1.356-.494zm11.678 4.28c.265.657.49 1.312.676 1.948-.64.157-1.316.29-2.016.39.24-.375.48-.762.705-1.158.225-.39.435-.788.636-1.18zm-9.945.02c.2.392.41.783.64 1.175.23.39.465.772.705 1.143-.695-.102-1.365-.23-2.006-.386.18-.63.406-1.282.66-1.933zM17.92 16.32c.112.493.2.968.254 1.423.23 1.868-.054 3.32-.714 3.708-.147.09-.338.128-.563.128-1.012 0-2.514-.807-4.11-2.28.686-.72 1.37-1.536 2.02-2.44 1.107-.118 2.154-.3 3.113-.54zm-11.83.01c.96.234 2.006.415 3.107.532.66.905 1.345 1.727 2.035 2.446-1.595 1.483-3.092 2.295-4.11 2.295-.22-.005-.406-.05-.553-.132-.666-.38-.955-1.834-.73-3.703.054-.46.142-.944.25-1.438zm4.56.64c.44.02.89.034 1.345.034.46 0 .915-.01 1.36-.034-.44.572-.895 1.095-1.345 1.565-.455-.47-.91-.993-1.36-1.565z'
        fill='currentColor'
      />
    </svg>
  ),
  tailwind: (props: IconProps) => (
    <svg viewBox='0 0 24 24' {...props}>
      <path
        d='M12.001,4.8c-3.2,0-5.2,1.6-6,4.8c1.2-1.6,2.6-2.2,4.2-1.8c0.913,0.228,1.565,0.89,2.288,1.624 C13.666,10.618,15.027,12,18.001,12c3.2,0,5.2-1.6,6-4.8c-1.2,1.6-2.6,2.2-4.2,1.8c-0.913-0.228-1.565-0.89-2.288-1.624 C16.337,6.182,14.976,4.8,12.001,4.8z M6.001,12c-3.2,0-5.2,1.6-6,4.8c1.2-1.6,2.6-2.2,4.2-1.8c0.913,0.228,1.565,0.89,2.288,1.624 c1.177,1.194,2.538,2.576,5.512,2.576c3.2,0,5.2-1.6,6-4.8c-1.2,1.6-2.6,2.2-4.2,1.8c-0.913-0.228-1.565-0.89-2.288-1.624 C10.337,13.382,8.976,12,6.001,12z'
        fill='currentColor'
      />
    </svg>
  ),
  google: (props: IconProps) => (
    <svg role='img' viewBox='0 0 24 24' {...props}>
      <path
        fill='currentColor'
        d='M12.48 10.92v3.28h7.84c-.24 1.84-.853 3.187-1.787 4.133-1.147 1.147-2.933 2.4-6.053 2.4-4.827 0-8.6-3.893-8.6-8.72s3.773-8.72 8.6-8.72c2.6 0 4.507 1.027 5.907 2.347l2.307-2.307C18.747 1.44 16.133 0 12.48 0 5.867 0 .307 5.387.307 12s5.56 12 12.173 12c3.573 0 6.267-1.173 8.373-3.36 2.16-2.16 2.84-5.213 2.84-7.667 0-.76-.053-1.467-.173-2.053H12.48z'
      />
    </svg>
  ),
  google_color: (props: IconProps) => (
    <svg
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <path
        d='M21.8055 10.0415H21V10H12V14H17.6515C16.827 16.3285 14.6115 18 12 18C8.6865 18 6 15.3135 6 12C6 8.6865 8.6865 6 12 6C13.5295 6 14.921 6.577 15.9805 7.5195L18.809 4.691C17.023 3.0265 14.634 2 12 2C6.4775 2 2 6.4775 2 12C2 17.5225 6.4775 22 12 22C17.5225 22 22 17.5225 22 12C22 11.3295 21.931 10.675 21.8055 10.0415Z'
        fill='#FFC107'
      />
      <path
        d='M3.15332 7.3455L6.43882 9.755C7.32782 7.554 9.48082 6 12.0003 6C13.5298 6 14.9213 6.577 15.9808 7.5195L18.8093 4.691C17.0233 3.0265 14.6343 2 12.0003 2C8.15932 2 4.82832 4.1685 3.15332 7.3455Z'
        fill='#FF3D00'
      />
      <path
        d='M12.0002 22.0003C14.5832 22.0003 16.9302 21.0118 18.7047 19.4043L15.6097 16.7853C14.5719 17.5745 13.3039 18.0014 12.0002 18.0003C9.39916 18.0003 7.19066 16.3418 6.35866 14.0273L3.09766 16.5398C4.75266 19.7783 8.11366 22.0003 12.0002 22.0003Z'
        fill='#4CAF50'
      />
      <path
        d='M21.8055 10.0415H21V10H12V14H17.6515C17.2571 15.1082 16.5467 16.0766 15.608 16.7855L15.6095 16.7845L18.7045 19.4035C18.4855 19.6025 22 17 22 12C22 11.3295 21.931 10.675 21.8055 10.0415Z'
        fill='#1976D2'
      />
    </svg>
  ),
  apple: (props: IconProps) => (
    <svg role='img' viewBox='0 0 24 24' {...props}>
      <path
        d='M12.152 6.896c-.948 0-2.415-1.078-3.96-1.04-2.04.027-3.91 1.183-4.961 3.014-2.117 3.675-.546 9.103 1.519 12.09 1.013 1.454 2.208 3.09 3.792 3.039 1.52-.065 2.09-.987 3.935-.987 1.831 0 2.35.987 3.96.948 1.637-.026 2.676-1.48 3.676-2.948 1.156-1.688 1.636-3.325 1.662-3.415-.039-.013-3.182-1.221-3.22-4.857-.026-3.04 2.48-4.494 2.597-4.559-1.429-2.09-3.623-2.324-4.39-2.376-2-.156-3.675 1.09-4.61 1.09zM15.53 3.83c.843-1.012 1.4-2.427 1.245-3.83-1.207.052-2.662.805-3.532 1.818-.78.896-1.454 2.338-1.273 3.714 1.338.104 2.715-.688 3.559-1.701'
        fill='currentColor'
      />
    </svg>
  ),
  paypal: (props: IconProps) => (
    <svg role='img' viewBox='0 0 24 24' {...props}>
      <path
        d='M7.076 21.337H2.47a.641.641 0 0 1-.633-.74L4.944.901C5.026.382 5.474 0 5.998 0h7.46c2.57 0 4.578.543 5.69 1.81 1.01 1.15 1.304 2.42 1.012 4.287-.023.143-.047.288-.077.437-.983 5.05-4.349 6.797-8.647 6.797h-2.19c-.524 0-.968.382-1.05.9l-1.12 7.106zm14.146-14.42a3.35 3.35 0 0 0-.607-.541c-.013.076-.026.175-.041.254-.93 4.778-4.005 7.201-9.138 7.201h-2.19a.563.563 0 0 0-.556.479l-1.187 7.527h-.506l-.24 1.516a.56.56 0 0 0 .554.647h3.882c.46 0 .85-.334.922-.788.06-.26.76-4.852.816-5.09a.932.932 0 0 1 .923-.788h.58c3.76 0 6.705-1.528 7.565-5.946.36-1.847.174-3.388-.777-4.471z'
        fill='currentColor'
      />
    </svg>
  ),
  spinner: (props: IconProps) => (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
      stroke='currentColor'
      strokeWidth='2'
      strokeLinecap='round'
      strokeLinejoin='round'
      {...props}
    >
      <path d='M21 12a9 9 0 1 1-6.219-8.56' />
    </svg>
  ),
  success: (props: IconProps) => (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      viewBox='0 0 48 48'
      width='24'
      height='24'
      stroke='currentColor'
      {...props}
    >
      <g id='surface1_4_'>
        <path
          fill='currentColor'
          d='M44,24c0,11.045-8.955,20-20,20S4,35.045,4,24S12.955,4,24,4S44,12.955,44,24z'
        />
        <path
          fill='currentColor'
          d='M34.602,14.602L21,28.199l-5.602-5.598l-2.797,2.797L21,33.801l16.398-16.402L34.602,14.602z'
        />
      </g>
    </svg>
  ),
  home: (props: IconProps) => (
    <svg
      width='18'
      height='18'
      viewBox='0 0 18 18'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      stroke='currentColor'
      {...props}
    >
      <path
        d='M14 17.1251H10.4583V12.7501C10.4583 11.9459 9.80417 11.2917 9 11.2917C8.19583 11.2917 7.54167 11.9459 7.54167 12.7501V17.1251H4C1.985 17.1251 0.875 16.0151 0.875 14.0001V8.70841C0.875 6.93924 1.36333 6.44505 2.15999 5.78422L7.25999 1.5084C8.26749 0.662565 9.73251 0.662565 10.74 1.5084L15.84 5.78422C16.6367 6.44505 17.125 6.94008 17.125 8.70841V14.0001C17.125 16.0151 16.015 17.1251 14 17.1251ZM11.7083 15.8751H14C15.3142 15.8751 15.875 15.3142 15.875 14.0001V8.70841C15.875 7.43674 15.665 7.26259 15.0425 6.74593L9.9375 2.46594C9.39417 2.01094 8.60583 2.01094 8.0625 2.46594L2.95752 6.74593C2.33502 7.26259 2.125 7.43674 2.125 8.70841V14.0001C2.125 15.3142 2.68583 15.8751 4 15.8751H6.29167V12.7501C6.29167 11.2567 7.50667 10.0417 9 10.0417C10.4933 10.0417 11.7083 11.2567 11.7083 12.7501V15.8751Z'
        fill='currentColor'
        fillOpacity='0.7'
      />
    </svg>
  ),
  search: (props: IconProps) => (
    <svg
      width='20'
      height='20'
      viewBox='0 0 20 20'
      fill='none'
      {...props}
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M19.7746 18.6874L15.8351 14.7479C17.152 13.1858 17.9489 11.1724 17.9489 8.97446C17.9489 4.02569 13.9232 0 8.97446 0C4.02569 0 0 4.02569 0 8.97446C0 13.9232 4.02569 17.9489 8.97446 17.9489C11.1724 17.9489 13.1858 17.152 14.7479 15.835L18.6874 19.7746C18.8371 19.9243 19.0341 20.0002 19.231 20.0002C19.4279 20.0002 19.6249 19.9254 19.7746 19.7746C20.0751 19.4751 20.0751 18.9879 19.7746 18.6874ZM1.53848 8.97446C1.53848 4.8739 4.8739 1.53848 8.97446 1.53848C13.075 1.53848 16.4104 4.8739 16.4104 8.97446C16.4104 13.075 13.075 16.4104 8.97446 16.4104C4.8739 16.4104 1.53848 13.075 1.53848 8.97446Z'
        fill='url(#paint0_linear_8778_19735)'
      />
      <defs>
        <linearGradient
          id='paint0_linear_8778_19735'
          x1='13.2'
          y1='25.3661'
          x2='-12.7672'
          y2='11.9595'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='#6600CC' />
          <stop offset='0.8' stopColor='#B098FF' />
        </linearGradient>
      </defs>
    </svg>
  ),
  event: (props: IconProps) => (
    <svg
      width='18'
      height='18'
      viewBox='0 0 18 18'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      stroke='currentColor'
      {...props}
    >
      <path
        d='M14 2.125H12.9583V1.5C12.9583 1.155 12.6783 0.875 12.3333 0.875C11.9883 0.875 11.7083 1.155 11.7083 1.5V2.125H6.29167V1.5C6.29167 1.155 6.01167 0.875 5.66667 0.875C5.32167 0.875 5.04167 1.155 5.04167 1.5V2.125H4C1.985 2.125 0.875 3.235 0.875 5.25V14C0.875 16.015 1.985 17.125 4 17.125H14C16.015 17.125 17.125 16.015 17.125 14V5.25C17.125 3.235 16.015 2.125 14 2.125ZM4 3.375H5.04167V4C5.04167 4.345 5.32167 4.625 5.66667 4.625C6.01167 4.625 6.29167 4.345 6.29167 4V3.375H11.7083V4C11.7083 4.345 11.9883 4.625 12.3333 4.625C12.6783 4.625 12.9583 4.345 12.9583 4V3.375H14C15.3142 3.375 15.875 3.93583 15.875 5.25V5.875H2.125V5.25C2.125 3.93583 2.68583 3.375 4 3.375ZM14 15.875H4C2.68583 15.875 2.125 15.3142 2.125 14V7.125H15.875V14C15.875 15.3142 15.3142 15.875 14 15.875Z'
        fill='currentColor'
        fillOpacity='0.7'
      />
    </svg>
  ),
  favourites: (props: IconProps) => (
    <svg
      width='18'
      height='17'
      viewBox='0 0 18 17'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      stroke='currentColor'
      {...props}
    >
      <path
        d='M9.00014 16.1249C8.9118 16.1249 8.82344 16.1065 8.74094 16.069C8.46844 15.9449 2.05514 12.9707 1.02681 7.67405C0.629309 5.62489 1.02848 3.62571 2.09431 2.32737C2.95681 1.27571 4.19678 0.716528 5.68094 0.709028C5.68844 0.709028 5.69594 0.709028 5.70261 0.709028C7.39594 0.709028 8.42849 1.67322 8.99932 2.49405C9.57266 1.66989 10.6134 0.701528 12.3176 0.709028C13.8026 0.716528 15.0434 1.27571 15.9068 2.32737C16.9709 3.62488 17.3693 5.62403 16.9709 7.67487C15.9443 12.9715 9.5301 15.9466 9.2576 16.0699C9.17677 16.1066 9.08847 16.1249 9.00014 16.1249ZM5.7018 1.95821C5.6968 1.95821 5.69266 1.95821 5.68766 1.95821C4.57266 1.96321 3.68934 2.35403 3.06101 3.11987C2.22851 4.13403 1.92766 5.74738 2.25433 7.43571C3.05016 11.539 7.82764 14.2057 9.00014 14.804C10.1726 14.2057 14.9501 11.539 15.7451 7.43571C16.0735 5.74655 15.7726 4.1332 14.9418 3.11987C14.3135 2.35487 13.4301 1.96486 12.3126 1.95903C12.3076 1.95903 12.3026 1.95903 12.2985 1.95903C10.3218 1.95903 9.62102 3.93988 9.59269 4.02405C9.50602 4.27655 9.26762 4.44819 9.00095 4.44819C8.99928 4.44819 8.99843 4.44819 8.99759 4.44819C8.73009 4.44736 8.49177 4.27654 8.40677 4.02237C8.37927 3.93904 7.67763 1.95821 5.7018 1.95821Z'
        fill='currentColor'
        fillOpacity='0.7'
      />
    </svg>
  ),
  profile: (props: IconProps) => (
    <svg
      width='14'
      height='18'
      viewBox='0 0 14 18'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      stroke='currentColor'
      {...props}
    >
      <path
        d='M7.00785 7.95833C5.05452 7.95833 3.46619 6.36917 3.46619 4.41667C3.46619 2.46417 5.05452 0.875 7.00785 0.875C8.96119 0.875 10.5495 2.46417 10.5495 4.41667C10.5495 6.36917 8.96119 7.95833 7.00785 7.95833ZM7.00785 2.125C5.74369 2.125 4.71619 3.1525 4.71619 4.41667C4.71619 5.68083 5.74369 6.70833 7.00785 6.70833C8.27202 6.70833 9.29952 5.68083 9.29952 4.41667C9.29952 3.1525 8.27119 2.125 7.00785 2.125ZM10.3311 17.125H3.66954C1.65287 17.125 0.541992 16.0209 0.541992 14.0159C0.541992 11.7984 1.79699 9.20833 5.33366 9.20833H8.66699C12.2037 9.20833 13.4587 11.7975 13.4587 14.0159C13.4587 16.0209 12.3478 17.125 10.3311 17.125ZM5.33366 10.4583C2.04783 10.4583 1.79199 13.1809 1.79199 14.0159C1.79199 15.3192 2.3537 15.875 3.66954 15.875H10.3311C11.6469 15.875 12.2087 15.3192 12.2087 14.0159C12.2087 13.1817 11.9528 10.4583 8.66699 10.4583H5.33366Z'
        fill='currentColor'
        fillOpacity='0.7'
      />
    </svg>
  ),
  setting: (props: IconProps) => (
    <svg
      width='20'
      height='20'
      viewBox='0 0 20 20'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      stroke='currentColor'
      {...props}
    >
      <path
        d='M9.99967 12.5002C11.3804 12.5002 12.4997 11.3809 12.4997 10.0002C12.4997 8.61945 11.3804 7.50016 9.99967 7.50016C8.61896 7.50016 7.49967 8.61945 7.49967 10.0002C7.49967 11.3809 8.61896 12.5002 9.99967 12.5002Z'
        stroke='currentColor'
        // stroke-opacity='0.7'
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M16.1663 12.5002C16.0554 12.7515 16.0223 13.0303 16.0713 13.3007C16.1204 13.571 16.2492 13.8204 16.4413 14.0168L16.4913 14.0668C16.6463 14.2216 16.7692 14.4054 16.8531 14.6078C16.937 14.8101 16.9802 15.027 16.9802 15.246C16.9802 15.465 16.937 15.6819 16.8531 15.8842C16.7692 16.0866 16.6463 16.2704 16.4913 16.4252C16.3366 16.5801 16.1527 16.7031 15.9504 16.7869C15.7481 16.8708 15.5312 16.914 15.3122 16.914C15.0931 16.914 14.8763 16.8708 14.6739 16.7869C14.4716 16.7031 14.2878 16.5801 14.133 16.4252L14.083 16.3752C13.8866 16.183 13.6372 16.0542 13.3668 16.0052C13.0965 15.9561 12.8177 15.9892 12.5663 16.1002C12.3199 16.2058 12.1097 16.3812 11.9616 16.6048C11.8135 16.8283 11.7341 17.0903 11.733 17.3585V17.5002C11.733 17.9422 11.5574 18.3661 11.2449 18.6787C10.9323 18.9912 10.5084 19.1668 10.0663 19.1668C9.62431 19.1668 9.20039 18.9912 8.88783 18.6787C8.57527 18.3661 8.39967 17.9422 8.39967 17.5002V17.4252C8.39322 17.1493 8.30394 16.8818 8.14343 16.6574C7.98293 16.433 7.75862 16.2621 7.49967 16.1668C7.24833 16.0559 6.96951 16.0228 6.69918 16.0718C6.42885 16.1208 6.17941 16.2497 5.98301 16.4418L5.93301 16.4918C5.77822 16.6468 5.5944 16.7697 5.39207 16.8536C5.18974 16.9375 4.97287 16.9806 4.75384 16.9806C4.53481 16.9806 4.31794 16.9375 4.11561 16.8536C3.91328 16.7697 3.72946 16.6468 3.57467 16.4918C3.41971 16.337 3.29678 16.1532 3.21291 15.9509C3.12903 15.7486 3.08586 15.5317 3.08586 15.3127C3.08586 15.0936 3.12903 14.8768 3.21291 14.6744C3.29678 14.4721 3.41971 14.2883 3.57467 14.1335L3.62467 14.0835C3.81679 13.8871 3.94566 13.6376 3.99468 13.3673C4.04369 13.097 4.0106 12.8182 3.89967 12.5668C3.79404 12.3204 3.61864 12.1101 3.39506 11.9621C3.17149 11.814 2.9095 11.7346 2.64134 11.7335H2.49967C2.05765 11.7335 1.63372 11.5579 1.32116 11.2453C1.0086 10.9328 0.833008 10.5089 0.833008 10.0668C0.833008 9.6248 1.0086 9.20088 1.32116 8.88832C1.63372 8.57576 2.05765 8.40016 2.49967 8.40016H2.57467C2.8505 8.39371 3.11801 8.30443 3.34242 8.14392C3.56684 7.98341 3.73777 7.75911 3.83301 7.50016C3.94394 7.24882 3.97703 6.97 3.92801 6.69967C3.879 6.42934 3.75012 6.17989 3.55801 5.9835L3.50801 5.9335C3.35305 5.77871 3.23012 5.59489 3.14624 5.39256C3.06237 5.19023 3.0192 4.97335 3.0192 4.75433C3.0192 4.5353 3.06237 4.31843 3.14624 4.1161C3.23012 3.91377 3.35305 3.72995 3.50801 3.57516C3.6628 3.4202 3.84661 3.29727 4.04894 3.2134C4.25127 3.12952 4.46815 3.08635 4.68717 3.08635C4.9062 3.08635 5.12308 3.12952 5.32541 3.2134C5.52774 3.29727 5.71155 3.4202 5.86634 3.57516L5.91634 3.62516C6.11274 3.81728 6.36219 3.94615 6.63252 3.99517C6.90285 4.04418 7.18166 4.01109 7.43301 3.90016H7.49967C7.74615 3.79453 7.95635 3.61913 8.10442 3.39555C8.25248 3.17198 8.33194 2.90998 8.33301 2.64183V2.50016C8.33301 2.05814 8.5086 1.63421 8.82116 1.32165C9.13372 1.00909 9.55765 0.833496 9.99967 0.833496C10.4417 0.833496 10.8656 1.00909 11.1782 1.32165C11.4907 1.63421 11.6663 2.05814 11.6663 2.50016V2.57516C11.6674 2.84332 11.7469 3.10531 11.8949 3.32888C12.043 3.55246 12.2532 3.72786 12.4997 3.8335C12.751 3.94443 13.0298 3.97752 13.3002 3.9285C13.5705 3.87948 13.8199 3.75061 14.0163 3.5585L14.0663 3.5085C14.2211 3.35354 14.4049 3.2306 14.6073 3.14673C14.8096 3.06286 15.0265 3.01968 15.2455 3.01968C15.4645 3.01968 15.6814 3.06286 15.8837 3.14673C16.0861 3.2306 16.2699 3.35354 16.4247 3.5085C16.5796 3.66328 16.7026 3.8471 16.7864 4.04943C16.8703 4.25176 16.9135 4.46864 16.9135 4.68766C16.9135 4.90669 16.8703 5.12357 16.7864 5.3259C16.7026 5.52823 16.5796 5.71204 16.4247 5.86683L16.3747 5.91683C16.1826 6.11323 16.0537 6.36268 16.0047 6.633C15.9557 6.90333 15.9887 7.18215 16.0997 7.4335V7.50016C16.2053 7.74664 16.3807 7.95684 16.6043 8.10491C16.8279 8.25297 17.0899 8.33243 17.358 8.3335H17.4997C17.9417 8.3335 18.3656 8.50909 18.6782 8.82165C18.9907 9.13421 19.1663 9.55813 19.1663 10.0002C19.1663 10.4422 18.9907 10.8661 18.6782 11.1787C18.3656 11.4912 17.9417 11.6668 17.4997 11.6668H17.4247C17.1565 11.6679 16.8945 11.7474 16.671 11.8954C16.4474 12.0435 16.272 12.2537 16.1663 12.5002Z'
        stroke='currentColor'
        // stroke-opacity='0.7'
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  ),
  support: (props: IconProps) => (
    <svg
      width='18'
      height='18'
      viewBox='0 0 18 18'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      stroke='currentColor'
      {...props}
    >
      <path
        d='M9.00033 0.0415039C4.06033 0.0415039 0.0419922 4.06067 0.0419922 8.99984C0.0419922 13.939 4.06033 17.9582 9.00033 17.9582C13.9403 17.9582 17.9587 13.939 17.9587 8.99984C17.9587 4.06067 13.9403 0.0415039 9.00033 0.0415039ZM14.8695 13.9848L12.4595 11.5749C12.9987 10.8532 13.3295 9.96734 13.3295 8.99984C13.3295 8.03234 12.9987 7.14648 12.4595 6.42482L14.8695 4.01484C16.0137 5.35984 16.7087 7.099 16.7087 8.99984C16.7087 10.9007 16.0137 12.6398 14.8695 13.9848ZM5.92202 8.99984C5.92202 7.30234 7.30283 5.92153 9.00033 5.92153C10.6978 5.92153 12.0786 7.30234 12.0786 8.99984C12.0786 10.6973 10.6978 12.0781 9.00033 12.0781C7.30283 12.0781 5.92202 10.6973 5.92202 8.99984ZM13.9853 3.13151L11.5753 5.54148C10.8537 5.00232 9.96783 4.67234 9.00033 4.67234C8.03283 4.67234 7.14697 5.00315 6.4253 5.54148L4.01533 3.13151C5.36116 1.98651 7.09949 1.2915 9.00033 1.2915C10.9012 1.2915 12.6403 1.98651 13.9853 3.13151ZM3.13113 4.01484L5.54116 6.42482C5.00199 7.14648 4.67115 8.03234 4.67115 8.99984C4.67115 9.96734 5.00199 10.8532 5.54116 11.5749L3.13113 13.9848C1.98697 12.6398 1.29199 10.9007 1.29199 8.99984C1.29199 7.099 1.98697 5.35984 3.13113 4.01484ZM4.01533 14.8682L6.4253 12.4582C7.14697 12.9974 8.03283 13.3273 9.00033 13.3273C9.96783 13.3273 10.8537 12.9965 11.5753 12.4582L13.9853 14.8682C12.6395 16.0132 10.9012 16.7082 9.00033 16.7082C7.09949 16.7082 5.36033 16.0132 4.01533 14.8682Z'
        fill='currentColor'
        fillOpacity='0.7'
      />
    </svg>
  ),
  touch: (props: IconProps) => (
    <svg
      width='19'
      height='20'
      viewBox='0 0 19 20'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      stroke='currentColor'
      {...props}
    >
      <path
        d='M5.83366 18.3332V9.1665M1.66699 10.8332V16.6665C1.66699 17.587 2.41318 18.3332 3.33366 18.3332H14.5222C15.7561 18.3332 16.8055 17.4329 16.9931 16.2133L17.8906 10.38C18.1235 8.86558 16.9518 7.49984 15.4196 7.49984H12.5003C12.0401 7.49984 11.667 7.12674 11.667 6.6665V3.72137C11.667 2.5865 10.747 1.6665 9.61213 1.6665C9.34144 1.6665 9.09614 1.82592 8.98621 2.07327L6.05361 8.67162C5.91986 8.97256 5.62142 9.1665 5.2921 9.1665H3.33366C2.41318 9.1665 1.66699 9.9127 1.66699 10.8332Z'
        stroke='#1A1A1A'
        // stroke-opacity='0.7'
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  ),
  logout: (props: IconProps) => (
    <svg
      width='36'
      height='36'
      viewBox='0 0 36 36'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      stroke='currentColor'
      {...props}
    >
      <path
        d='M15.5 25.5H12.1667C11.7246 25.5 11.3007 25.3244 10.9882 25.0118C10.6756 24.6993 10.5 24.2754 10.5 23.8333V12.1667C10.5 11.7246 10.6756 11.3007 10.9882 10.9882C11.3007 10.6756 11.7246 10.5 12.1667 10.5H15.5M21.3333 22.1667L25.5 18M25.5 18L21.3333 13.8333M25.5 18H15.5'
        stroke='#1A1A1A'
        // stroke-opacity='0.7'
        strokeWidth='1.67'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  ),
  dropdown: (props: IconProps) => (
    <svg
      width='14'
      height='8'
      viewBox='0 0 14 8'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      stroke='currentColor'
      {...props}
    >
      <path
        d='M7.00014 7.95848C6.84014 7.95848 6.68012 7.89767 6.55845 7.77517L0.725117 1.94183C0.480951 1.69767 0.480951 1.3018 0.725117 1.05764C0.969284 0.81347 1.36515 0.81347 1.60931 1.05764L7.00096 6.44928L12.3926 1.05764C12.6368 0.81347 13.0326 0.81347 13.2768 1.05764C13.521 1.3018 13.521 1.69767 13.2768 1.94183L7.44346 7.77517C7.32013 7.89767 7.16014 7.95848 7.00014 7.95848Z'
        fill='currentColor'
      />
    </svg>
  ),
  dropup: (props: IconProps) => (
    <svg
      width='14'
      height='8'
      viewBox='0 0 14 8'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      stroke='currentColor'
      {...props}
    >
      <path
        d='M12.8333 7.12498C12.6733 7.12498 12.5133 7.06417 12.3916 6.94167L6.99998 1.55003L1.60834 6.94167C1.36417 7.18584 0.968307 7.18584 0.724141 6.94167C0.479974 6.6975 0.479974 6.30164 0.724141 6.05747L6.55747 0.224141C6.80164 -0.020026 7.1975 -0.020026 7.44167 0.224141L13.275 6.05747C13.5192 6.30164 13.5192 6.6975 13.275 6.94167C13.1533 7.06417 12.9933 7.12498 12.8333 7.12498Z'
        fill='currentColor'
      />
    </svg>
  ),
  bell: (props: IconProps) => (
    <svg
      width='12'
      height='14'
      viewBox='0 0 12 14'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      stroke='currentColor'
      {...props}
    >
      <path
        d='M5.99981 13.5C5.34114 13.5 4.74914 13.158 4.41447 12.5853C4.27581 12.3467 4.35581 12.0407 4.59448 11.9013C4.83181 11.7627 5.13848 11.8427 5.27848 12.0813C5.58448 12.606 6.41514 12.606 6.72114 12.0813C6.86048 11.8427 7.16714 11.7627 7.40514 11.9013C7.64381 12.04 7.72448 12.3467 7.58515 12.5853C7.25048 13.158 6.65848 13.5 5.99981 13.5ZM11.7831 11.2167C11.8665 11.0433 11.8431 10.838 11.7238 10.688C11.7111 10.6727 10.4965 9.126 10.4965 7.33333V4.99666C10.4965 2.51733 8.47914 0.5 5.99981 0.5C3.52048 0.5 1.50314 2.51733 1.50314 4.99666V7.33333C1.50314 9.126 0.288477 10.6727 0.275811 10.688C0.156477 10.838 0.133141 11.044 0.216474 11.2167C0.299808 11.3893 0.474476 11.5 0.666476 11.5H11.3331C11.5251 11.5 11.6998 11.3893 11.7831 11.2167ZM2.50314 7.33333V4.99666C2.50314 3.06866 4.07181 1.5 5.99981 1.5C7.92781 1.5 9.49647 3.06866 9.49647 4.99666V7.33333C9.49647 8.624 9.99648 9.772 10.4085 10.5H1.59048C2.00314 9.772 2.50314 8.624 2.50314 7.33333Z'
        fill='currentColor'
      />
    </svg>
  ),
  wallet: (props: IconProps) => (
    <svg
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      stroke='currentColor'
      {...props}
    >
      <path
        d='M20 9.5V7.2C20 6.0799 20 5.51984 19.782 5.09202C19.5903 4.7157 19.2843 4.40974 18.908 4.21799C18.4802 4 17.9201 4 16.8 4H5.2C4.0799 4 3.51984 4 3.09202 4.21799C2.7157 4.40973 2.40973 4.71569 2.21799 5.09202C2 5.51984 2 6.0799 2 7.2V16.8C2 17.9201 2 18.4802 2.21799 18.908C2.40973 19.2843 2.71569 19.5903 3.09202 19.782C3.51984 20 4.07989 20 5.2 20L16.8 20C17.9201 20 18.4802 20 18.908 19.782C19.2843 19.5903 19.5903 19.2843 19.782 18.908C20 18.4802 20 17.9201 20 16.8V14.5M15 12C15 11.5353 15 11.303 15.0384 11.1098C15.1962 10.3164 15.8164 9.69624 16.6098 9.53843C16.803 9.5 17.0353 9.5 17.5 9.5H19.5C19.9647 9.5 20.197 9.5 20.3902 9.53843C21.1836 9.69624 21.8038 10.3164 21.9616 11.1098C22 11.303 22 11.5353 22 12C22 12.4647 22 12.697 21.9616 12.8902C21.8038 13.6836 21.1836 14.3038 20.3902 14.4616C20.197 14.5 19.9647 14.5 19.5 14.5H17.5C17.0353 14.5 16.803 14.5 16.6098 14.4616C15.8164 14.3038 15.1962 13.6836 15.0384 12.8902C15 12.697 15 12.4647 15 12Z'
        stroke='currentColor'
        stroke-width='2'
        stroke-linecap='round'
        stroke-linejoin='round'
      />
    </svg>
  ),
  successful: (props: IconProps) => (
    <svg
      width='40'
      height='34'
      viewBox='0 0 40 34'
      fill='none'
      stroke='currentColor'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <path
        d='M32 33.5H8C3.164 33.5 0.5 30.836 0.5 26V8C0.5 3.164 3.164 0.5 8 0.5H32C36.836 0.5 39.5 3.164 39.5 8V26C39.5 30.836 36.836 33.5 32 33.5ZM8 3.5C4.846 3.5 3.5 4.846 3.5 8V26C3.5 29.154 4.846 30.5 8 30.5H32C35.154 30.5 36.5 29.154 36.5 26V8C36.5 4.846 35.154 3.5 32 3.5H8ZM22.0581 18.358L31.8818 11.214C32.5518 10.728 32.6999 9.78801 32.2119 9.11801C31.7259 8.45001 30.7902 8.29799 30.1162 8.78799L20.292 15.932C20.116 16.06 19.8821 16.06 19.7061 15.932L9.88184 8.78799C9.20584 8.29799 8.27213 8.45201 7.78613 9.11801C7.29813 9.78801 7.44621 10.726 8.11621 11.214L17.9399 18.36C18.5559 18.808 19.278 19.03 19.998 19.03C20.718 19.03 21.4441 18.806 22.0581 18.358Z'
        fill='currentColor'
      />
    </svg>
  ),
  failed: (props: IconProps) => (
    <svg
      width='40'
      height='34'
      viewBox='0 0 40 34'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      stroke='currentColor'
      {...props}
    >
      <path
        d='M32 33.5H8C3.164 33.5 0.5 30.836 0.5 26V8C0.5 3.164 3.164 0.5 8 0.5H32C36.836 0.5 39.5 3.164 39.5 8V26C39.5 30.836 36.836 33.5 32 33.5ZM8 3.5C4.846 3.5 3.5 4.846 3.5 8V26C3.5 29.154 4.846 30.5 8 30.5H32C35.154 30.5 36.5 29.154 36.5 26V8C36.5 4.846 35.154 3.5 32 3.5H8ZM22.0581 18.358L31.8818 11.214C32.5518 10.728 32.6999 9.78801 32.2119 9.11801C31.7259 8.45001 30.7902 8.29799 30.1162 8.78799L20.292 15.932C20.116 16.06 19.8821 16.06 19.7061 15.932L9.88184 8.78799C9.20584 8.29799 8.27213 8.45201 7.78613 9.11801C7.29813 9.78801 7.44621 10.726 8.11621 11.214L17.9399 18.36C18.5559 18.808 19.278 19.03 19.998 19.03C20.718 19.03 21.4441 18.806 22.0581 18.358Z'
        fill='currentColor'
      />
    </svg>
  ),
  location: (props: IconProps) => (
    <svg
      width='16'
      height='16'
      viewBox='0 0 16 16'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
      stroke='currentColor'
    >
      <path
        d='M7.99967 1.66699C4.87501 1.66699 2.33301 4.20899 2.33301 7.33366C2.33301 10.6557 5.41901 12.6943 7.46101 14.043L7.81434 14.2777C7.87034 14.315 7.935 14.3337 7.999 14.3337C8.063 14.3337 8.12768 14.315 8.18368 14.2777L8.53701 14.043C10.579 12.6943 13.665 10.6557 13.665 7.33366C13.6663 4.20899 11.1243 1.66699 7.99967 1.66699ZM7.99967 9.00033C7.07901 9.00033 6.33301 8.25433 6.33301 7.33366C6.33301 6.41299 7.07901 5.66699 7.99967 5.66699C8.92034 5.66699 9.66634 6.41299 9.66634 7.33366C9.66634 8.25433 8.92034 9.00033 7.99967 9.00033Z'
        fill='currentColor'
      />
    </svg>
  ),
  calender: (props: IconProps) => (
    <svg
      width='14'
      height='14'
      viewBox='0 0 14 14'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
      stroke='currentColor'
    >
      <path
        d='M11 1.5H10.1667V1C10.1667 0.724 9.94267 0.5 9.66667 0.5C9.39067 0.5 9.16667 0.724 9.16667 1V1.5H4.83333V1C4.83333 0.724 4.60933 0.5 4.33333 0.5C4.05733 0.5 3.83333 0.724 3.83333 1V1.5H3C1.388 1.5 0.5 2.388 0.5 4V11C0.5 12.612 1.388 13.5 3 13.5H11C12.612 13.5 13.5 12.612 13.5 11V4C13.5 2.388 12.612 1.5 11 1.5ZM3 2.5H3.83333V3C3.83333 3.276 4.05733 3.5 4.33333 3.5C4.60933 3.5 4.83333 3.276 4.83333 3V2.5H9.16667V3C9.16667 3.276 9.39067 3.5 9.66667 3.5C9.94267 3.5 10.1667 3.276 10.1667 3V2.5H11C12.0513 2.5 12.5 2.94867 12.5 4V4.5H1.5V4C1.5 2.94867 1.94867 2.5 3 2.5ZM11 12.5H3C1.94867 12.5 1.5 12.0513 1.5 11V5.5H12.5V11C12.5 12.0513 12.0513 12.5 11 12.5ZM5.01335 7.66667C5.01335 8.03467 4.71535 8.33333 4.34668 8.33333C3.97868 8.33333 3.6766 8.03467 3.6766 7.66667C3.6766 7.29867 3.97201 7 4.34001 7H4.34668C4.71468 7 5.01335 7.29867 5.01335 7.66667ZM7.68001 7.66667C7.68001 8.03467 7.38201 8.33333 7.01335 8.33333C6.64535 8.33333 6.34326 8.03467 6.34326 7.66667C6.34326 7.29867 6.63867 7 7.00667 7H7.01335C7.38135 7 7.68001 7.29867 7.68001 7.66667ZM10.3467 7.66667C10.3467 8.03467 10.0487 8.33333 9.68001 8.33333C9.31201 8.33333 9.00993 8.03467 9.00993 7.66667C9.00993 7.29867 9.30534 7 9.67334 7H9.68001C10.048 7 10.3467 7.29867 10.3467 7.66667ZM5.01335 10.3333C5.01335 10.7013 4.71535 11 4.34668 11C3.97868 11 3.6766 10.7013 3.6766 10.3333C3.6766 9.96533 3.97201 9.66667 4.34001 9.66667H4.34668C4.71468 9.66667 5.01335 9.96533 5.01335 10.3333ZM7.68001 10.3333C7.68001 10.7013 7.38201 11 7.01335 11C6.64535 11 6.34326 10.7013 6.34326 10.3333C6.34326 9.96533 6.63867 9.66667 7.00667 9.66667H7.01335C7.38135 9.66667 7.68001 9.96533 7.68001 10.3333ZM10.3467 10.3333C10.3467 10.7013 10.0487 11 9.68001 11C9.31201 11 9.00993 10.7013 9.00993 10.3333C9.00993 9.96533 9.30534 9.66667 9.67334 9.66667H9.68001C10.048 9.66667 10.3467 9.96533 10.3467 10.3333Z'
        fill='currentColor'
      />
    </svg>
  ),
  arrow_right: (props: IconProps) => (
    <svg
      width='17'
      height='16'
      viewBox='0 0 17 16'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
      stroke='currentColor'
    >
      <path
        d='M0.5 7.00008V9.00008H12.5L7 14.5001L8.42 15.9201L16.34 8.00008L8.42 0.0800781L7 1.50008L12.5 7.00008H0.5Z'
        fill='currentColor'
      />
    </svg>
  ),
  addIcon: (props: IconProps) => (
    <svg
      width='30'
      height='30'
      viewBox='0 0 40 40'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <rect x='0.5' y='0.5' width='39' height='39' rx='7.5' stroke='#A1A1AA' />
      <g clip-path='url(#clip0_7918_13794)'>
        <path
          d='M20 10L20 30M30 20L10 20'
          stroke='#A1A1AA'
          stroke-width='1.5'
          stroke-linecap='round'
        />
      </g>
      <defs>
        <clipPath id='clip0_7918_13794'>
          <rect x='5' y='5' width='30' height='30' rx='8' fill='white' />
        </clipPath>
      </defs>
    </svg>
  ),
  features: (props: IconProps) => (
    <svg
      width='14'
      height='20'
      viewBox='0 0 14 20'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
      stroke='currentColor'
    >
      <path
        d='M12 0H1.33333C0.979833 0.000397018 0.640925 0.141 0.390963 0.390963C0.141 0.640925 0.000397018 0.979833 0 1.33333V18.6667C0.000397018 19.0202 0.141 19.3591 0.390963 19.609C0.640925 19.859 0.979833 19.9996 1.33333 20H12C12.3535 19.9996 12.6924 19.859 12.9424 19.609C13.1923 19.3591 13.3329 19.0202 13.3333 18.6667V1.33333C13.3329 0.979833 13.1923 0.640925 12.9424 0.390963C12.6924 0.141 12.3535 0.000397018 12 0ZM12 18.6667H1.33333V1.33333H12L12.0009 18.6667H12Z'
        fill='currentColor'
      />
      <path
        d='M6.66651 9.33301C5.87538 9.33301 5.10202 9.5676 4.44422 10.0071C3.78643 10.4467 3.27374 11.0714 2.97099 11.8023C2.66824 12.5332 2.58902 13.3374 2.74336 14.1134C2.89771 14.8893 3.27867 15.602 3.83808 16.1614C4.39749 16.7208 5.11022 17.1018 5.88614 17.2561C6.66207 17.4105 7.46633 17.3313 8.19724 17.0285C8.92814 16.7258 9.55286 16.2131 9.99238 15.5553C10.4319 14.8975 10.6665 14.1241 10.6665 13.333C10.6653 12.2725 10.2435 11.2558 9.49362 10.5059C8.74373 9.75601 7.72701 9.3342 6.66651 9.33301ZM6.66651 15.9997C6.13909 15.9997 5.62352 15.8433 5.18498 15.5503C4.74645 15.2572 4.40466 14.8408 4.20283 14.3535C4.00099 13.8662 3.94818 13.33 4.05108 12.8128C4.15397 12.2955 4.40795 11.8203 4.78089 11.4474C5.15383 11.0745 5.62898 10.8205 6.14626 10.7176C6.66355 10.6147 7.19972 10.6675 7.68699 10.8693C8.17426 11.0712 8.59074 11.413 8.88376 11.8515C9.17677 12.29 9.33317 12.8056 9.33317 13.333C9.33238 14.04 9.05117 14.7178 8.55125 15.2177C8.05132 15.7177 7.37351 15.9989 6.66651 15.9997Z'
        fill='currentColor'
      />
      <path
        d='M6.66667 7.66634C7.19408 7.66634 7.70966 7.50994 8.14819 7.21693C8.58672 6.92391 8.92851 6.50743 9.13035 6.02016C9.33218 5.5329 9.38499 4.99672 9.28209 4.47943C9.1792 3.96215 8.92522 3.487 8.55228 3.11406C8.17934 2.74112 7.70419 2.48714 7.18691 2.38425C6.66962 2.28135 6.13345 2.33416 5.64618 2.536C5.15891 2.73783 4.74243 3.07962 4.44941 3.51815C4.1564 3.95669 4 4.47226 4 4.99968C4.00079 5.70668 4.282 6.38449 4.78193 6.88442C5.28185 7.38434 5.95967 7.66555 6.66667 7.66634ZM6.66667 3.66634C6.93037 3.66634 7.18816 3.74454 7.40743 3.89105C7.62669 4.03756 7.79759 4.2458 7.89851 4.48943C7.99942 4.73307 8.02583 5.00115 7.97438 5.2598C7.92293 5.51844 7.79595 5.75601 7.60948 5.94248C7.42301 6.12895 7.18543 6.25594 6.92679 6.30739C6.66815 6.35884 6.40006 6.33243 6.15642 6.23151C5.91279 6.1306 5.70455 5.9597 5.55804 5.74044C5.41153 5.52117 5.33333 5.26338 5.33333 4.99968C5.33373 4.64618 5.47433 4.30727 5.7243 4.05731C5.97426 3.80734 6.31317 3.66674 6.66667 3.66634Z'
        fill='currentColor'
      />
      <path d='M7.33333 12.667H6V14.0003H7.33333V12.667Z' fill='currentColor' />
    </svg>
  ),
  question_circle: (props: IconProps) => (
    <svg
      width='18'
      height='18'
      viewBox='0 0 18 18'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
      stroke='currentColor'
    >
      <path
        d='M8.99984 17.9587C4.05984 17.9587 0.0415039 13.9403 0.0415039 9.00033C0.0415039 4.06033 4.05984 0.0419922 8.99984 0.0419922C13.9398 0.0419922 17.9582 4.06033 17.9582 9.00033C17.9582 13.9403 13.9398 17.9587 8.99984 17.9587ZM8.99984 1.29199C4.749 1.29199 1.2915 4.74949 1.2915 9.00033C1.2915 13.2512 4.749 16.7087 8.99984 16.7087C13.2507 16.7087 16.7082 13.2512 16.7082 9.00033C16.7082 4.74949 13.2507 1.29199 8.99984 1.29199ZM9.59229 10.137C9.61229 10.077 9.72569 9.84954 10.3357 9.4412C11.319 8.78204 11.759 7.84028 11.5765 6.78862C11.3915 5.72612 10.5256 4.85368 9.47062 4.66785C8.69979 4.53118 7.91898 4.7386 7.32564 5.23527C6.72648 5.73777 6.38326 6.47523 6.38326 7.2594C6.38326 7.6044 6.66326 7.8844 7.00826 7.8844C7.35326 7.8844 7.63326 7.6044 7.63326 7.2594C7.63326 6.84607 7.81407 6.45708 8.12907 6.19291C8.4424 5.93041 8.8423 5.82291 9.25313 5.89791C9.79147 5.99291 10.2507 6.45703 10.3457 7.00203C10.3815 7.20787 10.4907 7.83281 9.63989 8.40198C8.95073 8.86448 8.55904 9.28865 8.4082 9.73865C8.2982 10.0661 8.47479 10.4203 8.80229 10.5303C8.86812 10.5528 8.93482 10.5628 9.00065 10.5628C9.26065 10.5628 9.50395 10.3978 9.59229 10.137ZM9.84985 12.7503C9.84985 12.2903 9.47735 11.917 9.01652 11.917H9.00818C8.54818 11.917 8.17891 12.2903 8.17891 12.7503C8.17891 13.2103 8.55652 13.5837 9.01652 13.5837C9.47652 13.5837 9.84985 13.2103 9.84985 12.7503Z'
        fill='currentColor'
        fill-opacity='0.7'
      />
    </svg>
  ),
  question_square: (props: IconProps) => (
    <svg
      width='18'
      height='18'
      viewBox='0 0 18 18'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
      stroke='currentColor'
    >
      <path
        d='M9 10.5625C8.93417 10.5625 8.86747 10.5524 8.80164 10.5299C8.47414 10.4199 8.29839 10.0658 8.40755 9.73832C8.55839 9.28832 8.94924 8.86415 9.63924 8.40165C10.4901 7.83165 10.3808 7.20671 10.345 7.00171C10.2508 6.45671 9.79165 5.99258 9.25248 5.89758C8.84165 5.82258 8.44175 5.93009 8.12842 6.19259C7.81342 6.45675 7.63241 6.84574 7.63241 7.25907C7.63241 7.60407 7.35241 7.88407 7.00741 7.88407C6.66241 7.88407 6.38241 7.60407 6.38241 7.25907C6.38241 6.47491 6.72583 5.73744 7.32499 5.23494C7.91833 4.73828 8.69914 4.53085 9.46997 4.66752C10.5258 4.85419 11.3917 5.72662 11.5759 6.78829C11.7584 7.83912 11.3175 8.78171 10.335 9.44088C9.72588 9.84921 9.61247 10.0767 9.59163 10.1367C9.50413 10.3975 9.26083 10.5625 9 10.5625ZM9.85002 12.75C9.85002 12.29 9.47752 11.9167 9.01668 11.9167H9.00834C8.54834 11.9167 8.17908 12.29 8.17908 12.75C8.17908 13.21 8.55668 13.5833 9.01668 13.5833C9.47668 13.5833 9.85002 13.21 9.85002 12.75ZM17.125 13.6875V4.3125C17.125 2.09583 15.9042 0.875 13.6875 0.875H4.3125C2.09583 0.875 0.875 2.09583 0.875 4.3125V13.6875C0.875 15.9042 2.09583 17.125 4.3125 17.125H13.6875C15.9042 17.125 17.125 15.9042 17.125 13.6875ZM13.6875 2.125C15.2208 2.125 15.875 2.77917 15.875 4.3125V13.6875C15.875 15.2208 15.2208 15.875 13.6875 15.875H4.3125C2.77917 15.875 2.125 15.2208 2.125 13.6875V4.3125C2.125 2.77917 2.77917 2.125 4.3125 2.125H13.6875Z'
        fill='currentColor'
        fill-opacity='0.7'
      />
    </svg>
  ),
  note: (props: IconProps) => (
    <svg
      width='16'
      height='16'
      viewBox='0 0 16 16'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
      stroke='currentColor'
    >
      <path
        d='M15.9168 3.31217C15.9168 1.19967 14.8002 0.0830078 12.6877 0.0830078H3.31266C1.20016 0.0830078 0.0834961 1.19967 0.0834961 3.31217V12.6872C0.0834961 14.7997 1.20016 15.9163 3.31266 15.9163H8.71102C9.26769 15.9163 9.79591 15.763 10.2692 15.4997H10.2926V15.4872C10.5443 15.3447 10.7843 15.1805 10.9943 14.9705L14.9718 10.993C15.1818 10.783 15.3461 10.543 15.4886 10.2913H15.5002V10.268C15.7635 9.79471 15.9168 9.26638 15.9168 8.70972V3.31217ZM3.31266 15.083C1.67766 15.083 0.916829 14.3213 0.916829 12.6872V3.31217C0.916829 1.67801 1.67766 0.916341 3.31266 0.916341H12.6877C14.3227 0.916341 15.0835 1.67801 15.0835 3.31217V8.70972C15.0835 8.96888 15.0327 9.21884 14.9535 9.45801H12.0627C10.6268 9.45801 9.4585 10.6263 9.4585 12.0622V14.953C9.21933 15.0313 8.97019 15.083 8.71102 15.083H3.31266ZM14.3818 10.4038L10.4043 14.3813C10.3693 14.4163 10.3285 14.4414 10.2918 14.4739V12.0622C10.2918 11.0855 11.086 10.2913 12.0627 10.2913H14.4744C14.4419 10.328 14.4168 10.3688 14.3818 10.4038ZM4.25016 5.91634C4.25016 5.68634 4.43683 5.49967 4.66683 5.49967H11.3335C11.5635 5.49967 11.7502 5.68634 11.7502 5.91634C11.7502 6.14634 11.5635 6.33301 11.3335 6.33301H4.66683C4.43683 6.33301 4.25016 6.14634 4.25016 5.91634ZM8.41683 9.66634C8.41683 9.89634 8.23016 10.083 8.00016 10.083H4.66683C4.43683 10.083 4.25016 9.89634 4.25016 9.66634C4.25016 9.43634 4.43683 9.24967 4.66683 9.24967H8.00016C8.23016 9.24967 8.41683 9.43634 8.41683 9.66634Z'
        fill='currentColor'
        fill-opacity='0.7'
      />
    </svg>
  ),
  back: (props: IconProps) => (
    <svg
      width='18'
      height='18'
      viewBox='0 0 18 18'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <path
        d='M8.99984 0.041748C4.05984 0.041748 0.0415039 4.06008 0.0415039 9.00008C0.0415039 13.9401 4.05984 17.9584 8.99984 17.9584C13.9398 17.9584 17.9582 13.9401 17.9582 9.00008C17.9582 4.06008 13.9398 0.041748 8.99984 0.041748ZM8.99984 16.7084C4.749 16.7084 1.2915 13.2509 1.2915 9.00008C1.2915 4.74925 4.749 1.29175 8.99984 1.29175C13.2507 1.29175 16.7082 4.74925 16.7082 9.00008C16.7082 13.2509 13.2507 16.7084 8.99984 16.7084ZM12.9582 9.00008C12.9582 9.34508 12.6782 9.62508 12.3332 9.62508H7.1757L8.60901 11.0584C8.85318 11.3026 8.85318 11.6984 8.60901 11.9426C8.48734 12.0643 8.32732 12.1259 8.16732 12.1259C8.00732 12.1259 7.84729 12.0651 7.72563 11.9426L5.22563 9.44259C5.16813 9.38509 5.12241 9.31601 5.09074 9.23934C5.02741 9.08684 5.02741 8.91434 5.09074 8.76184C5.12241 8.68517 5.16813 8.61589 5.22563 8.55839L7.72563 6.05839C7.96979 5.81422 8.36566 5.81422 8.60982 6.05839C8.85399 6.30256 8.85399 6.69842 8.60982 6.94259L7.17651 8.3759H12.3332C12.6782 8.37506 12.9582 8.65508 12.9582 9.00008Z'
        fill='#1A1A1A'
      />
    </svg>
  ),
  successPaystack: (props: IconProps) => (
    <svg
      width='40'
      height='34'
      viewBox='0 0 40 34'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <path
        d='M32 33.5H8C3.164 33.5 0.5 30.836 0.5 26V8C0.5 3.164 3.164 0.5 8 0.5H32C36.836 0.5 39.5 3.164 39.5 8V26C39.5 30.836 36.836 33.5 32 33.5ZM8 3.5C4.846 3.5 3.5 4.846 3.5 8V26C3.5 29.154 4.846 30.5 8 30.5H32C35.154 30.5 36.5 29.154 36.5 26V8C36.5 4.846 35.154 3.5 32 3.5H8ZM22.0581 18.358L31.8818 11.214C32.5518 10.728 32.6999 9.78801 32.2119 9.11801C31.7259 8.45001 30.7902 8.29799 30.1162 8.78799L20.292 15.932C20.116 16.06 19.8821 16.06 19.7061 15.932L9.88184 8.78799C9.20584 8.29799 8.27213 8.45201 7.78613 9.11801C7.29813 9.78801 7.44621 10.726 8.11621 11.214L17.9399 18.36C18.5559 18.808 19.278 19.03 19.998 19.03C20.718 19.03 21.4441 18.806 22.0581 18.358Z'
        fill='#0BF04C'
      />
    </svg>
  ),
  places: (props: IconProps) => (
    <svg
      width='20'
      height='20'
      viewBox='0 0 15 17'
      fill='none'
      {...props}
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M4.60693 3.38721C4.60693 4.72021 5.50049 5.84814 6.73096 6.18506V10.7993C6.73096 12.7915 7.17773 14.022 7.5 14.022C7.81494 14.022 8.25439 12.7988 8.25439 10.7993V6.18506C9.49219 5.85547 10.3931 4.72021 10.3931 3.38721C10.3931 1.79053 9.104 0.479492 7.5 0.479492C5.88867 0.479492 4.60693 1.79053 4.60693 3.38721ZM6.67969 3.54834C6.15234 3.54834 5.68359 3.08691 5.68359 2.55225C5.68359 2.01758 6.15234 1.56348 6.67969 1.56348C7.22168 1.56348 7.66846 2.01758 7.66846 2.55225C7.66846 3.08691 7.22168 3.54834 6.67969 3.54834ZM7.49268 16.8857C12.1362 16.8857 14.8975 15.3257 14.8975 13.5386C14.8975 11.5684 11.8726 10.3086 9.49951 10.2134V11.5024C11.1108 11.5684 13.2349 12.3301 13.2349 13.3921C13.2349 14.6519 10.8179 15.5234 7.49268 15.5234C4.16748 15.5234 1.75781 14.6592 1.75781 13.3921C1.75781 12.3301 3.88184 11.5684 5.48584 11.5024V10.2134C3.12012 10.3086 0.0952148 11.5684 0.0952148 13.5386C0.0952148 15.3257 2.85645 16.8857 7.49268 16.8857Z'
        fill='#71717A'
      />
    </svg>
  ),
  newLocation: (props: IconProps) => (
    <svg
      width='12'
      height='16'
      viewBox='0 0 12 16'
      fill='none'
      {...props}
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fill-rule='evenodd'
        clip-rule='evenodd'
        d='M5.6 7.6C4.5 7.6 3.6 6.7 3.6 5.6C3.6 4.5 4.5 3.6 5.6 3.6C6.7 3.6 7.6 4.5 7.6 5.6C7.6 6.7 6.7 7.6 5.6 7.6ZM5.6 0C2.5 0 0 2.5 0 5.6C0 9.8 5.6 16 5.6 16C5.6 16 11.2 9.8 11.2 5.6C11.2 2.5 8.7 0 5.6 0Z'
        fill='white'
      />
    </svg>
  ),
  instagram: (props: IconProps) => (
    <svg
      width='20'
      height='20'
      viewBox='0 0 20 20'
      fill='none'
      {...props}
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fill-rule='evenodd'
        clip-rule='evenodd'
        d='M9.71772 0.666992C7.18255 0.666992 6.86522 0.678659 5.87005 0.722992C4.87722 0.768492 4.19939 0.925992 3.60555 1.15699C2.98282 1.39104 2.41865 1.75813 1.95239 2.23266C1.47772 2.69926 1.11062 3.26384 0.876719 3.88699C0.645719 4.48083 0.488219 5.15866 0.442719 6.15149C0.398385 7.14549 0.386719 7.46283 0.386719 9.99799C0.386719 12.5308 0.398385 12.8493 0.442719 13.8445C0.488219 14.8373 0.645719 15.5152 0.876719 16.109C1.11472 16.7227 1.43439 17.243 1.95239 17.7622C2.47155 18.2802 2.99072 18.5998 3.60555 18.8378C4.19939 19.0688 4.87722 19.2263 5.87005 19.2718C6.86522 19.3173 7.18255 19.3278 9.71772 19.3278C12.2506 19.3278 12.5691 19.3162 13.5642 19.2718C14.5571 19.2263 15.2349 19.0688 15.8287 18.8378C16.4514 18.6037 17.0156 18.2367 17.4819 17.7622C17.9999 17.243 18.3196 16.7227 18.5576 16.109C18.7886 15.5152 18.9461 14.8373 18.9916 13.8445C19.0371 12.8493 19.0476 12.5308 19.0476 9.99799C19.0476 7.46283 19.0359 7.14549 18.9916 6.15033C18.9461 5.15749 18.7886 4.47966 18.5576 3.88583C18.3235 3.26309 17.9564 2.69892 17.4819 2.23266C17.0156 1.75813 16.4515 1.39104 15.8287 1.15699C15.2349 0.925992 14.5571 0.768492 13.5642 0.722992C12.5691 0.678659 12.2506 0.666992 9.71772 0.666992ZM9.71772 2.34816C12.2086 2.34816 12.5037 2.35749 13.4872 2.40299C14.3972 2.44383 14.8907 2.59549 15.2197 2.72383C15.6549 2.89299 15.9664 3.09483 16.2931 3.42149C16.6197 3.74816 16.8216 4.05849 16.9907 4.49483C17.1191 4.82383 17.2707 5.31733 17.3116 6.22733C17.3571 7.21083 17.3664 7.50599 17.3664 9.99799C17.3664 12.4888 17.3571 12.784 17.3116 13.7675C17.2707 14.6775 17.1179 15.171 16.9907 15.5C16.8216 15.9352 16.6197 16.2467 16.2931 16.5733C15.9664 16.9 15.6549 17.1018 15.2197 17.271C14.8907 17.3993 14.3972 17.551 13.4872 17.5918C12.5037 17.6373 12.2086 17.6467 9.71772 17.6467C7.22572 17.6467 6.93055 17.6373 5.94705 17.5918C5.03705 17.551 4.54355 17.3982 4.21455 17.271C3.80913 17.1216 3.44237 16.8832 3.14122 16.5733C2.83135 16.2722 2.59294 15.9054 2.44355 15.5C2.31522 15.171 2.16355 14.6775 2.12272 13.7675C2.07722 12.784 2.06789 12.4888 2.06789 9.99799C2.06789 7.50599 2.07722 7.21083 2.12272 6.22733C2.16355 5.31733 2.31639 4.82383 2.44355 4.49483C2.61272 4.05849 2.81455 3.74816 3.14122 3.42149C3.46789 3.09483 3.77939 2.89299 4.21455 2.72383C4.54355 2.59549 5.03705 2.44383 5.94705 2.40299C6.93055 2.35749 7.22572 2.34816 9.71772 2.34816Z'
        fill='#1B1B1B'
      />
      <path
        fill-rule='evenodd'
        clip-rule='evenodd'
        d='M9.71783 13.1073C9.30438 13.1153 8.89349 13.0408 8.50918 12.8881C8.12488 12.7354 7.77488 12.5077 7.47965 12.2181C7.18443 11.9285 6.94991 11.583 6.78981 11.2017C6.62972 10.8205 6.54726 10.4111 6.54726 9.99757C6.54726 9.58404 6.62972 9.17467 6.78981 8.7934C6.94991 8.41212 7.18443 8.0666 7.47965 7.77704C7.77488 7.48748 8.12488 7.25969 8.50918 7.10701C8.89349 6.95433 9.30438 6.87981 9.71783 6.88782C10.5322 6.90359 11.3078 7.23816 11.8781 7.81963C12.4484 8.40111 12.7679 9.18309 12.7679 9.99757C12.7679 10.812 12.4484 11.594 11.8781 12.1755C11.3078 12.757 10.5322 13.0915 9.71783 13.1073ZM9.71783 5.20665C9.08238 5.19673 8.45132 5.31333 7.86137 5.54964C7.27142 5.78596 6.73437 6.13727 6.2815 6.58313C5.82862 7.029 5.46897 7.56049 5.22348 8.14668C4.97799 8.73288 4.85156 9.36205 4.85156 9.99757C4.85156 10.6331 4.97799 11.2623 5.22348 11.8485C5.46897 12.4346 5.82862 12.9661 6.2815 13.412C6.73437 13.8579 7.27142 14.2092 7.86137 14.4455C8.45132 14.6818 9.08238 14.7984 9.71783 14.7885C10.9755 14.7689 12.175 14.2555 13.0575 13.3591C13.94 12.4628 14.4346 11.2554 14.4346 9.99757C14.4346 8.73973 13.94 7.53234 13.0575 6.636C12.175 5.73967 10.9755 5.22628 9.71783 5.20665ZM15.8172 5.01648C15.8172 5.31353 15.6992 5.5984 15.4891 5.80844C15.2791 6.01848 14.9942 6.13648 14.6972 6.13648C14.4001 6.13648 14.1152 6.01848 13.9052 5.80844C13.6952 5.5984 13.5772 5.31353 13.5772 5.01648C13.5772 4.71944 13.6952 4.43457 13.9052 4.22452C14.1152 4.01448 14.4001 3.89648 14.6972 3.89648C14.9942 3.89648 15.2791 4.01448 15.4891 4.22452C15.6992 4.43457 15.8172 4.71944 15.8172 5.01648Z'
        fill='#1B1B1B'
      />
    </svg>
  ),
  tiktok: (props: IconProps) => (
    <svg
      width='17'
      height='20'
      viewBox='0 0 17 20'
      fill='none'
      {...props}
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M12.5593 0.666992H9.33695V13.3825C9.33324 14.117 9.03832 14.82 8.51688 15.3373C7.99545 15.8545 7.2901 16.1438 6.55562 16.1417C5.82093 16.1441 5.11528 15.855 4.5936 15.3377C4.07191 14.8204 3.77682 14.1172 3.77311 13.3825C3.77311 11.8938 4.98528 10.6758 6.47278 10.6222V7.43016C3.19445 7.48383 0.550781 10.1088 0.550781 13.3813C0.550781 16.6842 3.25045 19.3337 6.58245 19.3337C9.91562 19.3337 12.6141 16.655 12.6141 13.3825V6.86199C13.8625 7.75189 15.3513 8.24246 16.8841 8.26899V5.07699C14.4598 4.99533 12.5593 3.04699 12.5593 0.666992Z'
        fill='#1B1B1B'
      />
    </svg>
  ),
  linkedIn: (props: IconProps) => (
    <svg
      width='20'
      height='19'
      viewBox='0 0 20 19'
      fill='none'
      {...props}
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fill-rule='evenodd'
        clip-rule='evenodd'
        d='M3.88672 18.167V6.50033H0.386719V18.167H3.88672ZM2.12505 4.16699C3.21122 4.16699 3.88672 3.38883 3.88672 2.41699C3.86689 1.42299 3.21122 0.666992 2.14722 0.666992C1.08205 0.666992 0.386719 1.42299 0.386719 2.41699C0.386719 3.38883 1.06222 4.16699 2.10639 4.16699H2.12622H2.12505ZM7.38672 18.167H11.2087V11.8017C11.2087 11.461 11.2332 11.1203 11.3347 10.8777C11.6101 10.1963 12.2401 9.49166 13.2947 9.49166C14.6784 9.49166 15.2314 10.537 15.2314 12.07V18.167H19.0534V11.6302C19.0534 8.13016 17.1681 6.50033 14.6539 6.50033C12.5924 6.50033 11.6859 7.64366 11.1831 8.42066H11.2087V6.76749H7.38672C7.43689 7.83733 7.38672 18.167 7.38672 18.167Z'
        fill='#1B1B1B'
      />
    </svg>
  ),
  youtube: (props: IconProps) => (
    <svg
      width='20'
      height='14'
      viewBox='0 0 20 14'
      fill='none'
      {...props}
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fill-rule='evenodd'
        clip-rule='evenodd'
        d='M17.0059 0.417666C17.8121 0.6545 18.4491 1.33233 18.6614 2.191C19.0534 3.74733 19.0534 7 19.0534 7C19.0534 7 19.0534 10.2515 18.6719 11.8102C18.4491 12.6793 17.8225 13.356 17.0176 13.5823C15.5534 14 9.72005 14 9.72005 14C9.72005 14 3.88672 14 2.43422 13.5823C1.62805 13.3455 0.991052 12.6677 0.778719 11.809C0.386719 10.2527 0.386719 7 0.386719 7C0.386719 7 0.386719 3.7485 0.768219 2.18983C0.991052 1.32067 1.61755 0.644 2.42255 0.417666C3.88672 -2.43386e-07 9.72005 0 9.72005 0C9.72005 0 15.5534 -2.43386e-07 17.0059 0.417666ZM12.6367 7L7.97005 9.91667V4.08333L12.6367 7Z'
        fill='#1B1B1B'
      />
    </svg>
  ),
  newsLetter: (props: IconProps) => (
    <svg
      width='25'
      height='25'
      viewBox='0 0 25 25'
      fill='none'
      {...props}
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fill-rule='evenodd'
        clip-rule='evenodd'
        d='M22.7975 2.68352C22.4186 2.75092 21.8813 2.92722 21.0212 3.21395L9.20009 7.15431C6.81656 7.94882 5.1293 8.51352 4.02659 9.04996C2.83983 9.62729 2.82422 9.94022 2.82422 10.0004C2.82422 10.0605 2.83983 10.3734 4.02659 10.9507C5.1293 11.4872 6.81656 12.0519 9.20009 12.8464L9.41852 12.9192C9.47193 12.937 9.52496 12.9546 9.5776 12.972C10.3488 13.2275 11.0344 13.4547 11.5398 13.9602C12.0453 14.4657 12.2725 15.1512 12.528 15.9224C12.5454 15.9751 12.563 16.0281 12.5808 16.0815L12.6536 16.2999C13.4481 18.6835 14.0128 20.3707 14.5493 21.4734C15.1266 22.6602 15.4395 22.6758 15.4997 22.6758C15.5598 22.6758 15.8727 22.6602 16.4501 21.4734C16.9865 20.3707 17.5512 18.6835 18.3457 16.2999L22.2861 4.47886C22.5728 3.61869 22.7491 3.08146 22.8165 2.70256C22.8179 2.69467 22.8192 2.68699 22.8205 2.67953C22.813 2.68078 22.8053 2.68211 22.7975 2.68352ZM23.0564 2.6604C23.0562 2.66058 23.0529 2.66031 23.0472 2.65905C23.0538 2.65959 23.0566 2.66022 23.0564 2.6604ZM22.841 2.45283C22.8397 2.44708 22.8394 2.44379 22.8396 2.44359C22.8398 2.44338 22.8404 2.44625 22.841 2.45283ZM22.4472 0.714429C22.9982 0.616412 23.7527 0.587225 24.3327 1.16728C24.9128 1.74734 24.8836 2.50185 24.7856 3.05284C24.6893 3.59427 24.4628 4.27339 24.2073 5.03967L24.1834 5.11132L20.2431 16.9324L20.216 17.0135C19.4545 19.2982 18.8498 21.1124 18.2485 22.3484C17.6811 23.5148 16.881 24.6758 15.4997 24.6758C14.1183 24.6758 13.3182 23.5148 12.7508 22.3484C12.1495 21.1124 11.5448 19.2982 10.7833 17.0135L10.7562 16.9324L10.6834 16.7139C10.3413 15.6877 10.2533 15.5021 10.1256 15.3744C9.99792 15.2467 9.81236 15.1587 8.78607 14.8166L8.56763 14.7438L8.48658 14.7168C6.20185 13.9552 4.38759 13.3505 3.15167 12.7492C1.98522 12.1818 0.824219 11.3817 0.824219 10.0004C0.824219 8.61898 1.98522 7.81894 3.15167 7.25148C4.38758 6.65024 6.20182 6.04552 8.48653 5.28397L8.56763 5.25694L20.3887 1.31658L20.4603 1.29271C21.2266 1.03719 21.9057 0.810746 22.4472 0.714429Z'
        fill='white'
      />
    </svg>
  ),
  android: (props: IconProps) => (
    <svg
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
      {...props}
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M4 11C4 10.7348 4.10536 10.4804 4.29289 10.2929C4.48043 10.1054 4.73478 10 5 10C5.26522 10 5.51957 10.1054 5.70711 10.2929C5.89464 10.4804 6 10.7348 6 11V15C6 15.2652 5.89464 15.5196 5.70711 15.7071C5.51957 15.8946 5.26522 16 5 16C4.73478 16 4.48043 15.8946 4.29289 15.7071C4.10536 15.5196 4 15.2652 4 15V11ZM18 11C18 10.7348 18.1054 10.4804 18.2929 10.2929C18.4804 10.1054 18.7348 10 19 10C19.2652 10 19.5196 10.1054 19.7071 10.2929C19.8946 10.4804 20 10.7348 20 11V15C20 15.2652 19.8946 15.5196 19.7071 15.7071C19.5196 15.8946 19.2652 16 19 16C18.7348 16 18.4804 15.8946 18.2929 15.7071C18.1054 15.5196 18 15.2652 18 15V11ZM7 10H17V17C17 17.2652 16.8946 17.5196 16.7071 17.7071C16.5196 17.8946 16.2652 18 16 18H15V20C15 20.2652 14.8946 20.5196 14.7071 20.7071C14.5196 20.8946 14.2652 21 14 21C13.7348 21 13.4804 20.8946 13.2929 20.7071C13.1054 20.5196 13 20.2652 13 20V18H11V20C11 20.2652 10.8946 20.5196 10.7071 20.7071C10.5196 20.8946 10.2652 21 10 21C9.73478 21 9.48043 20.8946 9.29289 20.7071C9.10536 20.5196 9 20.2652 9 20V18H8C7.73478 18 7.48043 17.8946 7.29289 17.7071C7.10536 17.5196 7 17.2652 7 17V10Z'
        fill='white'
      />
      <path
        fill-rule='evenodd'
        clip-rule='evenodd'
        d='M8.776 2.55256C8.89455 2.49315 9.03185 2.48327 9.15768 2.52509C9.28352 2.56691 9.38759 2.65701 9.447 2.77556L10.222 4.32556C10.7899 4.10955 11.3924 3.99907 12 3.99956C12.6075 3.99875 13.2101 4.10889 13.778 4.32456L14.553 2.77556C14.5821 2.71634 14.6226 2.66346 14.6723 2.61998C14.7219 2.5765 14.7797 2.54328 14.8422 2.52223C14.9047 2.50117 14.9708 2.49271 15.0367 2.49732C15.1025 2.50192 15.1667 2.51952 15.2257 2.54908C15.2847 2.57864 15.3373 2.61959 15.3804 2.66956C15.4235 2.71954 15.4563 2.77754 15.4768 2.84024C15.4974 2.90294 15.5053 2.96909 15.5002 3.03488C15.4951 3.10067 15.477 3.16479 15.447 3.22356L14.672 4.77356C15.3853 5.2241 15.9729 5.84794 16.38 6.5869C16.787 7.32587 17.0003 8.15589 17 8.99956H7C7 7.22056 7.929 5.65956 9.328 4.77256L8.553 3.22256C8.49391 3.10411 8.4842 2.96706 8.52601 2.84146C8.56781 2.71586 8.65771 2.61197 8.776 2.55256ZM10 6.49956C10 6.63216 9.94732 6.75934 9.85355 6.85311C9.75979 6.94688 9.63261 6.99956 9.5 6.99956C9.36739 6.99956 9.24021 6.94688 9.14645 6.85311C9.05268 6.75934 9 6.63216 9 6.49956C9 6.36695 9.05268 6.23977 9.14645 6.146C9.24021 6.05223 9.36739 5.99956 9.5 5.99956C9.63261 5.99956 9.75979 6.05223 9.85355 6.146C9.94732 6.23977 10 6.36695 10 6.49956ZM14.5 6.99956C14.6326 6.99956 14.7598 6.94688 14.8536 6.85311C14.9473 6.75934 15 6.63216 15 6.49956C15 6.36695 14.9473 6.23977 14.8536 6.146C14.7598 6.05223 14.6326 5.99956 14.5 5.99956C14.3674 5.99956 14.2402 6.05223 14.1464 6.146C14.0527 6.23977 14 6.36695 14 6.49956C14 6.63216 14.0527 6.75934 14.1464 6.85311C14.2402 6.94688 14.3674 6.99956 14.5 6.99956Z'
        fill='white'
      />
    </svg>
  ),
  allEvent: (props: IconProps) => (
    <svg
      width='25'
      height='24'
      viewBox='0 0 25 24'
      fill='none'
      {...props}
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M6.116 20.9995C5.65533 20.9995 5.271 20.8455 4.963 20.5375C4.655 20.2295 4.50067 19.8452 4.5 19.3845V6.61453C4.5 6.15453 4.65433 5.77053 4.963 5.46253C5.27167 5.15453 5.656 5.0002 6.116 4.99953H7.885V2.76953H8.962V4.99953H16.116V2.76953H17.116V4.99953H18.885C19.345 4.99953 19.7293 5.15386 20.038 5.46253C20.3467 5.7712 20.5007 6.15553 20.5 6.61553V19.3845C20.5 19.8445 20.346 20.2289 20.038 20.5375C19.73 20.8462 19.3453 21.0002 18.884 20.9995H6.116ZM6.116 19.9995H18.885C19.0383 19.9995 19.1793 19.9355 19.308 19.8075C19.4367 19.6795 19.5007 19.5382 19.5 19.3835V10.6155H5.5V19.3845C5.5 19.5379 5.564 19.6789 5.692 19.8075C5.82 19.9362 5.961 20.0002 6.115 19.9995M5.5 9.61453H19.5V6.61453C19.5 6.4612 19.436 6.3202 19.308 6.19153C19.18 6.06286 19.0387 5.99886 18.884 5.99953H6.116C5.962 5.99953 5.82067 6.06353 5.692 6.19153C5.56333 6.31953 5.49933 6.46086 5.5 6.61553V9.61453ZM8 13.4995V12.4995H17V13.4995H8ZM8 17.4995V16.4995H14V17.4995H8Z'
        fill={props.color}
      />
    </svg>
  ),
  artsEvent: (props: IconProps) => (
    <svg
      width='25'
      height='24'
      viewBox='0 0 25 24'
      fill='none'
      {...props}
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M3.192 21.5002C3.59867 20.2669 3.86833 19.0152 4.001 17.7452C4.13367 16.4752 4.21667 15.1949 4.25 13.9042C3.6 13.7055 3.06267 13.3112 2.638 12.7212C2.21267 12.1319 2 11.3915 2 10.5002V9.34621C3.84 8.73821 5.714 7.81654 7.622 6.58121C9.53 5.34588 11.156 4.02421 12.5 2.61621C13.844 4.02354 15.47 5.34521 17.378 6.58121C19.286 7.81721 21.16 8.73888 23 9.34621V10.5002C23 11.3915 22.7873 12.1319 22.362 12.7212C21.9373 13.3112 21.4 13.7055 20.75 13.9042C20.7833 15.1949 20.8663 16.4752 20.999 17.7452C21.1317 19.0152 21.4013 20.2669 21.808 21.5002H3.192ZM4.246 9.50021H20.754C19.1973 8.81821 17.7283 8.00654 16.347 7.06521C14.9657 6.12388 13.6833 5.10488 12.5 4.00821C11.3167 5.10421 10.0343 6.12321 8.653 7.06521C7.27167 8.00654 5.80267 8.81821 4.246 9.50021ZM15 13.0002C15.6093 13.0002 16.095 12.7485 16.457 12.2452C16.819 11.7419 17 11.1602 17 10.5002H13C13 11.1602 13.181 11.7419 13.543 12.2452C13.905 12.7485 14.3907 13.0002 15 13.0002ZM10 13.0002C10.6093 13.0002 11.095 12.7485 11.457 12.2452C11.819 11.7419 12 11.1602 12 10.5002H8C8 11.1602 8.181 11.7419 8.543 12.2452C8.905 12.7485 9.39067 13.0002 10 13.0002ZM5 13.0002C5.60933 13.0002 6.095 12.7485 6.457 12.2452C6.819 11.7419 7 11.1602 7 10.5002H3C3 11.1602 3.181 11.7419 3.543 12.2452C3.905 12.7485 4.39067 13.0002 5 13.0002ZM4.496 20.5002H8.267C8.443 19.3722 8.576 18.2489 8.666 17.1302C8.75533 16.0129 8.81867 14.8835 8.856 13.7422C8.59067 13.6202 8.34133 13.4559 8.108 13.2492C7.87467 13.0425 7.672 12.7762 7.5 12.4502C7.26267 12.8929 6.95033 13.2479 6.563 13.5152C6.17567 13.7825 5.738 13.9385 5.25 13.9832C5.21667 15.0865 5.152 16.1809 5.056 17.2662C4.96 18.3515 4.77333 19.4295 4.496 20.5002ZM9.273 20.5002H15.727C15.555 19.4169 15.425 18.3369 15.337 17.2602C15.2497 16.1829 15.1873 15.0942 15.15 13.9942C14.5887 14.0249 14.068 13.8949 13.588 13.6042C13.108 13.3142 12.7453 12.9059 12.5 12.3792C12.2553 12.9059 11.891 13.3145 11.407 13.6052C10.923 13.8959 10.404 14.0259 9.85 13.9952C9.81267 15.0952 9.75033 16.1835 9.663 17.2602C9.575 18.3369 9.445 19.4169 9.273 20.5002ZM16.733 20.5002H20.503C20.227 19.4295 20.0407 18.3515 19.944 17.2662C19.848 16.1815 19.7833 15.0872 19.75 13.9832C19.2633 13.9379 18.8183 13.7815 18.415 13.5142C18.013 13.2475 17.708 12.8802 17.5 12.4122C17.3533 12.7502 17.1573 13.0259 16.912 13.2392C16.6653 13.4525 16.4093 13.6202 16.144 13.7422C16.1813 14.8835 16.2457 16.0132 16.337 17.1312C16.4283 18.2492 16.5603 19.3722 16.733 20.5002ZM20 13.0002C20.6093 13.0002 21.095 12.7485 21.457 12.2452C21.819 11.7419 22 11.1602 22 10.5002H18C18 11.1602 18.181 11.7419 18.543 12.2452C18.905 12.7485 19.3907 13.0002 20 13.0002Z'
        fill={props.color}
      />
    </svg>
  ),
  sportsEvent: (props: IconProps) => (
    <svg
      width='25'
      height='24'
      viewBox='0 0 25 24'
      fill='none'
      {...props}
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M12.504 15C10.8333 15 9.415 14.4183 8.249 13.255C7.083 12.0917 6.5 10.6747 6.5 9.004C6.5 7.33333 7.08167 5.915 8.245 4.749C9.40833 3.583 10.8253 3 12.496 3C14.1667 3 15.585 3.58167 16.751 4.745C17.917 5.90833 18.5 7.32533 18.5 8.996C18.5 10.6667 17.9183 12.085 16.755 13.251C15.5917 14.417 14.1747 15 12.504 15ZM12.5 14C13.8833 14 15.0627 13.5123 16.038 12.537C17.0133 11.5617 17.5007 10.3827 17.5 9C17.4993 7.61733 17.0117 6.43833 16.037 5.463C15.0623 4.48767 13.8833 4 12.5 4C11.1167 4 9.93767 4.48767 8.963 5.463C7.98833 6.43833 7.50067 7.61733 7.5 9C7.49933 10.3827 7.987 11.562 8.963 12.538C9.939 13.514 11.118 14.0013 12.5 14ZM10.5 9C10.7833 9 11.021 8.904 11.213 8.712C11.405 8.52 11.5007 8.28267 11.5 8C11.4993 7.71733 11.4033 7.48 11.212 7.288C11.0207 7.096 10.7833 7 10.5 7C10.2167 7 9.97933 7.096 9.788 7.288C9.59667 7.48 9.50067 7.71733 9.5 8C9.49933 8.28267 9.59533 8.52033 9.788 8.713C9.98067 8.90567 10.218 9.00133 10.5 9ZM14.5 9C14.7833 9 15.021 8.904 15.213 8.712C15.405 8.52 15.5007 8.28267 15.5 8C15.4993 7.71733 15.4033 7.48 15.212 7.288C15.0207 7.096 14.7833 7 14.5 7C14.2167 7 13.9793 7.096 13.788 7.288C13.5967 7.48 13.5007 7.71733 13.5 8C13.4993 8.28267 13.5953 8.52033 13.788 8.713C13.9807 8.90567 14.218 9.00133 14.5 9ZM12.5 7C12.7833 7 13.021 6.904 13.213 6.712C13.405 6.52 13.5007 6.28267 13.5 6C13.4993 5.71733 13.4033 5.48 13.212 5.288C13.0207 5.096 12.7833 5 12.5 5C12.2167 5 11.9793 5.096 11.788 5.288C11.5967 5.48 11.5007 5.71733 11.5 6C11.4993 6.28267 11.5953 6.52033 11.788 6.713C11.9807 6.90567 12.218 7.00133 12.5 7ZM12 20.885V19.885C12 19.181 11.7593 18.5883 11.278 18.107C10.7967 17.6257 10.204 17.385 9.5 17.385H8.27V16.385H16.73V17.385H15.5C14.796 17.385 14.2033 17.6257 13.722 18.107C13.2407 18.5883 13 19.181 13 19.885V20.885H12Z'
        fill={props.color}
      />
    </svg>
  ),
  socialEvent: (props: IconProps) => (
    <svg
      width='25'
      height='24'
      viewBox='0 0 25 24'
      fill='none'
      {...props}
      xmlns='http://www.w3.org/2000/svg'
    >
      <mask
        id='path-1-outside-1_9091_48289'
        maskUnits='userSpaceOnUse'
        x='1.17188'
        y='1.25'
        width='22'
        height='22'
        fill={props.color}
      >
        <rect fill='white' x='1.17188' y='1.25' width='22' height='22' />
        <path d='M8.00014 8.25C8.79579 8.25 9.55885 7.93393 10.1215 7.37132C10.6841 6.80871 11.0001 6.04565 11.0001 5.25C11.0001 4.45435 10.6841 3.69129 10.1215 3.12868C9.55885 2.56607 8.79579 2.25 8.00014 2.25C7.20449 2.25 6.44143 2.56607 5.87882 3.12868C5.31621 3.69129 5.00014 4.45435 5.00014 5.25C5.00014 6.04565 5.31621 6.80871 5.87882 7.37132C6.44143 7.93393 7.20449 8.25 8.00014 8.25ZM8.00014 7.5C7.70467 7.5 7.41209 7.4418 7.1391 7.32873C6.86612 7.21566 6.61808 7.04992 6.40915 6.84099C6.20022 6.63206 6.03449 6.38402 5.92141 6.11104C5.80834 5.83806 5.75014 5.54547 5.75014 5.25C5.75014 4.95453 5.80834 4.66194 5.92141 4.38896C6.03449 4.11598 6.20022 3.86794 6.40915 3.65901C6.61808 3.45008 6.86612 3.28434 7.1391 3.17127C7.41209 3.0582 7.70467 3 8.00014 3C8.59688 3 9.16918 3.23705 9.59113 3.65901C10.0131 4.08097 10.2501 4.65326 10.2501 5.25C10.2501 5.84674 10.0131 6.41903 9.59113 6.84099C9.16918 7.26295 8.59688 7.5 8.00014 7.5ZM17.0001 8.25C17.7958 8.25 18.5589 7.93393 19.1215 7.37132C19.6841 6.80871 20.0001 6.04565 20.0001 5.25C20.0001 4.45435 19.6841 3.69129 19.1215 3.12868C18.5589 2.56607 17.7958 2.25 17.0001 2.25C16.2045 2.25 15.4414 2.56607 14.8788 3.12868C14.3162 3.69129 14.0001 4.45435 14.0001 5.25C14.0001 6.04565 14.3162 6.80871 14.8788 7.37132C15.4414 7.93393 16.2045 8.25 17.0001 8.25ZM17.0001 7.5C16.7047 7.5 16.4121 7.4418 16.1391 7.32873C15.8661 7.21566 15.6181 7.04992 15.4092 6.84099C15.2002 6.63206 15.0345 6.38402 14.9214 6.11104C14.8083 5.83806 14.7501 5.54547 14.7501 5.25C14.7501 4.95453 14.8083 4.66194 14.9214 4.38896C15.0345 4.11598 15.2002 3.86794 15.4092 3.65901C15.6181 3.45008 15.8661 3.28434 16.1391 3.17127C16.4121 3.0582 16.7047 3 17.0001 3C17.5969 3 18.1692 3.23705 18.5911 3.65901C19.0131 4.08097 19.2501 4.65326 19.2501 5.25C19.2501 5.84674 19.0131 6.41903 18.5911 6.84099C18.1692 7.26295 17.5969 7.5 17.0001 7.5ZM15.5001 11.25C15.5001 12.0456 15.1841 12.8087 14.6215 13.3713C14.0589 13.9339 13.2958 14.25 12.5001 14.25C11.7045 14.25 10.9414 13.9339 10.3788 13.3713C9.81621 12.8087 9.50014 12.0456 9.50014 11.25C9.50014 10.4544 9.81621 9.69129 10.3788 9.12868C10.9414 8.56607 11.7045 8.25 12.5001 8.25C13.2958 8.25 14.0589 8.56607 14.6215 9.12868C15.1841 9.69129 15.5001 10.4544 15.5001 11.25ZM14.7501 11.25C14.7501 10.6533 14.5131 10.081 14.0911 9.65901C13.6692 9.23705 13.0969 9 12.5001 9C11.9034 9 11.3311 9.23705 10.9092 9.65901C10.4872 10.081 10.2501 10.6533 10.2501 11.25C10.2501 11.8467 10.4872 12.419 10.9092 12.841C11.3311 13.2629 11.9034 13.5 12.5001 13.5C13.0969 13.5 13.6692 13.2629 14.0911 12.841C14.5131 12.419 14.7501 11.8467 14.7501 11.25ZM6.68389 17.6287C6.65467 17.3936 6.67575 17.1549 6.74576 16.9285C6.81576 16.702 6.93308 16.4931 7.08995 16.3155C7.24681 16.1378 7.43963 15.9955 7.65564 15.8981C7.87165 15.8006 8.10591 15.7501 8.34289 15.75H16.6559C16.8929 15.7501 17.1271 15.8006 17.3431 15.8981C17.5592 15.9955 17.752 16.1378 17.9088 16.3155C18.0657 16.4931 18.183 16.702 18.253 16.9285C18.323 17.1549 18.3441 17.3936 18.3149 17.6287C18.1467 18.9737 17.4931 20.211 16.4769 21.108C15.4607 22.0051 14.1519 22.5001 12.7964 22.5H12.2024C10.847 22.4999 9.5384 22.0048 8.52235 21.1078C7.50631 20.2108 6.8528 18.9736 6.68464 17.6287M8.34289 16.5C7.78864 16.5 7.36039 16.986 7.42864 17.5358C7.57414 18.6993 8.13953 19.7696 9.01858 20.5456C9.89763 21.3216 11.0298 21.7499 12.2024 21.75H12.7964C13.969 21.7499 15.1012 21.3216 15.9802 20.5456C16.8592 19.7696 17.4246 18.6993 17.5701 17.5358C17.5862 17.4061 17.5745 17.2746 17.5359 17.1498C17.4973 17.0251 17.4327 16.9099 17.3462 16.812C17.2598 16.7141 17.1536 16.6357 17.0346 16.5819C16.9156 16.5281 16.7865 16.5002 16.6559 16.5H8.34289ZM19.1616 16.179C19.0448 15.9453 18.8964 15.7288 18.7206 15.5355C19.6042 15.2611 20.39 14.7379 20.984 14.0286C21.5781 13.3192 21.9551 12.4538 22.0701 11.5357C22.0862 11.4061 22.0745 11.2746 22.0359 11.1498C21.9973 11.0251 21.9327 10.9099 21.8462 10.812C21.7598 10.7141 21.6536 10.6357 21.5346 10.5819C21.4156 10.5281 21.2865 10.5002 21.1559 10.5H16.5576C16.5102 10.2438 16.4387 9.99269 16.3439 9.75H21.1566C21.3936 9.7501 21.6279 9.80057 21.8439 9.89806C22.0599 9.99554 22.2527 10.1378 22.4096 10.3155C22.5665 10.4931 22.6838 10.702 22.7538 10.9285C22.8238 11.1549 22.8449 11.3936 22.8156 11.6287C22.6877 12.651 22.2784 13.6176 21.6336 14.421C20.9887 15.2243 20.1335 15.8329 19.1631 16.179M3.84289 9.75H8.65564C8.56164 9.99 8.49064 10.24 8.44264 10.5H3.84289C3.28864 10.5 2.86039 10.986 2.92864 11.5357C3.04372 12.4539 3.4209 13.3194 4.01506 14.0287C4.60922 14.7381 5.39518 15.2612 6.27889 15.5355C6.10283 15.7287 5.95418 15.9453 5.83714 16.179C4.86682 15.8329 4.0116 15.2243 3.36672 14.421C2.72184 13.6176 2.31263 12.651 2.18464 11.6287C2.15542 11.3936 2.17649 11.155 2.24645 10.9286C2.31641 10.7023 2.43367 10.4934 2.59045 10.3157C2.74723 10.1381 2.93996 9.99582 3.15588 9.89829C3.37179 9.80076 3.60597 9.75021 3.84289 9.75Z' />
      </mask>
      <path
        d='M8.00014 8.25C8.79579 8.25 9.55885 7.93393 10.1215 7.37132C10.6841 6.80871 11.0001 6.04565 11.0001 5.25C11.0001 4.45435 10.6841 3.69129 10.1215 3.12868C9.55885 2.56607 8.79579 2.25 8.00014 2.25C7.20449 2.25 6.44143 2.56607 5.87882 3.12868C5.31621 3.69129 5.00014 4.45435 5.00014 5.25C5.00014 6.04565 5.31621 6.80871 5.87882 7.37132C6.44143 7.93393 7.20449 8.25 8.00014 8.25ZM8.00014 7.5C7.70467 7.5 7.41209 7.4418 7.1391 7.32873C6.86612 7.21566 6.61808 7.04992 6.40915 6.84099C6.20022 6.63206 6.03449 6.38402 5.92141 6.11104C5.80834 5.83806 5.75014 5.54547 5.75014 5.25C5.75014 4.95453 5.80834 4.66194 5.92141 4.38896C6.03449 4.11598 6.20022 3.86794 6.40915 3.65901C6.61808 3.45008 6.86612 3.28434 7.1391 3.17127C7.41209 3.0582 7.70467 3 8.00014 3C8.59688 3 9.16918 3.23705 9.59113 3.65901C10.0131 4.08097 10.2501 4.65326 10.2501 5.25C10.2501 5.84674 10.0131 6.41903 9.59113 6.84099C9.16918 7.26295 8.59688 7.5 8.00014 7.5ZM17.0001 8.25C17.7958 8.25 18.5589 7.93393 19.1215 7.37132C19.6841 6.80871 20.0001 6.04565 20.0001 5.25C20.0001 4.45435 19.6841 3.69129 19.1215 3.12868C18.5589 2.56607 17.7958 2.25 17.0001 2.25C16.2045 2.25 15.4414 2.56607 14.8788 3.12868C14.3162 3.69129 14.0001 4.45435 14.0001 5.25C14.0001 6.04565 14.3162 6.80871 14.8788 7.37132C15.4414 7.93393 16.2045 8.25 17.0001 8.25ZM17.0001 7.5C16.7047 7.5 16.4121 7.4418 16.1391 7.32873C15.8661 7.21566 15.6181 7.04992 15.4092 6.84099C15.2002 6.63206 15.0345 6.38402 14.9214 6.11104C14.8083 5.83806 14.7501 5.54547 14.7501 5.25C14.7501 4.95453 14.8083 4.66194 14.9214 4.38896C15.0345 4.11598 15.2002 3.86794 15.4092 3.65901C15.6181 3.45008 15.8661 3.28434 16.1391 3.17127C16.4121 3.0582 16.7047 3 17.0001 3C17.5969 3 18.1692 3.23705 18.5911 3.65901C19.0131 4.08097 19.2501 4.65326 19.2501 5.25C19.2501 5.84674 19.0131 6.41903 18.5911 6.84099C18.1692 7.26295 17.5969 7.5 17.0001 7.5ZM15.5001 11.25C15.5001 12.0456 15.1841 12.8087 14.6215 13.3713C14.0589 13.9339 13.2958 14.25 12.5001 14.25C11.7045 14.25 10.9414 13.9339 10.3788 13.3713C9.81621 12.8087 9.50014 12.0456 9.50014 11.25C9.50014 10.4544 9.81621 9.69129 10.3788 9.12868C10.9414 8.56607 11.7045 8.25 12.5001 8.25C13.2958 8.25 14.0589 8.56607 14.6215 9.12868C15.1841 9.69129 15.5001 10.4544 15.5001 11.25ZM14.7501 11.25C14.7501 10.6533 14.5131 10.081 14.0911 9.65901C13.6692 9.23705 13.0969 9 12.5001 9C11.9034 9 11.3311 9.23705 10.9092 9.65901C10.4872 10.081 10.2501 10.6533 10.2501 11.25C10.2501 11.8467 10.4872 12.419 10.9092 12.841C11.3311 13.2629 11.9034 13.5 12.5001 13.5C13.0969 13.5 13.6692 13.2629 14.0911 12.841C14.5131 12.419 14.7501 11.8467 14.7501 11.25ZM6.68389 17.6287C6.65467 17.3936 6.67575 17.1549 6.74576 16.9285C6.81576 16.702 6.93308 16.4931 7.08995 16.3155C7.24681 16.1378 7.43963 15.9955 7.65564 15.8981C7.87165 15.8006 8.10591 15.7501 8.34289 15.75H16.6559C16.8929 15.7501 17.1271 15.8006 17.3431 15.8981C17.5592 15.9955 17.752 16.1378 17.9088 16.3155C18.0657 16.4931 18.183 16.702 18.253 16.9285C18.323 17.1549 18.3441 17.3936 18.3149 17.6287C18.1467 18.9737 17.4931 20.211 16.4769 21.108C15.4607 22.0051 14.1519 22.5001 12.7964 22.5H12.2024C10.847 22.4999 9.5384 22.0048 8.52235 21.1078C7.50631 20.2108 6.8528 18.9736 6.68464 17.6287M8.34289 16.5C7.78864 16.5 7.36039 16.986 7.42864 17.5358C7.57414 18.6993 8.13953 19.7696 9.01858 20.5456C9.89763 21.3216 11.0298 21.7499 12.2024 21.75H12.7964C13.969 21.7499 15.1012 21.3216 15.9802 20.5456C16.8592 19.7696 17.4246 18.6993 17.5701 17.5358C17.5862 17.4061 17.5745 17.2746 17.5359 17.1498C17.4973 17.0251 17.4327 16.9099 17.3462 16.812C17.2598 16.7141 17.1536 16.6357 17.0346 16.5819C16.9156 16.5281 16.7865 16.5002 16.6559 16.5H8.34289ZM19.1616 16.179C19.0448 15.9453 18.8964 15.7288 18.7206 15.5355C19.6042 15.2611 20.39 14.7379 20.984 14.0286C21.5781 13.3192 21.9551 12.4538 22.0701 11.5357C22.0862 11.4061 22.0745 11.2746 22.0359 11.1498C21.9973 11.0251 21.9327 10.9099 21.8462 10.812C21.7598 10.7141 21.6536 10.6357 21.5346 10.5819C21.4156 10.5281 21.2865 10.5002 21.1559 10.5H16.5576C16.5102 10.2438 16.4387 9.99269 16.3439 9.75H21.1566C21.3936 9.7501 21.6279 9.80057 21.8439 9.89806C22.0599 9.99554 22.2527 10.1378 22.4096 10.3155C22.5665 10.4931 22.6838 10.702 22.7538 10.9285C22.8238 11.1549 22.8449 11.3936 22.8156 11.6287C22.6877 12.651 22.2784 13.6176 21.6336 14.421C20.9887 15.2243 20.1335 15.8329 19.1631 16.179M3.84289 9.75H8.65564C8.56164 9.99 8.49064 10.24 8.44264 10.5H3.84289C3.28864 10.5 2.86039 10.986 2.92864 11.5357C3.04372 12.4539 3.4209 13.3194 4.01506 14.0287C4.60922 14.7381 5.39518 15.2612 6.27889 15.5355C6.10283 15.7287 5.95418 15.9453 5.83714 16.179C4.86682 15.8329 4.0116 15.2243 3.36672 14.421C2.72184 13.6176 2.31263 12.651 2.18464 11.6287C2.15542 11.3936 2.17649 11.155 2.24645 10.9286C2.31641 10.7023 2.43367 10.4934 2.59045 10.3157C2.74723 10.1381 2.93996 9.99582 3.15588 9.89829C3.37179 9.80076 3.60597 9.75021 3.84289 9.75Z'
        fill={props.color}
      />
      <path
        d='M8.00014 8.25V8.45V8.25ZM11.0001 5.25H11.2001H11.0001ZM8.00014 2.25V2.05V2.25ZM5.00014 5.25H4.80014H5.00014ZM8.00014 7.5V7.7V7.5ZM5.75014 5.25H5.55014H5.75014ZM8.00014 3V2.8V3ZM17.0001 8.25V8.45V8.25ZM17.0001 2.25V2.05V2.25ZM17.0001 7.5V7.7V7.5ZM17.0001 3V2.8V3ZM15.5001 11.25H15.3001H15.5001ZM9.50014 11.25H9.30014H9.50014ZM12.5001 8.25V8.05V8.25ZM14.7501 11.25H14.9501H14.7501ZM12.5001 13.5V13.7V13.5ZM8.34289 15.75V15.55H8.3428L8.34289 15.75ZM16.6559 15.75L16.656 15.55H16.6559V15.75ZM18.3149 17.6287L18.5133 17.6536L18.5134 17.6534L18.3149 17.6287ZM12.7964 22.5L12.7964 22.3H12.7964V22.5ZM12.2024 22.5L12.2024 22.7H12.2024V22.5ZM7.42864 17.5358L7.23017 17.5604L7.23019 17.5606L7.42864 17.5358ZM12.2024 21.75L12.2024 21.95H12.2024V21.75ZM12.7964 21.75V21.95H12.7964L12.7964 21.75ZM17.5701 17.5358L17.7686 17.5606L17.7686 17.5603L17.5701 17.5358ZM16.6559 16.5L16.6562 16.3H16.6559V16.5ZM18.7206 15.5355L18.6613 15.3445L18.3613 15.4377L18.5727 15.6701L18.7206 15.5355ZM22.0701 11.5357L22.2686 11.5606L22.2686 11.5603L22.0701 11.5357ZM21.1559 10.5L21.1562 10.3H21.1559V10.5ZM16.5576 10.5L16.361 10.5364L16.3913 10.7H16.5576V10.5ZM16.3439 9.75V9.55H16.0511L16.1576 9.82275L16.3439 9.75ZM21.1566 9.75L21.1567 9.55H21.1566V9.75ZM22.8156 11.6287L23.0141 11.6536L23.0141 11.6534L22.8156 11.6287ZM3.84289 9.75V9.55L3.84271 9.55L3.84289 9.75ZM8.65564 9.75L8.84187 9.82294L8.94877 9.55H8.65564V9.75ZM8.44264 10.5V10.7H8.6091L8.63932 10.5363L8.44264 10.5ZM2.92864 11.5357L2.73016 11.5604L2.73019 11.5606L2.92864 11.5357ZM6.27889 15.5355L6.42672 15.6702L6.63856 15.4377L6.33818 15.3445L6.27889 15.5355ZM5.83714 16.179L5.76996 16.3674L5.93671 16.4268L6.01597 16.2685L5.83714 16.179ZM2.18464 11.6287L1.98617 11.6534L1.98619 11.6536L2.18464 11.6287ZM8.00014 8.45C8.84883 8.45 9.66277 8.11286 10.2629 7.51274L9.98004 7.2299C9.45494 7.755 8.74275 8.05 8.00014 8.05V8.45ZM10.2629 7.51274C10.863 6.91263 11.2001 6.09869 11.2001 5.25H10.8001C10.8001 5.99261 10.5051 6.7048 9.98004 7.2299L10.2629 7.51274ZM11.2001 5.25C11.2001 4.40131 10.863 3.58737 10.2629 2.98726L9.98004 3.2701C10.5051 3.7952 10.8001 4.50739 10.8001 5.25H11.2001ZM10.2629 2.98726C9.66277 2.38714 8.84883 2.05 8.00014 2.05V2.45C8.74275 2.45 9.45494 2.745 9.98004 3.2701L10.2629 2.98726ZM8.00014 2.05C7.15145 2.05 6.33752 2.38714 5.7374 2.98726L6.02024 3.2701C6.54534 2.745 7.25753 2.45 8.00014 2.45V2.05ZM5.7374 2.98726C5.13728 3.58737 4.80014 4.40131 4.80014 5.25H5.20014C5.20014 4.50739 5.49514 3.7952 6.02024 3.2701L5.7374 2.98726ZM4.80014 5.25C4.80014 6.09869 5.13728 6.91263 5.7374 7.51274L6.02024 7.2299C5.49514 6.7048 5.20014 5.99261 5.20014 5.25H4.80014ZM5.7374 7.51274C6.33752 8.11286 7.15145 8.45 8.00014 8.45V8.05C7.25753 8.05 6.54534 7.755 6.02024 7.2299L5.7374 7.51274ZM8.00014 7.3C7.73093 7.3 7.46436 7.24698 7.21564 7.14395L7.06257 7.5135C7.35981 7.63663 7.6784 7.7 8.00014 7.7V7.3ZM7.21564 7.14395C6.96692 7.04093 6.74093 6.88993 6.55057 6.69957L6.26773 6.98241C6.49523 7.20992 6.76532 7.39038 7.06257 7.5135L7.21564 7.14395ZM6.55057 6.69957C6.36021 6.50921 6.20921 6.28322 6.10619 6.0345L5.73664 6.18757C5.85976 6.48482 6.04023 6.75491 6.26773 6.98241L6.55057 6.69957ZM6.10619 6.0345C6.00317 5.78578 5.95014 5.51921 5.95014 5.25H5.55014C5.55014 5.57174 5.61351 5.89033 5.73664 6.18757L6.10619 6.0345ZM5.95014 5.25C5.95014 4.98079 6.00317 4.71422 6.10619 4.4655L5.73664 4.31243C5.61351 4.60967 5.55014 4.92826 5.55014 5.25H5.95014ZM6.10619 4.4655C6.20921 4.21678 6.36021 3.99079 6.55057 3.80043L6.26773 3.51759C6.04023 3.74509 5.85976 4.01518 5.73664 4.31243L6.10619 4.4655ZM6.55057 3.80043C6.74093 3.61007 6.96692 3.45907 7.21564 3.35605L7.06257 2.9865C6.76532 3.10962 6.49523 3.29008 6.26773 3.51759L6.55057 3.80043ZM7.21564 3.35605C7.46436 3.25302 7.73093 3.2 8.00014 3.2V2.8C7.6784 2.8 7.35981 2.86337 7.06257 2.9865L7.21564 3.35605ZM8.00014 3.2C8.54384 3.2 9.06526 3.41598 9.44971 3.80043L9.73255 3.51759C9.27309 3.05812 8.64992 2.8 8.00014 2.8V3.2ZM9.44971 3.80043C9.83416 4.18488 10.0501 4.70631 10.0501 5.25H10.4501C10.4501 4.60022 10.192 3.97705 9.73255 3.51759L9.44971 3.80043ZM10.0501 5.25C10.0501 5.79369 9.83416 6.31512 9.44971 6.69957L9.73255 6.98241C10.192 6.52295 10.4501 5.89978 10.4501 5.25H10.0501ZM9.44971 6.69957C9.06526 7.08402 8.54384 7.3 8.00014 7.3V7.7C8.64992 7.7 9.27309 7.44188 9.73255 6.98241L9.44971 6.69957ZM17.0001 8.45C17.8488 8.45 18.6628 8.11286 19.2629 7.51274L18.98 7.2299C18.4549 7.755 17.7427 8.05 17.0001 8.05V8.45ZM19.2629 7.51274C19.863 6.91263 20.2001 6.09869 20.2001 5.25H19.8001C19.8001 5.99261 19.5051 6.7048 18.98 7.2299L19.2629 7.51274ZM20.2001 5.25C20.2001 4.40131 19.863 3.58737 19.2629 2.98726L18.98 3.2701C19.5051 3.7952 19.8001 4.50739 19.8001 5.25H20.2001ZM19.2629 2.98726C18.6628 2.38714 17.8488 2.05 17.0001 2.05V2.45C17.7427 2.45 18.4549 2.745 18.98 3.2701L19.2629 2.98726ZM17.0001 2.05C16.1514 2.05 15.3375 2.38714 14.7374 2.98726L15.0202 3.2701C15.5453 2.745 16.2575 2.45 17.0001 2.45V2.05ZM14.7374 2.98726C14.1373 3.58737 13.8001 4.40131 13.8001 5.25H14.2001C14.2001 4.50739 14.4951 3.7952 15.0202 3.2701L14.7374 2.98726ZM13.8001 5.25C13.8001 6.09869 14.1373 6.91263 14.7374 7.51274L15.0202 7.2299C14.4951 6.7048 14.2001 5.99261 14.2001 5.25H13.8001ZM14.7374 7.51274C15.3375 8.11286 16.1514 8.45 17.0001 8.45V8.05C16.2575 8.05 15.5453 7.755 15.0202 7.2299L14.7374 7.51274ZM17.0001 7.3C16.7309 7.3 16.4644 7.24698 16.2156 7.14395L16.0626 7.5135C16.3598 7.63663 16.6784 7.7 17.0001 7.7V7.3ZM16.2156 7.14395C15.9669 7.04093 15.7409 6.88993 15.5506 6.69957L15.2677 6.98241C15.4952 7.20992 15.7653 7.39038 16.0626 7.5135L16.2156 7.14395ZM15.5506 6.69957C15.3602 6.50921 15.2092 6.28322 15.1062 6.0345L14.7366 6.18757C14.8598 6.48482 15.0402 6.75491 15.2677 6.98241L15.5506 6.69957ZM15.1062 6.0345C15.0032 5.78578 14.9501 5.51921 14.9501 5.25H14.5501C14.5501 5.57174 14.6135 5.89033 14.7366 6.18757L15.1062 6.0345ZM14.9501 5.25C14.9501 4.98079 15.0032 4.71422 15.1062 4.4655L14.7366 4.31243C14.6135 4.60967 14.5501 4.92826 14.5501 5.25H14.9501ZM15.1062 4.4655C15.2092 4.21678 15.3602 3.99079 15.5506 3.80043L15.2677 3.51759C15.0402 3.74509 14.8598 4.01518 14.7366 4.31243L15.1062 4.4655ZM15.5506 3.80043C15.7409 3.61007 15.9669 3.45907 16.2156 3.35605L16.0626 2.9865C15.7653 3.10962 15.4952 3.29008 15.2677 3.51759L15.5506 3.80043ZM16.2156 3.35605C16.4644 3.25302 16.7309 3.2 17.0001 3.2V2.8C16.6784 2.8 16.3598 2.86337 16.0626 2.9865L16.2156 3.35605ZM17.0001 3.2C17.5438 3.2 18.0653 3.41598 18.4497 3.80043L18.7326 3.51759C18.2731 3.05812 17.6499 2.8 17.0001 2.8V3.2ZM18.4497 3.80043C18.8342 4.18488 19.0501 4.70631 19.0501 5.25H19.4501C19.4501 4.60022 19.192 3.97705 18.7326 3.51759L18.4497 3.80043ZM19.0501 5.25C19.0501 5.79369 18.8342 6.31512 18.4497 6.69957L18.7326 6.98241C19.192 6.52295 19.4501 5.89978 19.4501 5.25H19.0501ZM18.4497 6.69957C18.0653 7.08402 17.5438 7.3 17.0001 7.3V7.7C17.6499 7.7 18.2731 7.44188 18.7326 6.98241L18.4497 6.69957ZM15.3001 11.25C15.3001 11.9926 15.0051 12.7048 14.48 13.2299L14.7629 13.5127C15.363 12.9126 15.7001 12.0987 15.7001 11.25H15.3001ZM14.48 13.2299C13.9549 13.755 13.2427 14.05 12.5001 14.05V14.45C13.3488 14.45 14.1628 14.1129 14.7629 13.5127L14.48 13.2299ZM12.5001 14.05C11.7575 14.05 11.0453 13.755 10.5202 13.2299L10.2374 13.5127C10.8375 14.1129 11.6514 14.45 12.5001 14.45V14.05ZM10.5202 13.2299C9.99514 12.7048 9.70014 11.9926 9.70014 11.25H9.30014C9.30014 12.0987 9.63728 12.9126 10.2374 13.5127L10.5202 13.2299ZM9.70014 11.25C9.70014 10.5074 9.99514 9.7952 10.5202 9.2701L10.2374 8.98726C9.63728 9.58737 9.30014 10.4013 9.30014 11.25H9.70014ZM10.5202 9.2701C11.0453 8.745 11.7575 8.45 12.5001 8.45V8.05C11.6514 8.05 10.8375 8.38714 10.2374 8.98726L10.5202 9.2701ZM12.5001 8.45C13.2427 8.45 13.9549 8.745 14.48 9.2701L14.7629 8.98726C14.1628 8.38714 13.3488 8.05 12.5001 8.05V8.45ZM14.48 9.2701C15.0051 9.7952 15.3001 10.5074 15.3001 11.25H15.7001C15.7001 10.4013 15.363 9.58737 14.7629 8.98726L14.48 9.2701ZM14.9501 11.25C14.9501 10.6002 14.692 9.97705 14.2326 9.51759L13.9497 9.80043C14.3342 10.1849 14.5501 10.7063 14.5501 11.25H14.9501ZM14.2326 9.51759C13.7731 9.05812 13.1499 8.8 12.5001 8.8V9.2C13.0438 9.2 13.5653 9.41598 13.9497 9.80043L14.2326 9.51759ZM12.5001 8.8C11.8504 8.8 11.2272 9.05812 10.7677 9.51759L11.0506 9.80043C11.435 9.41598 11.9564 9.2 12.5001 9.2V8.8ZM10.7677 9.51759C10.3083 9.97705 10.0501 10.6002 10.0501 11.25H10.4501C10.4501 10.7063 10.6661 10.1849 11.0506 9.80043L10.7677 9.51759ZM10.0501 11.25C10.0501 11.8998 10.3083 12.5229 10.7677 12.9824L11.0506 12.6996C10.6661 12.3151 10.4501 11.7937 10.4501 11.25H10.0501ZM10.7677 12.9824C11.2272 13.4419 11.8504 13.7 12.5001 13.7V13.3C11.9564 13.3 11.435 13.084 11.0506 12.6996L10.7677 12.9824ZM12.5001 13.7C13.1499 13.7 13.7731 13.4419 14.2326 12.9824L13.9497 12.6996C13.5653 13.084 13.0438 13.3 12.5001 13.3V13.7ZM14.2326 12.9824C14.692 12.5229 14.9501 11.8998 14.9501 11.25H14.5501C14.5501 11.7937 14.3342 12.3151 13.9497 12.6996L14.2326 12.9824ZM6.88236 17.6041C6.85663 17.397 6.8752 17.1869 6.93683 16.9875L6.55468 16.8694C6.47631 17.1229 6.4527 17.3901 6.48542 17.6534L6.88236 17.6041ZM6.93683 16.9875C6.99847 16.7882 7.10176 16.6042 7.23986 16.4478L6.94003 16.1831C6.76441 16.382 6.63306 16.6159 6.55468 16.8694L6.93683 16.9875ZM7.23986 16.4478C7.37797 16.2914 7.54773 16.1662 7.73791 16.0804L7.57337 15.7158C7.33153 15.8249 7.11565 15.9842 6.94003 16.1831L7.23986 16.4478ZM7.73791 16.0804C7.92809 15.9945 8.13433 15.9501 8.34298 15.95L8.3428 15.55C8.07748 15.5501 7.81521 15.6066 7.57337 15.7158L7.73791 16.0804ZM8.34289 15.95H16.6559V15.55H8.34289V15.95ZM16.6558 15.95C16.8645 15.9501 17.0707 15.9945 17.2609 16.0804L17.4254 15.7158C17.1836 15.6066 16.9213 15.5501 16.656 15.55L16.6558 15.95ZM17.2609 16.0804C17.4511 16.1662 17.6208 16.2914 17.7589 16.4478L18.0588 16.1831C17.8831 15.9842 17.6673 15.8249 17.4254 15.7158L17.2609 16.0804ZM17.7589 16.4478C17.897 16.6042 18.0003 16.7882 18.0619 16.9875L18.4441 16.8694C18.3657 16.6159 18.2344 16.382 18.0588 16.1831L17.7589 16.4478ZM18.0619 16.9875C18.1236 17.1869 18.1421 17.397 18.1164 17.6041L18.5134 17.6534C18.5461 17.3901 18.5225 17.1229 18.4441 16.8694L18.0619 16.9875ZM18.1164 17.6039C17.9543 18.9006 17.3242 20.0933 16.3445 20.9581L16.6093 21.258C17.662 20.3287 18.3391 19.0469 18.5133 17.6536L18.1164 17.6039ZM16.3445 20.9581C15.3649 21.8229 14.1031 22.3001 12.7964 22.3L12.7964 22.7C14.2006 22.7001 15.5565 22.1873 16.6093 21.258L16.3445 20.9581ZM12.7964 22.3H12.2024V22.7H12.7964V22.3ZM12.2024 22.3C10.8958 22.2999 9.63422 21.8226 8.65472 20.9579L8.38999 21.2577C9.44258 22.187 10.7983 22.6999 12.2024 22.7L12.2024 22.3ZM8.65472 20.9579C7.67521 20.0931 7.04521 18.9004 6.8831 17.6039L6.48619 17.6536C6.6604 19.0468 7.3374 20.3285 8.38999 21.2577L8.65472 20.9579ZM8.34289 16.3C7.66811 16.3 7.14714 16.8916 7.23017 17.5604L7.62712 17.5111C7.57364 17.0804 7.90917 16.7 8.34289 16.7V16.3ZM7.23019 17.5606C7.38173 18.7724 7.97063 19.8873 8.88622 20.6955L9.15094 20.3957C8.30844 19.6519 7.76655 18.6261 7.6271 17.5109L7.23019 17.5606ZM8.88622 20.6955C9.80181 21.5038 10.9811 21.9499 12.2024 21.95L12.2024 21.55C11.0786 21.5499 9.99345 21.1394 9.15094 20.3957L8.88622 20.6955ZM12.2024 21.95H12.7964V21.55H12.2024V21.95ZM12.7964 21.95C14.0177 21.9499 15.197 21.5038 16.1126 20.6955L15.8478 20.3957C15.0053 21.1394 13.9202 21.5499 12.7964 21.55L12.7964 21.95ZM16.1126 20.6955C17.0282 19.8873 17.6171 18.7724 17.7686 17.5606L17.3717 17.5109C17.2322 18.6261 16.6903 19.6519 15.8478 20.3957L16.1126 20.6955ZM17.7686 17.5603C17.7881 17.4026 17.7739 17.2425 17.727 17.0907L17.3448 17.209C17.3751 17.3067 17.3842 17.4097 17.3717 17.5112L17.7686 17.5603ZM17.727 17.0907C17.68 16.9389 17.6013 16.7988 17.4962 16.6797L17.1963 16.9444C17.264 17.0211 17.3146 17.1112 17.3448 17.209L17.727 17.0907ZM17.4962 16.6797C17.391 16.5605 17.2617 16.4651 17.1169 16.3996L16.9522 16.7641C17.0454 16.8063 17.1286 16.8677 17.1963 16.9444L17.4962 16.6797ZM17.1169 16.3996C16.9721 16.3342 16.8151 16.3002 16.6562 16.3L16.6556 16.7C16.7579 16.7001 16.859 16.722 16.9522 16.7641L17.1169 16.3996ZM16.6559 16.3H8.34289V16.7H16.6559V16.3ZM19.3405 16.0896C19.2155 15.8395 19.0567 15.6078 18.8686 15.4009L18.5727 15.6701C18.7362 15.8498 18.8741 16.0511 18.9827 16.2684L19.3405 16.0896ZM18.78 15.7265C19.7002 15.4407 20.5187 14.8958 21.1374 14.157L20.8307 13.9002C20.2614 14.58 19.5082 15.0815 18.6613 15.3445L18.78 15.7265ZM21.1374 14.157C21.7561 13.4182 22.1488 12.5168 22.2686 11.5606L21.8717 11.5109C21.7615 12.3908 21.4 13.2203 20.8307 13.9002L21.1374 14.157ZM22.2686 11.5603C22.2881 11.4026 22.2739 11.2425 22.227 11.0907L21.8448 11.209C21.8751 11.3067 21.8842 11.4097 21.8717 11.5112L22.2686 11.5603ZM22.227 11.0907C22.18 10.9389 22.1013 10.7988 21.9962 10.6797L21.6963 10.9444C21.764 11.0211 21.8146 11.1112 21.8448 11.209L22.227 11.0907ZM21.9962 10.6797C21.891 10.5605 21.7617 10.4651 21.6169 10.3996L21.4522 10.7641C21.5454 10.8063 21.6286 10.8677 21.6963 10.9444L21.9962 10.6797ZM21.6169 10.3996C21.4721 10.3342 21.3151 10.3002 21.1562 10.3L21.1556 10.7C21.2579 10.7001 21.359 10.722 21.4522 10.7641L21.6169 10.3996ZM21.1559 10.3H16.5576V10.7H21.1559V10.3ZM16.7543 10.4636C16.7046 10.195 16.6296 9.9317 16.5302 9.67725L16.1576 9.82275C16.2478 10.0537 16.3159 10.2926 16.361 10.5364L16.7543 10.4636ZM16.3439 9.95H21.1566V9.55H16.3439V9.95ZM21.1566 9.95C21.3652 9.95009 21.5714 9.99452 21.7616 10.0804L21.9262 9.71576C21.6843 9.60662 21.4221 9.55012 21.1567 9.55L21.1566 9.95ZM21.7616 10.0804C21.9518 10.1662 22.1216 10.2914 22.2597 10.4478L22.5595 10.1831C22.3839 9.98419 22.168 9.8249 21.9262 9.71576L21.7616 10.0804ZM22.2597 10.4478C22.3978 10.6042 22.5011 10.7882 22.5627 10.9875L22.9449 10.8694C22.8665 10.6159 22.7351 10.382 22.5595 10.1831L22.2597 10.4478ZM22.5627 10.9875C22.6243 11.1869 22.6429 11.397 22.6172 11.6041L23.0141 11.6534C23.0468 11.3901 23.0232 11.1229 22.9449 10.8694L22.5627 10.9875ZM22.6172 11.6039C22.4938 12.5894 22.0993 13.5212 21.4776 14.2958L21.7895 14.5461C22.4576 13.7139 22.8815 12.7125 23.0141 11.6536L22.6172 11.6039ZM21.4776 14.2958C20.8559 15.0703 20.0314 15.657 19.096 15.9906L19.2303 16.3674C20.2355 16.0089 21.1215 15.3784 21.7895 14.5461L21.4776 14.2958ZM3.84289 9.95H8.65564V9.55H3.84289V9.95ZM8.46942 9.67706C8.37066 9.92921 8.29622 10.1915 8.24597 10.4637L8.63932 10.5363C8.68507 10.2885 8.75263 10.0508 8.84187 9.82294L8.46942 9.67706ZM8.44264 10.3H3.84289V10.7H8.44264V10.3ZM3.84289 10.3C3.16811 10.3 2.64714 10.8916 2.73016 11.5604L3.12712 11.5111C3.07364 11.0804 3.40917 10.7 3.84289 10.7V10.3ZM2.73019 11.5606C2.85005 12.5169 3.2429 13.4184 3.86174 14.1572L4.16838 13.9003C3.5989 13.2204 3.23738 12.3909 3.12709 11.5109L2.73019 11.5606ZM3.86174 14.1572C4.48057 14.896 5.29918 15.4408 6.2196 15.7265L6.33818 15.3445C5.49117 15.0816 4.73786 14.5802 4.16838 13.9003L3.86174 14.1572ZM6.13106 15.4008C5.94264 15.6076 5.78356 15.8393 5.65831 16.0895L6.01597 16.2685C6.1248 16.0512 6.26302 15.8499 6.42672 15.6702L6.13106 15.4008ZM5.90433 15.9906C4.96887 15.657 4.14439 15.0703 3.52269 14.2958L3.21075 14.5461C3.87881 15.3784 4.76476 16.0089 5.76996 16.3674L5.90433 15.9906ZM3.52269 14.2958C2.90099 13.5212 2.50648 12.5894 2.38309 11.6039L1.98619 11.6536C2.11878 12.7125 2.5427 13.7139 3.21075 14.5461L3.52269 14.2958ZM2.38311 11.6041C2.35739 11.3971 2.37594 11.187 2.43753 10.9877L2.05537 10.8696C1.97705 11.123 1.95346 11.3902 1.98617 11.6534L2.38311 11.6041ZM2.43753 10.9877C2.49913 10.7884 2.60236 10.6045 2.74039 10.4481L2.4405 10.1834C2.26497 10.3823 2.1337 10.6162 2.05537 10.8696L2.43753 10.9877ZM2.74039 10.4481C2.87843 10.2917 3.04811 10.1664 3.23821 10.0806L3.07354 9.71602C2.83181 9.82521 2.61603 9.98452 2.4405 10.1834L2.74039 10.4481ZM3.23821 10.0806C3.42831 9.99469 3.63448 9.95018 3.84307 9.95L3.84271 9.55C3.57746 9.55024 3.31528 9.60682 3.07354 9.71602L3.23821 10.0806Z'
        fill={props.color}
        mask='url(#path-1-outside-1_9091_48289)'
      />
    </svg>
  ),
  businessEvent: (props: IconProps) => (
    <svg
      width='25'
      height='24'
      viewBox='0 0 25 24'
      fill='none'
      {...props}
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M5.116 20C4.65533 20 4.271 19.846 3.963 19.538C3.655 19.23 3.50067 18.8453 3.5 18.384V8.616C3.5 8.15534 3.65433 7.771 3.963 7.463C4.27167 7.155 4.65567 7.00067 5.115 7H9.5V5.615C9.5 5.155 9.65433 4.77067 9.963 4.462C10.2717 4.15334 10.656 3.99934 11.116 4H13.885C14.345 4 14.7293 4.154 15.038 4.462C15.3467 4.77 15.5007 5.15434 15.5 5.615V7H19.885C20.345 7 20.729 7.15434 21.037 7.463C21.345 7.77167 21.4993 8.156 21.5 8.616V18.385C21.5 18.845 21.3457 19.2293 21.037 19.538C20.7283 19.8467 20.3443 20.0007 19.885 20H5.116ZM10.5 7H14.5V5.615C14.5 5.46167 14.436 5.32067 14.308 5.192C14.18 5.06334 14.039 4.99934 13.885 5H11.115C10.9617 5 10.8207 5.064 10.692 5.192C10.5633 5.32 10.4993 5.461 10.5 5.615V7ZM20.5 14.5H14.5V16H10.5V14.5H4.5V18.385C4.5 18.5383 4.564 18.6793 4.692 18.808C4.82 18.9367 4.961 19.0007 5.115 19H19.885C20.0383 19 20.1793 18.936 20.308 18.808C20.4367 18.68 20.5007 18.5387 20.5 18.384V14.5ZM11.5 15H13.5V13H11.5V15ZM4.5 13.5H10.5V12H14.5V13.5H20.5V8.616C20.5 8.462 20.436 8.32067 20.308 8.192C20.18 8.06334 20.039 7.99934 19.885 8H5.115C4.96167 8 4.82067 8.064 4.692 8.192C4.56333 8.32 4.49933 8.46134 4.5 8.616V13.5Z'
        fill={props.color}
      />
    </svg>
  ),
  lifestyleEvent: (props: IconProps) => (
    <svg
      width='25'
      height='24'
      viewBox='0 0 25 24'
      fill='none'
      {...props}
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M7.98914 20.9372C5.91534 19.9199 4.24631 18.2315 3.25306 16.1461C2.25981 14.0607 2.00066 11.7007 2.5177 9.44947C3.03473 7.1982 4.29758 5.18781 6.1012 3.74474C7.90482 2.30166 10.1433 1.51063 12.4531 1.50011C14.763 1.48958 17.0086 2.26016 18.8253 3.68674C20.642 5.11332 21.9231 7.11211 22.4606 9.35857C22.9982 11.605 22.7605 13.9672 21.7863 16.0616C20.8121 18.156 19.1586 19.8596 17.0941 20.8957'
        stroke={props.color}
        strokeWidth='0.7'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M16.4071 19.3027L15.4121 21.4847L17.5351 22.4997'
        stroke={props.color}
        strokeWidth='0.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M13.1558 7.20414C13.7661 7.20414 14.2608 6.70942 14.2608 6.09914C14.2608 5.48887 13.7661 4.99414 13.1558 4.99414C12.5455 4.99414 12.0508 5.48887 12.0508 6.09914C12.0508 6.70942 12.5455 7.20414 13.1558 7.20414Z'
        stroke={props.color}
        strokeWidth='0.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M10.3506 15.1114L8.89111 16.8889C8.19211 17.6979 9.20111 18.4074 9.94111 17.7074L11.3706 15.9064L11.8396 14.1464L12.5941 14.9004L13.0971 17.5039C13.4296 18.2284 14.5111 18.1889 14.4756 17.2569L13.7306 14.4749L12.6621 12.6249L13.2606 10.3924L13.9281 10.5714C13.9188 10.6476 13.9321 10.7249 13.9666 10.7936C14.001 10.8622 14.0549 10.9192 14.1216 10.9574C14.3541 11.0814 16.0256 11.5934 16.0256 11.5934C17.0771 11.9164 17.1956 10.8579 16.3456 10.6114L14.9541 10.2379L14.2651 9.09085L13.1456 8.08785L11.4341 8.04785L9.01511 9.50835L8.11661 11.9094C7.91811 12.5804 8.88011 12.7549 9.14611 12.2334C9.27511 12.0044 9.92511 10.4574 9.92511 10.4574L11.3371 9.75635L10.6466 12.9234L10.3506 15.1114Z'
        stroke={props.color}
        strokeWidth='0.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  ),
  globe: (props: IconProps) => (
    <svg
      width='16'
      height='16'
      viewBox='0 0 16 16'
      fill='none'
      {...props}
      xmlns='http://www.w3.org/2000/svg'
    >
      <g clip-path='url(#clip0_9110_59637)'>
        <path
          d='M7.9987 1.33301C9.66622 3.15858 10.6139 5.5277 10.6654 7.99967C10.6139 10.4717 9.66622 12.8408 7.9987 14.6663M7.9987 1.33301C6.33118 3.15858 5.38353 5.5277 5.33203 7.99967C5.38353 10.4717 6.33118 12.8408 7.9987 14.6663M7.9987 1.33301C4.3168 1.33301 1.33203 4.31778 1.33203 7.99967C1.33203 11.6816 4.3168 14.6663 7.9987 14.6663M7.9987 1.33301C11.6806 1.33301 14.6654 4.31778 14.6654 7.99967C14.6654 11.6816 11.6806 14.6663 7.9987 14.6663M1.66538 5.99967H14.332M1.66536 9.99967H14.332'
          stroke='#71717A'
          stroke-width='1.5'
          stroke-linecap='round'
          stroke-linejoin='round'
        />
      </g>
      <defs>
        <clipPath id='clip0_9110_59637'>
          <rect width='16' height='16' fill='white' />
        </clipPath>
      </defs>
    </svg>
  ),
  stepCompleted: (props: IconProps) => (
    <svg
      width='25'
      height='24'
      viewBox='0 0 25 24'
      fill='none'
      {...props}
      xmlns='http://www.w3.org/2000/svg'
    >
      <rect
        x='1.25'
        y='0.75'
        width='22.5'
        height='22.5'
        rx='11.25'
        stroke='url(#paint0_linear_9350_19942)'
        stroke-width='1.5'
      />
      <path
        fill-rule='evenodd'
        clip-rule='evenodd'
        d='M17.598 7.38967L10.438 14.2997L8.53797 12.2697C8.18797 11.9397 7.63797 11.9197 7.23797 12.1997C6.84797 12.4897 6.73797 12.9997 6.97797 13.4097L9.22797 17.0697C9.44797 17.4097 9.82797 17.6197 10.258 17.6197C10.668 17.6197 11.058 17.4097 11.278 17.0697C11.638 16.5997 18.508 8.40967 18.508 8.40967C19.408 7.48967 18.318 6.67967 17.598 7.37967V7.38967Z'
        fill='url(#paint1_linear_9350_19942)'
      />
      <defs>
        <linearGradient
          id='paint0_linear_9350_19942'
          x1='16.34'
          y1='30.439'
          x2='-14.8205'
          y2='14.351'
          gradientUnits='userSpaceOnUse'
        >
          <stop stop-color='#664FB0' />
          <stop offset='1' stop-color='#A992F5' />
        </linearGradient>
        <linearGradient
          id='paint1_linear_9350_19942'
          x1='14.7694'
          y1='20.4379'
          x2='0.130928'
          y2='11.8066'
          gradientUnits='userSpaceOnUse'
        >
          <stop stop-color='#664FB0' />
          <stop offset='1' stop-color='#A992F5' />
        </linearGradient>
      </defs>
    </svg>
  ),
  stepActive: (props: IconProps) => (
    <svg
      width='25'
      height='24'
      viewBox='0 0 25 24'
      fill='none'
      {...props}
      xmlns='http://www.w3.org/2000/svg'
    >
      <rect
        x='1.25'
        y='0.75'
        width='22.5'
        height='22.5'
        rx='11.25'
        fill='#F2E5FF'
      />
      <rect
        x='1.25'
        y='0.75'
        width='22.5'
        height='22.5'
        rx='11.25'
        stroke='url(#paint0_linear_9110_58177)'
        stroke-width='1.5'
      />
      <circle cx='12.5' cy='12' r='4' fill='url(#paint1_linear_9110_58177)' />
      <defs>
        <linearGradient
          id='paint0_linear_9110_58177'
          x1='16.34'
          y1='30.439'
          x2='-14.8205'
          y2='14.351'
          gradientUnits='userSpaceOnUse'
        >
          <stop stop-color='#664FB0' />
          <stop offset='1' stop-color='#A992F5' />
        </linearGradient>
        <linearGradient
          id='paint1_linear_9110_58177'
          x1='13.78'
          y1='18.1463'
          x2='3.39318'
          y2='12.7837'
          gradientUnits='userSpaceOnUse'
        >
          <stop stop-color='#664FB0' />
          <stop offset='1' stop-color='#A992F5' />
        </linearGradient>
      </defs>
    </svg>
  ),
  stepInActive: (props: IconProps) => (
    <svg
      width='25'
      height='24'
      viewBox='0 0 25 24'
      fill='none'
      {...props}
      xmlns='http://www.w3.org/2000/svg'
    >
      <g clip-path='url(#clip0_9110_57449)'>
        <rect x='0.5' width='24' height='24' rx='12' fill='#F4F4F5' />
        <rect
          x='1.25'
          y='0.75'
          width='22.5'
          height='22.5'
          rx='11.25'
          stroke='#D4D4D8'
          stroke-width='1.5'
        />
        <circle cx='12.5' cy='12' r='4' fill='#D4D4D8' />
      </g>
      <defs>
        <clipPath id='clip0_9110_57449'>
          <rect x='0.5' width='24' height='24' rx='12' fill='white' />
        </clipPath>
      </defs>
    </svg>
  ),
  ticketEvent: (props: IconProps) => (
    <svg
      width='41'
      height='40'
      viewBox='0 0 41 40'
      fill='none'
      {...props}
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M29.3476 11.4062C28.6976 10.756 28.3171 9.88448 28.282 8.96586C28.247 8.04724 28.56 7.14924 29.1585 6.45146C29.208 6.39343 29.2337 6.31883 29.2305 6.24262C29.2274 6.16641 29.1955 6.09422 29.1413 6.04052L25.6937 2.58896C25.6367 2.532 25.5594 2.5 25.4788 2.5C25.3982 2.5 25.321 2.532 25.264 2.58896L19.7687 8.08428C19.5658 8.28694 19.4131 8.53411 19.3226 8.80615C19.2323 9.07879 19.0797 9.32665 18.877 9.53006C18.6743 9.73348 18.4269 9.88685 18.1546 9.97803C17.8824 10.0687 17.635 10.2214 17.4319 10.4241L3.33896 24.514C3.282 24.571 3.25 24.6482 3.25 24.7288C3.25 24.8094 3.282 24.8867 3.33896 24.9437L6.78662 28.3913C6.84032 28.4455 6.9125 28.4774 6.98871 28.4805C7.06492 28.4837 7.13952 28.458 7.19756 28.4085C7.89519 27.8094 8.79338 27.4958 9.71232 27.5307C10.6313 27.5655 11.5031 27.9462 12.1534 28.5964C12.8036 29.2467 13.1843 30.1185 13.2191 31.0375C13.254 31.9564 12.9404 32.8546 12.3413 33.5522C12.2918 33.6103 12.2661 33.6849 12.2693 33.7611C12.2724 33.8373 12.3043 33.9095 12.3585 33.9632L15.8062 37.4108C15.8631 37.4678 15.9404 37.4998 16.021 37.4998C16.1016 37.4998 16.1788 37.4678 16.2358 37.4108L30.3296 23.3179C30.5323 23.1148 30.685 22.8674 30.7757 22.5952C30.8659 22.3226 31.0185 22.0747 31.2212 21.8713C31.424 21.6679 31.6713 21.5145 31.9437 21.4233C32.2157 21.3328 32.4629 21.18 32.6655 20.9772L38.1608 15.4819C38.2178 15.4249 38.2498 15.3477 38.2498 15.2671C38.2498 15.1865 38.2178 15.1092 38.1608 15.0522L34.7132 11.6046C34.6595 11.5504 34.5873 11.5185 34.5111 11.5154C34.4349 11.5122 34.3603 11.5379 34.3022 11.5874C33.6054 12.1869 32.708 12.5013 31.7894 12.4677C30.8708 12.4341 29.9987 12.055 29.3476 11.4062Z'
        stroke='#5A5A5A'
        stroke-width='1.5'
        stroke-miterlimit='10'
      />
      <path
        d='M20.3211 10.9715L19.0312 9.68164M23.7602 14.4105L22.9 13.5512M27.1992 17.8504L26.3398 16.9902M31.0687 21.7191L29.7789 20.4293'
        stroke='#5A5A5A'
        stroke-width='1.5'
        stroke-miterlimit='10'
        stroke-linecap='round'
      />
    </svg>
  ),
  freeEvent: (props: IconProps) => (
    <svg
      width='41'
      height='40'
      viewBox='0 0 41 40'
      fill='none'
      {...props}
      xmlns='http://www.w3.org/2000/svg'
      xlinkHref='http://www.w3.org/1999/xlink'
    >
      <rect x='0.75' width='40' height='40' fill='url(#pattern0_9372_19630)' />
      <defs>
        <pattern
          id='pattern0_9372_19630'
          patternContentUnits='objectBoundingBox'
          width='1'
          height='1'
        >
          <use xlinkHref='#image0_9372_19630' transform='scale(0.00195312)' />
        </pattern>
        <image
          id='image0_9372_19630'
          width='512'
          height='512'
          xlinkHref='data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAgAAAAIAEAQAAAAO4cAyAAAAIGNIUk0AAHomAACAhAAA+gAAAIDoAAB1MAAA6mAAADqYAAAXcJy6UTwAAAACYktHRAAAqo0jMgAAAAlwSFlzAAAAYAAAAGAA8GtCzwAAgABJREFUeNrs3XdU1Ee7B/BntqAiCoi9xYK9YKxRUCzYRVFZYXdFxYItWGPHjsYSYw+KBRV2F1xjQ8VesEUNKvYodlFBaYoiLLtz/zC+1yQW1J3fbHk+59xz7mtw5jsjMM/+ygwBhJBJc6cSSbkaxYrpHxQrBlCiBOnm4ADE0ZGccHAwbHFwIFEODjDfwYFuK1iQbLO1haoFC8JNGxvQ2NvTdLGYDHVw+EeDsXZ20EIq/eef6XTQIjPz/T+iIenpZHJuLqS9eAFVc3Lg5qtXtPvr16T7q1cwIT2d+qSni7zT06lbejrQtDS6Iz0dIClJXP7Zs+w6yclarV7Pe+4QQh9HeAdAyJrJZEWKSHaXLUueli8PbcqVoyvLlSMdypaF38uUofmLFydexYvD+mLFQEXM62dVSSn0f/aMbk9OhhJJSaTh48cw8OFDOPLoERx6+JCWfPAgt/OjR1ptairvqAhZK/P6pYKQ2SHEt13ZsqJuzs5ks7OzYYyzMylVuTIt7OxM+js7Q4WCBXkn5Mo9M5O6375NXiQkwJKEBOqekCAKSEjQt799O3L/o0cAlPKOiJClwgIAISPxXVe6tOhgzZp0Va1aZGjNmnCiVi36Z926ZFShQryzmaWg7GzwvH0bGl+9StXXrhHF1as089q1qjuvX59JDAbe8RAyd1gAIPSF3KlEUrprtWqwtEEDUrtBA/CqWRP869eHsCJFeGezCu6ZmTAnPh7crl6lIdeuQWRcnE3An39uJG/e8I6GkDnBAgChz/BZXrWquE2TJrRMkybkZpMmULBOHQjOl493LvSeoOxs6nzpEmSdPUsSz5yhZf74Q2N/6xbvWAiZMiwAEHpPQICt7csLP/wA9dzcyIYffoA9TZrgJ3sztTUlBYLOnKH3z5wRhcbG5vQ6c0arzcriHQshU4EFALJqfsULFtS3adqUqt3ciNLVFeLd3MAlf37euRADw3NzYWV8PMDBg3ToyZM2bsePbyTp6bxjIcQLFgDIqrhTiaTsd02aGO63bUuy2raFC40bw0qJhHcuxMHw3FyQnzkDIQcOGJYeOPBk39mzx0huLu9YCAkFCwBk8RSKSpVgSceO4NG2LZRt1QocChfmnQmZIK+MDMh/5AiNOnBAciAmJjz57l3ekRBiCQsAZHFkMrFYKq1Xj6o8PYmySxeABg14Z0JmiN65Azt27YJu0dF2dseOha7R6XhHQsiYsABAFsGveMGCBp+OHWFzt240o2NH6OHkxDsTsiAOz59DZEwM7btjR6FXMTGhoa9f846E0LfCAgCZrYAAW9uXfm3akG4yGfzcvTscs7PjnQlZgfg3byD14EF6UqvNHb9jh3ZLRgbvSAh9DSwAkFnpP65Qoayh3buLqvXqReM9PPB9fMRVUHY2tDlwgGZEReV22r5du+WfByohZMqwAEAmTyYTi6WSVq1gX58++Ekfmay/rwxAy02bdLodO7RbcnJ4R0LoU7AAQCZLvtrNDWr26UNCvL0BHB1550Eoz/xTU6GJVktqh4erHpw8yTsOQh+CBQAyKb2rlyqlP9OrF/EYMACq1KnDOw9C3yzo5k1aQ6Mh18PC1DXv3+cdB6F3sABA3AUMkkpf5e/WjZbw94fE9u0hQyzmnQkho7PX68navXsNP4aFFcrcuRNfK0S8YQGAuPFdV7q06JCfH3gMHw4Hy5XjnQchwVRMSoK7GzboVatWRZF793jHQdYJCwAkqOlUJLqlaN0aICAAfuzeHbfhRVYt2mAAz8OHgYaG6nK3btVq9XrekZD1wAIACUIZWLiwIdjfn1wKDISQypV550HI5GgSEiBpyRJx7Q0bwpNfveIdB1k+LAAQUwpFpUoAAQFv/w+f5Efos7wyMqDfxo2SUwsWbKqXmMg7DrJcWAAgJnr3bN7c0HfMGFB27QqeIhHvPBanfE4ObEtJoRtSUsjDlBRYnZICJZ4/B0hLA9ecHDj56hVNfP2alMnOhisvXkCtt5eWaUmDgTz98M51tKS9PXn697/VVbEYahcuTBPz5SNlbG3BtWBBOGljA+DoCElFi8JgJydazsmJ9HNygldFi0ILqZT3lFicoOxsgPBw/fJff40KuX6ddxxkebAAQEZEiDLR05OumTgRbjZtyjuN2YnV6aD248fg+fAhHfzgAex+9AhaPn4MpVJSRJVTUmhiSopY8uyZwSclRbX8xQvecd+nDCxcmKYULQp7ixYlZZycDLednOCJkxMcLV0aOpctS1aXL089y5cnzUqVwmLhCykpJV579kDOL7+oyNGjvOMgy4EFAPpmAYOk0ldNFQo6Z/x4aFKzJu88Ji39xQsalpBAshMSYEVCAm2bkEDK3L4tybl9u6LLkyczicHAOyJLMplYXGBKyZK5NpUrkxBnZ4OfszNZ7OwMfZydaQNnZzKqUCHeGU1bXBxc+eWXxPgtW46R3FzeaZB5wwIAfTWZt42N9P6AAfT3iRPJxPLleecxKcNzc6HKrVvw5vJl8I2Pp/MvXza4Xb6Mr3x9mg+tUEHyuG5dw+o6dcjAunVhTd268LxKFdwb4l9S79+n3//8c6Hn69fjfgLoa2EBgL7Y21f5evaEYT//jE/0AwCkpcGJa9fA7epVGnLtGkTGxRWKO38ej4w1joBBUunLp1WrwtIGDUhQzZpwpFYtKNygATQoVYp3Nt7ovAcPyMQ5cxJV69fjFQH0pbAAQHn2v4V/anAwBFetyjsPF/Z6Pez/6y9ocuIEDT55EkbGxWmir17lHcsa+a4rXZpUbdCAhLi6wgk3NzA0bmy1zxf0uncPNv/8s063bh3uJYDyCgsAlAeEyI936QLFZs8ms1xceKcR1L1Xr+DRxYvgduIEHXryJAk5cUKtTkvjHQv9l8zbzk7Stl49cszVFY64udF7bm7E38GBdy5BLbl+nWbOm1e1YkSEpT9Pgr4dFgDoE94+1W94OWuW9Sz8aWl04qFDEBgbK046eTK7Tnw8fqIyT+5UIimjdHGBoa6ukK9FC+rSpo3VFAQpFy/SoGnTNM2jo3lHQaYLCwD0Qb4PO3QgJWbNIv0aNeKdhSklpbThhQtk0d69tHJMzOOAP/7Ae6mWyZ1KJOW8mzbVT+jYkaR06ACGevVARSz7d6DT2bOwZ9o09e19+3hHQabHsr/50RdTBtasSVf98gvIOnbknYUZ98xMmHH0KLSKjpaM370bd1uzTjLvYsWk0pYtYZ+nJ3To0sWid6ocdPgwaTdqlEp3+TLvKMh0YAGAAABAGVi2rKF2cDD5yc/PEnfuo4vPnwfdnj20f0yM3v7MGbysj94nk4nFNmd++AHyd+xIK3buDE716vHOZHTDc3PhzKpVutPTp2u1qam84yD+sACwcjJvOztpq/HjafuxY8k0W1veeYyq46VLEBIVJWqyeXPE4oQE3nGQ+ZBnVKlCEn18oG+vXlClTh3eeYzKPzUVfKZPT0xZtQpvd1k3LACsmPy4pyd8t2KFRW3ic+baNZqg1RqGRUXh/unIGPyKV6yo9+jVC5b07QujatTgncdo9v71F707ZozGfs8e3lEQH1gAWCH56urVSfNlyyC4bVveWYyi1717oNi5ky7WajWDT5zgHQdZLrlnrVqwUyYjTr6+0KFaNd55jOPgQeI0cqRq+bVrvJMgYWEBYEUUCkdHcnnmTLpq6FBYKZHwzvNNDiUnQ7VNmwzL1OrIehcu8I6DrE/v6vXrG8KUSrDx84NFxYrxzvNNyufkwKlly3TFg4O1Wz58WiSyPFgAWAm5be/epNCiRdCmeHHeWb5atMEAZw4dgtlr1uh0O3Zot+Tk8I6EkMzbxkaS7eVFCg0aBNGtW5v1Q7Rznj6lI0aO1ERv3sw7CmIPCwAL53OycmVR7d9+I0PbteOd5avNefoUpmzcCBAaqlbfucM7DkIfowwsW5amKJV03rBh5vxsDdHu3i26MWxYeOUHD3hnQexgAWChAgZJpZmvxoyB+BkzwCV/ft55vli0wQCehw8DDQ21s9u+HU88Q+ZEJhOLpZJWrQACAuDH7t3N8pZb+osXVDNtWtXCy5fjtsKWCQsAC+TbrlkzUcTatWb5xHLckyfw6+rVhqXr10fuf/iQdxyEvpVvu3LlyL4BA8i9wYNhSsmSvPN8MdcTJ6hk0CDN4Bs3eEdBxoUFgAWRyQoUkEqnTwf7n34yu/PTHW/donNXrrQpvHr1RvLmDe84CBmbzNvGRrLA1xeSxo0jy2vX5p3ni8TqdNDi1191OdOm4bM3lgMLAAuhLO/qSjPXrTO7V5NOnDxJVfPna5rv2gVAKe84CAlBvtrNjcyYMAHWdu5sTucR0MArV2jkwIGRS8+c4Z0FfTuz+cZDH+ZXvGBB/cR582D6sGFm8/RxrE5HdFFRdPmiRepeFy/yjoMQL72r16+vvzJ2LKkgk0ELqZR3njyx1+tpqeXLCz2aMiU09PVr3nHQ18MCwIz5rmvYUDQuIsJcPvXTJS9fkgphYeLLixbh08UI/b/e1UuV0l8fPJgoR4wwm0OJ9v71F3RQKtXquDjeUdDXwQLADE2nItHN0PHjyexZs8ziU8PWlBTosXAhcQoJUS1/8YJ3HIRMlczb3l76ePhwmDl2LIQVKcI7z2eVz8kh6qlTne//8gu+KWB+sAAwM77typUjczdtIr+2bMk7y+fQJS9fklG//abL+fln3F0MobyTedvZSW2GD6dhEycSfwcH3nk+69mRI6R6nz6q5Y8e8Y6C8g4LADMiH92zJ+kUGmrynwzuvXoFFVasAJg/X61OS+MdByFzJZMVKSLZPGIE6Tx6NDgULsw7zyd5ZWQQ5bBhKp1azTsKyhssAMxA/3GFCr1J/OUXgIAA3lk+hc56/ZrUXbuWwty5mtdJSbzzIGQp5KuLFiXHfvqJzgoMNPlju/eGh+taDxum3ZKZyTsK+jQsAEycokjjxiCPiIC0KlV4Z/mo8jk58GDDBkObmTMjBzx+zDsOQpZK5l2smNRm7FiIHznSpHf47HXvHhnRu7fqwcmTvKOgj8MCwETJZGKxxGHKFNJv6lST3UY02mCAM+vXk5CZM/HeH0LC8btdvnxu/PTppG+/fib7+m+sTkf7z5yZe33ePK1Wr+cdB/0XFgAmqI+Xk1PuXI0Ggtu25Z3lo7yOHQMYNQrf40eIH9+L338v6r9kCVRv0YJ3lo+hY44elXTy8QlPTk7mnQX9ExYAJsb34vffi+5t3QqbK1TgneWD7jx6RNVTpmgqhYfjzn0ImQb5cU9PqLt0KRlasSLvLB/k8fAh/OTtrU49e5Z3FPT/sAAwIfI7ffoQ7apVEF+gAO8s/0ZnvX4NlRcutIF583CvfoRMz9uzQEaMoEumTCGjChXinec/grKz6fEff9QMXruWdxT0FhYAJqCjc758jo8XLACvESN4Z/kPJaUQsWWLOPinn3D3PoRMn++60qVFh6ZPh+iBA03y+YC94eG6NoMHa7VZWbyjWDssADhTBpYtS8O0Wuj6ww+8s/wb3XDuHO0yalTk/lOneGdBCH0Z33UNG4rkS5fCwGbNeGf5r7g4COrZU13z/n3eSawZFgAcKVa2agU/REXBomLFeGf5p7Q06j92rKbthg14nx8hc0aI0n3gQMP+BQtMbkfBiklJNNnHR7Pm2DHeUayVeZ0Zb0EUioAAUGk08NjE7tUd2bVLEtK5s8o1NpZ3FITQt7t8//z5Gn9t3Cg+UKIEOLu48M7zP+l2dsTWz69O1ezsy5fxKiMPeAVAYAEBtraZQWFhMLFXL95Z3kcnJiaKnIYNU5XZuZN3FoQQG4r5PXrA+hUroEGpUryz/EOJyEjdo/798bkAYWEBIKA+Xk5Oued37AA3V1feWf5HSSn4RUTo2owapdWmpvKOgxBiS+Ztby/dM2sWiH/80aQeEpx75oy4cdeuuF+AcLAAEEjv0c7Ohpw9e0xqS9+ht2/TMQEBmnOHD/OOghASlny1mxt5tGYN3K5enXeWd2jI3bsQ2amTZvCNG7yzWAMsAATg265ZM5Hzjh2QXrQo7ywAADA8NxdWLlokVc2Yge/0I2S9+tL8+XNg4kQyadIkeGBjwzvPW2lptGD37vhwIHtYADAmt+3dm4xYt85kfrgaX70qyq9URgyJj+cdBSFkGnwvfv+9qLRKBaNq1OCdBQAAgrKziUv//ni0MFumc//HAskzRo4k2k2bTGLxV1IKEBpqd61xY1z8EULvi6x34YK0WP36sH3Zsre/KzgLzpePbo+IkNMZM3hHsWR4BYABdyqRlHm5YgUMGTyYdxYAADiUnAy3BgxQF961i3cUhJBpUwa2a0f3b9hgMm8KXNiwwc4tICB0jU7HO4qlwSsARtZ/XKFCZVKjo01l8ach+/eLHOvVw8UfIZQXquX794vT6tWDIybyO+P7fv1eToqJkXnb2/OOYmnwCoAR+bYrV07UKSYGztaqxTsLuGRlgf+4ceriv/2Gu/lZp+lUJLqltLc3tClQQHQof/63f+ro+Om/lZYGAGBo8+aN6FBWVhVVRsZMYjDwHgvigRDF8cBAGDJ/Pri8+/7h6Nbly5I1HTtuqpeYyDuKpcACwEh8aIUK4hMHD0JI5cq8s0Djq1fpZIVC8/rSJd5RkHEEDJJKX+UvUUIvKlNG1KVkSbqhTBlRaokShqXFi0MhR0eSVqQIBBcpQkOKFCFDHRwg1s4OWkilRuk8VqeDFpmZNCQ9nQxNTYWg1FTqmJoKL9PSRCOTk+nzp0/J9sRE/YKkJLEhMbHgm6QkvFxrOZSBNWvSmyoVONWrxzsLpN6/L6rh4RGxOCGBdxRLgAWAEfgMrVFDNPTAATKvTBmuQZSUQpHFi9P8Jk+OScjO5j0v6Mv08XJy0p+vXt2wrmpV0a8VKtAzFSvSqAoVyMMKFeBc6dKQITaPrbvt9Xpo9PgxLXfvHnl89y7tc/cu0d67Z1h786be/sYN3HDK/PSl+fPrCs6fD9rAQFARvuvGnUePqH/btrhXwLfDAuAbKTbXqweV9+/nfaAPXfLypWh2//6q5Vu28J4T9GkKhaMjUbm4GF64uJDE2rXhSPXqcLV6dZPZJ4K1sc+e0dQbNyD/jRuk26VL9GhcXKEV8fGhoa9f846GPk2Z2LUrPblpE2znfD/+UHIyLG/fXt3r4kXec2LOsAD4Br7rGjYUDd+7F3o4OXENEnTzJp3Qo4cm+upV3nOC/qmPl5OT/nbjxoZLTZqQNfXrw9a6daHId9/xzmVyhufmUtGNG3ApLg7UcXE0X1xc4QoXL2JRYHqUidWq0RZbt0KTmjV55qBh6enwV6dOmrqnT/OeE3OFBcBXUlxr0QJ+io4Gh8KFeeag3XfuzI3q00e7JSOD95ygv++XTm3VCsKaNoUHjRub1NbP5sZer6d9rl8nN8+fp/vPnDEUPnIkKuT6dd6x0Nu3nd7IwsJgSc+eXIPce/WKTOrWTeV56BDvOTFHWAB8Bd+HHTqI1Fu3QnyBAtxC2Ov19LfgYA2ZOROf8ufHd13p0qKDrq5APDzAo2NHOFiuHO9MFq1iUhLciY0FcvAgfXnyJF714okQhWL8eLCfM4fn8yl01uvXNF/PnpHl9u7lPSPmBguALyQ/7ulJhmzezPW1mK0pKVQkl2teHzjAez6sjdy2RAnSrUULIB4eNKRtWzK0YkXemaxa3JMnUP/ECSAHD4pn790bXvnBA96RrI2StmxJS0ZFQZvixbmFKJ+TQ7MVCs3i33/nPR/mBAuAL6Co6+sLqZs2Ge31qq9AF58/byjes2cUuXeP93xYB0J81zVoQOr06EHk3brxvu+JPqPx1avweMcOeLR1q1odF8c7jrXwbVeunKjali2Q0rgxtxD2ej1pNGCAqv/Gjbznw1xgAZBHCqlSCfk3beJ5fjb1Xr/epvvw4XiCH1vTqUh0M7RZM3KsSxfQ9OwJcmdn3pnQV/B4+BAOxsTQobt2Fdq0dy/uTcCWTFaggLTLqlWwr08fbiGiDQZ406ePWqdS8Z4Pc4AFQB7IV3t5kdpaLayUSLgEUFJKO82apSF4MAYrHZ3z5XNs1Lw57PD0hKu9esGUkiV5Z0JGtDUlBWz37KE7tNp0//37cZ8MduQZI0eSsr/+yu3Dkr1eD+lyuVqj1fKeC1OHBcBnyG3btiV/RkdDcL58XALgsZjMTKciUcIIDw/D4r59IdXTk4wqVIh3JiSA9BcvwD06ms7cuLHqq0OHcKtj41MGenvTw+Hh3J6VKp+TY2jXo0dk6927ec+FKcMC4BOU5V1dadl9+6BCwYJcAvinpkKZ7t3VNWNjec+FJfFtV66cqKhCQUMGD8aH+KwbnZiYSOZFROiHr1kT5Xr7Nu88lkR+qWlTotuxg9smaS5ZWWR8p04qcvQo77kwVVgAfITvyCZNyJQDB7h9Khx6+zap1Lmzqsxff/GeC0vQl+bPr1N4egIJCABlmzbctzNFJiguDiA0VHxQpQpPfvWKdxpL4HOycmWx4549EFy1KpcA9169on07dNAMPnGC91yYIvwl+AFy27p1yY4jRyCsSBEuAXb+8YeuQ9eu2i3PnvGeC3OnUDRoANv79IEdvXtz+/dE5iX9xQv4Y8cOaL9pk1pz6BDus/Ft+ng5OeW23b4dTrq5cQnglZEhmta6dcSN8+d5z4WpwQLgX3yWV60q7nLsGLeHwEb9/rtuoZ+fVpuVxXsuzFVH53z5HEIUCjJ59GioUqcO7zzIjHW8dInofv01Z49Go92Sk8M7jrnq6Jwvn6Pnhg2Q5OvLJcDYZ8/IhpYtVcuvXeM9F6YEC4D3+N0uXz5Xevw4mVi+PJcA25ctq/Jq9Gh8KOnryLzt7SXr+vUj7X/6CSqVLcs7D7IgFZOSaPCqVdLuy5dv2p6SwjuOeSJETqdPJ8rp03n0/vZ5jxYt1Oo7d3jPhKnAAuBvvu3KlRMNi42FzRUqCN65klLqNnq0xn7pUt7zYI58TlauLG47YgTUGzCA2wObyDoEZWeD2+bNep+ff8ZzCb6OYspPP0GzBQu4PIdD79yRTGjRYlO9xETe82AKsAAAAGVg4cK08fHjEFO3ruCdRxsM9JfBgzWD167lPQ/mRr7azY0cHTECHHr04LkXObJC0QYD2O3ZAy2XLlVrDh7kHcfcKBQBARAdEsJlr4Az165JE1xdN5L0dN7zwJvVFwABg6TSzNG7d0Nw27aCd45bV34VZWLXrvTx1KmwuGFD3lkQgtF//knfzJqlaR4dzTuKOVFKFQp6dONGHhus0TFHj+bOa9/e2p/rsPoCQFEjLAy+79dP8I7L5+SAo1yunrB1K+85MBcKuYcHFJ0zh+t+4wh9jNPZs7RXcDAWAnnH83A16r1+vabHgAG854Anqy4A5IOmTSOvZs4Uul866/Vr0r5HD/Xtfft4z4E58G3XrJmIBgdDsVateGdB6LPWnjpF3adO1Zw7fJh3FHOgrNOxI+39++9cjle/N2WK+tTcubzngBerLQAUdX194We1WvAHUe69ekUmdeum8jx0iPccmDrfkU2aiDYHBUGrLl14Z0Hoi504eRL2Tp6MO3l+nuJaixbwU3Q0OBQuLGjHSkqhYt++6prh4bzngAerLAAU11q0ANi/X+j9/WlYejr81amTpu7p07znwJT5Xvz+e1HHWbNw4f+E8jk5UPT5c9rl2TMolZQkqpqcbBj+/DnZ++wZafD0qaF2aio5mpkJAEB+zMigKw0G+vLNG1IoK0t8kFK9xz8fgBIfdHDQexBCXxYoQArlz0+Gi0R0hb09AABtaWcnulKkCI0rWZJ2KFaM5CtWjA4pVgyelChBdhUrBkuKFeN5RLbJaxkdLTJMnRoxJD6edxRT5ruuYUPR8L17oYeTk6Adl8/JoYc6drTGKzZWVwDIV1evTo6dOgXg6Chox2OfPROda9sWfwl8XJ+LZcrk7ps/H2orFFa/Va+SUpidmEhXJCQQ24QECE5IoCUSEsTVEhIMsQ8eqNVpabwjvk+hcHQEr+++I8ednWlK5coQ5OxMXzs7kx+dnWFqmTL470kp6a9S6edOmBA54PFj3nFMle/F778X6fftE/z8AP/UVFKzWTNr23rdqn4o5auLFiVDTp8W/Hx33Iryk2TeNjZSm9GjwT0oCI7Z2fHOIzQacvcu+ev8eXCJi6Mbrl+XTE1IECUlJGwkb97wzmYMMlmBAjbbnZ1hk7Mz7VG9OsQ3aACPGzTgsucGZ3TJy5eihsHBOY2XLLH2J9A/htdW7DTk7l1JlR9+CE9OTuY9B0KxmgJAJitQQFrr0CG42bSpoB3jYRSfpJB7eMDZpUuhSc2avLMIIu7JE3gRF0cfx8XBibg4Sc8zZ6zpF877ZN729pK2deqAb4MGpGKDBuDYoAEsq1HDKq4WON66Zeg5ejQeV/thvA5joxvOnSv0Y8uWoaGvX/OeAyFY/g/a22ESxYPISJjYq5eQvdJZr1/D/E6dNGuOHeM9A6am92hnZ0Pk4sUWfZ/fXq+nS8+fh5gjR2j9I0dEE86cMbVL96ZGJitSxOZGkyaG4JYtSenWrWH9999b8iZPtPvOnYbSY8bgUcT/pVjZqhVk7t4t+NsBJSIj1Yvlct7jF4JVFADy3ePGEdWCBYJ2Wj4nx9CuRw+s8P8pIMDW9uXq8eNJ7QkTeLz7yxy9cwfIwYNADx7U5R46pNWmpvKOZM5k3nZ2UukPPwDx8ADw8ABl/foWd4UgVqeD1JCQ/MODgtYvfPmSdxxTogxs144O3blT6Ae2wWPMGHX/xYt5j581y/pB+gDFylatoN7+/YLuNmWv10O6XK7WaLW8x29KFPN79IBtS5da1EE9Y589g5927qR2+/dLzhw9aq2X84XiV7x4cUPFVq0ME9q1Iwe7doX0okV5ZzIaj4cPqW7ECM3g7dt5RzElSk337rT85s2C/g4fnptLmrVtqyJHj/IeP0sWXQD4titXTvRzXJygT5RGGwzwpk8ftU6l4j1+U6FQODoCzJsHEBDAO4tReDx8CAdjYujQXbsKbdq7N3SNTsc7kjWaTkWim6HNmpFjXbqApmdPwR/uZYVqtbTlsGGawc+f845iKhTX/PygyYYNgp4dcCg5mfRq0EC1/NEj3uNnxWILgI7O+fI5doyNFXTbWCWl9MTQoZqfV6/mPX5ToXjRpQuErl4N50uX5p3lm9A7d2DHrl10sVarGXzyJAClvCOhf5J71qoFO2Uy4iyTmf1DpYeSk6li2DDN4t9/5x3FVCgD+/enHdauFfQW0NwzZ9I83N1jErKzeY+fBYstAOTHQ0NJyKBBQvZJQ0aNwiN935KvLloUuq5YQcb6+PDO8tVS79+nMRs2QGhkpGbwjRu846C88xlao4boNx8f0rFfPyjy3Xe883wtukmtlnqPGLFpe0oK7yymQDHlp5/g7sKFgnZaKiREvWjYMN5jZ8EiCwD5nT59SJCwJ+wR7bx5Kt2kSbzHbgrM+lN/UHY2zN65kw4LD89dtmePVqvX846Evt7/3ybw84N7SiVUKFiQd6YvhlcD/kFxetEiWD5mjJB9EqcBA1TL16/nPXajj4t3AGPzvfj996J9J08K+uoI1WqrqH19ZxKDgff4eTLve/1xcTQkPFzaNyICP21ZJpm3vb3UxscHTvTpA26urrzzfDF8NgAA3hZ1tzy0WijRo4dgnca/eWMY07x55IA//+Q9fmOyqAJAJitSRLL2zz/J0IoVBevU6exZu+xWraxl44iP8X3YoYOo4caN0KZ4cd5Z8oqGpaeTmmFh4pfr14cnX7nCOw8SjlJap45hav/+pLq/P2x/e+aBWZjz9Cn5tW9f1fL9+3lH4YnHxm503oMHENOggSUVYBZTAMhkYrFk/r59JKhNG8E6dbx1i9Zt1sySviG+lEwmFks2T51KCk+dKugTut+Ahty9S4auXi1VrV69kfzzUBxkXWTednZSG4UC9o4ZAx2qVeOdJ0+UlIJs+XI75U8/WfMbKH7FixfXtzl9GkilSkL1SUP2788d2KmTpdwatJgCQLF7+nRQzZghWIdbU1L0C5s1iwq8eZP32HmR25YoQbwiIgA8PHhnyZu4OBq8bNnjimr1MZKbyzsNMh3TqUh080TnzkQ5YYLZ3B64ERtrGC6XW/PhQj4nK1cW5z99WtBXve9NmaI+NXcu77Ebg0UUAMqTjRpRn5MnBTuSNP7NG/LSw0P14ORJ3mPnRd6odWuyWaWCKSVL8s7ySdEGA9jt2UOk8+ZZ878XyjuFokED2DtyJETL5YJuPvM1HJ4/N0zy84sst3cv7yi89O7ZvLlh9oEDgu0WODw317DZzS1y6ZkzvMf+rcx+j22Zt52d6O7+/VBCoApQSSms9PdXp1nnFr/uVCJxzZo9m+wNDYVHwh7U8UXK5+QQ9erV4vy+vqqk3367nPHwIe9IyDxcvvzkyeWsbdvq7YqIoK42NlDExcVkzyN4Y2tLRioUte9IpTUexsZeu2Z9+1Ncuv7gQd0/b92CwJ494bIAewScE4lI31atGhUNC7twyrxPdDTNb+ov4DIyNJTcaN1asA4vT5qkjg8J4T1uHuS2JUoUvrZ1K7zo2xeqmeh+7NEGA1TdskXv16OH5reIiEuv8B4/+jqXXqWnXz69e3e9jZs20SMFC8JwFxc4Z4LPuVQjhBxo0UJ8q2XLWsH791/ZaX3nCVxOvHq1TqzBAN+1aiVIh38WKZJ7s3jxy/d27uQ99m9hmr/E80ju2asXKRQVJVR/1Hv9ek2PAQN4j5sHhdzDA35SqwW91/Ylog0GCNJo6JCZMzX2t27xjoMsj8/yqlVFQ6dPJ46+vib7wOuh5GSyVqFQeR46xDsKD4qNGzfCvj59hOqPlvD2Nuf9Gcy2AFAGli1Lu8bHQ1iRIoJ06Hfhgm69q6tWm5XFe+xCUygCAiB2xQrBnrH4EkpKYeDu3bB06lR1r4sXecdBlk8ZWLMmfT5jBvT29jbJkwnt9Xp6bfRozbHly3lHEVpfmj+/TnniBECDBkL0R8PS0yWPXFzCKz94wHvsX8P0vnnzYDoViW4dPXAA1gh06X9rSoq4cKNG4cl37/Ieu5DcqURSZvHixfDnjz/yzvJBy/fuNTycPDmy3oULvKMg66NQNGhAQ+bOJUPbteOd5YO2L1um6zxmjKW8spZXimvffQcr/vxTsJMib8TG6iq3bm2O82yal7E+41bOhAmCLf72ej0ZpFBY2+Lff1yhQmVKb9tmkov/0Nu3gfbqpXbq2BEXf8SLWh0Xp7Fv354O7doV6J07vPP8h9eIEZK1e/bIvM1ooyMjUNe8f5909PUFe4EW5OotWkj6Cbs1sbGY3RWA3tXr1zd4nT4ND2xshOiPKseP13QW+PAJzhSKSpXgTHS0qZ2oRme9fg2VFy60gXnzNpI3b3jnQegdmbeNjWTd0KGQPXs2GWVib8c0vnpVPNfT09o+xMinTZlCEoKDBeksVqeD125u6tSzZ3mP+0uYVQHgV7xgQf3R8+chuGpVQToc9fvv6sYymTUd/erbrlkzUfy2bSa3pe+RXbvg0I8/qmvev887CkIf0+dimTK5rX/+GcJ79zap5wO2pqRQv549NWuOHeMdRTiEKOpu2wa1u3UTpLvKN27YJTVoYE7bwpvVLYDcW/PnC7b47/3rLxLev781Lf6Kur6+osRDh0xq8a984wbd3q6d+omnJy7+yNRtqpeYqE7t04dub9UKOl66xDvP//RwciKj9+1Tru/bl3cU4VCav72fHyy5fl2Q7m5Xr555SKArDkZiNgWAb7tmzUjZoUMF6cw9M5Moe/RQLX/xgve4hUGIYvycOVBbowGX/Pl5pwEAgHuvXsHQkSN1N2rX1rw+cIB3HIS+hGbNsWOJvRs0AI8xY+gsE/lEGJwvHy0RFqbsOHMm7yhCWb/w5Uui694d0gX6XZ48cqT8knAHFH0r07lE9QkBAba2mdKLFyGtShXmnSkpJXt79VIt37KF97iFIJOJxZIRISEkZNAg3ln+58TJk/px/ftb8zkLyHL4Fa9YUe+ybh0UE2iTmrwoFRJS5Zcff7SWI8yVmu7daeHffxfktsyty5ft6jVoYA4HNZnFToDV0ufPB6fOnQXprOXixeqAJUt4j1kIHZ3z5bP9Ta0mS/z8eGcBAACXrCxImjq1yvFBg1Y2sd4TFpFlufQqPf3ynU2b6tR5/BjcW7WC+8I8wPxJmY0apd52di5yYOfO+zMtvwi4/PuNG3UvOzlBiSZNmHfmVKJEzoScnMsrY2N5j/tzTP4KgPxS06Yk5PhxIfbipoFXrtj80KiRNTxh3pc6OOga7NgB1Vu04J0FAPBTP7IKJnc1oGV0tHRQr17W8Duvo3O+fI7Tz56FmLp1mXcWlJ1NQurXVy2/do33uD/FpJ8BkHnb2JCS69YJchCHS1aWSO3jYw0/CAqFo2OObv9+k1j8XbKyACZOrHK/RQtc/JGlC0++e1d9oE0bgMGDwT0zk3ceOOrpqWu3Z0//cSb26iIDMQnZ2aR/794QL8Dv+OB8+QxdV62aTk10y+i/mXQ4iePEiTCqRg1BOvMfN87UqzVj8CtevDjcOnaM9GvUiHcWWHvqFMTXrq1Wz59vLfciEQKgVK0ODRXd+P57mGsCR8oWa9XqTVRMTF/q4MA7Cmsq3eXLpPWkSUL0RcKaN08oGhDAe8yfzMg7wMf0Hu3sbNh/+bIgT6Uv37tX7dSpk6W/8ie3LVECDh08SJbXrs01iJJSkC1frus0bpx2i3kfp4nQt3CnEklpCAoihadO5X7AkN+FC+K+HTqEJycn854XtghRSqOjqUyA58rSX7wglWvVUi1/9Ij3qD/EZK8AGJJCQgRZ/A8lJ+uG+Ptb+uKvDCxblvgfP8598d+akmIo4Ompfj1yJC7+yNodI7m5GjJjBrnRvTtAWhrXMOHff68ffOxYn4tlyvCeF7YoFTn27w8Vk5KYd+VQuDCsWrWK94g/xiQLAMU1Pz8ADw/mHSkphVsDBmi1T5/yHjNLMu9ixejU/fsFeY3yE+iGc+fEhRs1imy9ezfvOUHIlKjK7NxpeO7iAmtPneIa5Hb16rlrjhyRyUqW5D0nLIUnJyeTnf7+oGT/wY/KOndWyGUy3mP+EJMrABQKR0do9csvQvRFJq5YoS68axfvMbPUlzo4SFbu3SvYsxQfoqQUti9blqtwc7O2/cgRyqvI/Q8fJhZwd6eqmTMhmuMzMWlVqki77tsnkwl01DonqssxMbTK6tWCdDZ32TJTPJTJ5AoAaDhrliBb0Z65di2n+oQJvIfLkl/xggV13+3aRUbXr88thFdGBj0ok+Elf4Q+790tAbrHywv8U1O5BYmpW1e60PLfDrCpMno03Lp8mXlHU0qWlF6YPp33eP/NpB4ClNvWrUsOxMXBSomEaUexOp0oqFGjiCHx8bzHzIpMVqCAVBYTA9vd3XlloIFXrhh+8PSMIvfu8Z4PhMyNSZzK+ezIEZ1D585abVYW7/lgpXf1+vUN686cYb7uDM/NpfPq1dNEX73Ke8zvmNYVgNilS5n/IwAA/WP+fEte/AMGSaU227Vanos/FN23L3eRmxsu/gh9HbX6zp38PX74AY5wvE1ZrFUrydrt2zs658vHez5Yibhx/jysXLSIeUcrJRIyZtky3uN9n8lcAVBIlUqQRUQw7yjo5k1pDRcXS93wRyYTiyXLVCoy1seHX4rQ0ETV8OHHSG4u7/lAyNzJZGKxVDpnDgC/W5Z04LZtj1v16mWpP9MdnfPlc5RfvAi3q1dn3hnt1Uut0Wp5jxnARAoAmbednfSHv/6C86VLM+0o2mAQtWvZMuL348d5j5kNQhSt1qyBUgMGcOl+eG4uVBk1Sl185UreM4GQpVEoAgIgdsUKaCGVcgmwNzy8Skq/fpa6aZdiZatWUOHQIeYHBnk8fGj3R/XqoaH8T4k0iVsAkiUTJjBf/AEAAlavttzFH0CesXw5t8XfPzWVjmrfHhd/hNhQq0NDSfcuXWhYejqXAB38/BLqWu5BaerhR45AxXXrmHd0sFy5l/Snn3iPF8AErgD0uVimTO6wv/6CCgULMu0o7skT6Y2aNTcSTj88jCkUEyYAzJvHpXNNQgJd5empGXzjBu95QMjSyTOqVCEVo6OhQ7VqXAJUHDdOPUeYV7WFJvO2t5dMu3qVzGO8GZJ7ZqZocdWqETeePOE5Xu5XAHLj585lvvgDAB0zbJilLv7KJj4+oPz5Zy6dO509q/Nu0gQXf4SEobG/dUvXunlzuvj8eS4Bls2fb6ob23wr7ZaMDNGaUaOYd3TMzs5QavZs3uPlegVAsblePRgQF8d8D+x5mzery/N8KI6d3j2bNzfc2L9fkG2T/+1GbCxx9fRULX/xgvc8IGRtZN729tJWu3bBSTc3wTuPf/OGvPTwUD04eZL3PLAgP7x1K1nbvTvTTqINBlGZRo0ibnAq5ID3FYDtCxcyX/y9MjIMB0aP5jpORnyWV61qGLJ9O4/Fn2h379ZV7tABF3+E+NBuyciwi2/fHoru2yd45y7589PkHTvkGXy3F2dFPGz4cOZnM3iKRIZyfG+lcCsAfB926CDEfv/UcfToyAGPH/MaJyvKwMKFxUu2bYMw4bfrpIuiogr2697dkjcHQcgchIa+fq173LUrnbhli+Cd93ByIhWjoy3xGOG39+YnTmTeUbFWrRSV27fnNU5uBYBowcyZzDupevq0pu2GDbzGyIpMJhbDKrWayw5he8PDH5fs3Tt0jU7Hex4QQgDaLTk5ubN9fan3+vWCd96hWrWcF1FRMplYzHsejK2Kau1acDp7lnU/dPf8+dMpn6OguXSqONCtG6Q0bsy0EyWlsGzUKEs85ld6fuFCQc6y/reGK1aoU/v2tdTNQBAyV1qtXq/pMXAgnFm8WOi+ydB27SQe8+fzngNjm0kMBjpo1CjWJwaSWS4utxQ9e/IYI4cCgBD4ZcYM1r3QR5s2qVPZV29CU67v2xeaCP9MA0mbNUs9JjDQEgsqhCwDperbY8YQrfCvA5NjY8cq1/fty3sGjE1T9/RpaB8ZybyjqcHB7pT9Nvj/JvhlG7lnr17EITCQaSfumZlU0r37lZ0vXwo9PpYU1777jtaLjiZ7hd2Xm1ZfskS9RoD7YQihb3bZcOhQHUc7OyjcrJmgHR9u27ZetkZz6ZVlvW5dN+3MGbg+eDA42Ngw6yTWyanwlQcPLh+9cEHIsQl6BUAmE4vJNfZHItLEefMs8cE/4rJyJRkl8PGcpUJCNNPGjOE9doRQ3qljx4+HJwLsave+CgUL6oOXLuU9dmNTLX/0CKYsXMi6H9p/2jShD10StACQ7JbLWT+4RkPu3rWZKcDJTgLzWV61Kt3eqZOQfZJDERFVfvnxR7zsj5C5obTK4YAA2KTRCNptmS5dfJZXrcp79Mam27BgAaTev8+yDzKxfHlHx/79hRyXYAWATCYWk/xBQcw7mjFunCWe9CcZ4+/P/JCK9yVt3froqb+/pR78gZClm0kMBruhfftCy+howTpVESJq0q8f77Ebm1ablQWPBbgNOnTSJCGvAghWAEhr9evHfO/qEydPahZv3SrUmIRk2OPqKlhnQQcOpN1XKPBpf4TMW+ganU6339sblu/dK1Sf5J7Azx4IRH0pMpL6Mz5M7mC5ckXS/P2FGpMgBUDAIKmUjp4yhWkn0QYDuI0caamXq0lY7dqCdOR17JhuerduMQnZ2bzHjBD6dtotOTniGt7esPbUKUE6dKxTh/eYWaHFRo6EaLZXRenCyZNl3gwfOHyPIAVA5sWBA8nQihWZdnJm/Xq1Oi5OiPFwUZ79gUngd+FC/nOenrjDH0KWJTz51Stpgc6d4dbly8w7KyPA7ypOIutduABlN21i2snBcuUkVYS5CsC8AAgYJJXC0EmTmHYS/+YNCRFgZ0GO6EC2l+PpxMRESSlPz/ULLevVSYTQWxtJejps9PSEiklJTDti/LuKu99nzIDyOTksuyBNJk4UYl8A5gVAZlulEg6WK8e0k+mrV6uWP3rEeiw8kfb37jFr3CUri7Ts0WNTvcRE3uNECLGjrnn/Ptzt3JnOev2aWSe6O3d4j5Mldc3790nI2rVMO9lcoUIZG/Yn2DIuAAiBxz/9xLQLl6wsw8sFC9iOgz86/tw5Jg1HGwzUQaGwxF0TEUL/pVbHxYmW9u3L7F62neX/LiGlgoPBhfGt0noTJgCwffOLaQEgX92tG5ytVYtlH9B7xQpL3PTnv7ZvZ9LsiAkTNIMZtY0QMkmq5Vu2wBlGm7LtsPzfJxE3njyh2atXM+2kSp06vofZ7v3CtAAgfuPGsWwf3DMzxd/zPU9ZKLkHo6Ohl5FvA/iHhannWMf8IYT+SV0zOBjaG/mBttT793UDYmJ4j00IuZfmzqVL2D4zRfQTJrBsn9lZAL17Nm9O90ybxjI8+X3hwogHAm5ywdG1a5TWvfv0KRTx9jZKgzdiY3V3fHyuXdPreY8NIcRH02K7d+c8dnODgkZ6Syt/QEBU1JUrvMclhGvXXr+um2hvD+DmxqoPcvG772qVOnDgyu2HD1m0z+wKgOHW2LGs2gYAAK+MjByXX39l2oeJUZ2JiqKLoqK+uaEl169DdS8v7Ra2T7IihExb6Bqdjnr36gVBN29+a1t0k1qt1mi1vMckJMnrhQsh/cULln2QX9id/sqkAPArXrEiuHbpwm5KAGDMokVabWoq0z5MUHrzvn1p9507v7oBx1u3DNEeHmp1WhrvsSCE+NMMfv6cNmzRAs5cu/bVjSzfu9dGPGAA77EIbdP2lBRClyxh2QcJ6d5doahUiUXbTAoA/YqRIyFDzO6o4a0pKaS75Z06lRcxCdnZ6ZN69aJD16wB5ZftekiDDx0y1G/Z0joemkQI5ZXmdVKSYXLbtnTM0aNf9BeVlELQ2rVpTby8LPEMlrzIKfjrr+DP8MNohlhM3YcNY9G00QsAZWDhwrCB8S5GPRYuVC1ne9nFlMUkZGdrmgcE0BkeHjD3zJnP/oWht28DDB6sqdS2LS7+CKEPiRzw+LGmYevW4OXvn6eT73b+8QfZ26GDuuagQda8dbh2S0YGPc72djRxHDiw/zjjHwVv9HcMFetHj4aDDCcj/cULnW358totGRnM+jAzctu6deEnT09yy8UFxlSsCIsJgaLPn5O9f/5peHTsWNVXhw7hqX4IobwjRFm+WTPD8q5dSWTFilCtbFkYTyn9IymJuJ47B/7791v01utfSBlYuDBt/uABbLe3Z9bJ0JEj1c2XLTNmk0YtAKZTkejWiZs3IaRyZWaT8OiXX9SxjF8vRAghhL6AfNbixeTGqFHMOtAkJFQxVKtmzA9zRr0FkFC3fXumi3+sTicOW76cWfsIIYTQVxA9W7QIYnU6Zh3InZ0TRnh4GDWzMRujgUOHMhs8AMDryMjwyg8eMO0DIYQQ+kKq5Y8ewb0tW1j2Qdsad401WgHgd7t8ebjAdttCw+HFi1m2jxBCCH21PosWMW3fqUsXZWDZssZqzmgFgH714MFMX/0LOnAgst6FC8zaRwghhL6BWh0XB8+OHGHWwUqJxLBs4EBjNWeUAiBgkFQKxfv1YzZoAABPxpUVQggh9I0Mk9iuVaR6QEDAIKnUGG0ZpQB4lb9bNzhfujSrAdPAK1fUt/fvZ9U+QgghZAyRrffs+aZdFT+nQalSLxt27myMpoxSANBV/fszGywAkIeLFgF82a53CCGEkPAoBT/G59QUMc5me99cAPS5WKYMDGzXjtlA6z9+rNusVjNrHyGEEDKitIiICIh78oRV+6R0p069q5cq9a3tfHMBoDvj58f04b8aoaF4ah1CCCFzEZOQnU0qrVnDrIOVEomhp1L5rc18cwFAHvXty2yQ0QaDuHpYGLP2EUIIIQb0+rVrwV6vZ9ZByLe/DfBNBYB8tZsb3K5endX4yJuYGNz4ByGEkLmJ3P/wIQQfOMCsgw7VqvmObNLkW5r4pgKAHPPzYzY4AKB7GF5CQQghhFhay3YNE2/o0+db/v5XHwYk87axke56/Bh6ODkxGdmcp0/t5pYvH7qG4d7KCCGEECPuVCIpU/3BA2jw7Q/sfZDD8+d2utKlv3ad/OorANLBHTsyW/wBgLRftw4Xf4QQQubqGMnNhQabNjHrIL1o0Ze/tG37tX/9628BVFMomA1KSWluGD78hxBCyNyFhoKS3T42ZNXXr8VfVQD0H1eoEM3p0oXVgKjbgQNRrrdvs2ofIYQQEoJafecOLDl6lFkHO7y8ZN52dl/zV7+qAMiu0aMHmWZry2o8oiB8+A8hhJBloIkM17QKBQva7Oja9Wv+6lcVAPRxr17MBuPw/HlqTHQ0s/YRQgghAeXW/P13GPvsGav2aY2vW5O/uACQedvbQ482bVgNBK5s2BCTkJ3NrH2EEEJIQNotOTl0t0rFrAN9+/ZfcxvgiwsAyR5PTwjOl4/VOESKiAhWbSOEEEI80DIMCwCX/PltHn75CYFfXACQyJ49mQ0i6ObNiCHx8czaRwghhDiIHPDnn6BJSGDVPq305WvzFxUAAQG2trQmw5P/jkRFMWsbIYQQ4oie0WqZtT27c2e/4gULfsnf+aIC4FW+Tp1YPv0vnrl5M6u2EUIIIZ7IPXZrHJlma2tY+mUf0L+oAKBlvbxYhYfKN26EJ1+5wqx9hBBCiCN1r4sXYcn166zaN7zo1u1Lvj7PBYBMJhbD9A4dWAWnTzUaVm0jhBBCJkG7ZQurpknDTp1kMrE4r1+f5wJAOtPVleXe/wYJu3sjCCGEkCmgErWaWeOLihWTTG3cOK9fnvdbAPYMt/6dFh8fFcLusghCCCFkCjSDb9yAxlevsmpf5J331wHzXgBI2RUApCI+/Y8QQshK/MJuzaNTPT3z+rV5KgAUikqVYFSNGqwCiybi5X+EEELWQT+B4YfemLp1fWiFCnn50rxdAVjSsSOzsH4XLkQsZrc5AkIIIWRKogJv3oSOly6xal+szNvrgHkqAOgxhpv/3Nm1i1nbCCGEkAmi1XbvZtb2xLZt8/J1ny0A3KlEQqi7O7OgzWNiWLWNEEIImSJit3cvs7bntWmTl9cBP1sAlA794QfYbm/PJmZaWu7ss2dZTQJCCCFkihJrnDpFw9LT2bTu6Cg91KDB577qswWAaHveLiV8Dbpo/36tVq9n1T5CCCFkio6R3FwSf+gQsw5OfP7W/WcLANqEXQEgisHL/wghhKzUHoZrYO/Pr92fLAD6jytUCNo2asQknJLSnJh9+5gNHiGEEDJhEq+9e0FJKZPG2/3wQ0DApw/v+2QB8HqEqyuslEhYZKMNL1zQap8+ZTJwhBBCyMRtqpeYCKmXLzNp/IGNzcsLP/zwqS/5ZAFAnrRowWrgInz6HyGEkJWjhdm9DQBnP72Gf7oAqM3u9T94gwUAQggh6ybqynAtjPv0Gk4+9h9ksgIFpJXT0+GBjY3xU6WlJaqKFz9GcnOZDRwhhBAycQGDpNLMts+eMXndPv7NG+kVR8eN5M2bD/3nj14BkNxr2pTN4g9AJx46hIs/Qgghaxe6RqeD/EeOMGncJX9+vffHH+T/+C2Aem5uzEYcGBvLrG2EEELInDxntyYa1B9fyz9aAJB5TZuyCkSXnjjBqm2EEELInJBq7NZEuvPjbwJ85BU/QqBMo0bQg0Ea98zMJy6MXnswQ31p/vzZR9q0EfWpX59eLVGCDClenHemL0F/NRhI5aQkSHv6FNb+8UfisOPHTe32TkfnfPnsj7RqJXrZsCHsLVECzpUowWwuxmZkUPeXL0XbX70yeD18CHDjRu6Bv/7Sbnn2jPc8sBQwSCp9qWrZEn5q3Fi0rUQJurtECZhAyLe3LKBGSUnQISkJPM+d031/5Ih2S04O70jvc6cSSenGLVqQE02akIalShn2Fy9OxojydqKriaCrkpNJraQkejUuzqbw4cMfuzdtbR41u3ChzJXMTDhmZ2fstsmPHy8APvgDqkysVo2Ou3GDyUiDDhxQ12R4uqCZ6F29VCn9zmnTSD8/P6hQsCDvPEbjn5oKB1at0t1ZsEC7JSODZxS/4sWLG0oEBdFyffuCQ+HCXOel/uPHZOHhw9DryBH9XwcORO5/+JBrHiPp4+XklDth8mRav39/4u/gwDuP0aS/eAFHN2zQdQoO5l289aUODrk2EybQPQEBEFakCO+pMRr3zEy4GR6uezBrFu4JAyD/88gR8mvLlmxar1xZrb5z599/+sHq0ZDdpAmzUd49eZJZ22ZCMWPAAEORW7fIjCFDLGrxBwAIK1IEHk2eLC2ekOD7sEMHXjGUUoUi90pCAq0TGMh98QcAOF+6NG3TuzdNWbdOdPrePfmfR44oNvfr139coUK8o30txfwePXRRCQmwfMwYi1r8AQAcChcGrxEjpK8TEhR1fX15xZBndOqkK5CQQGUTJ1rU4g8AcMzODp4MHSpNTEhQbO7Xj3cc7kqyuw1AtB++CvDBAkD0PcMCIMK6CwBF4oIFcHPtWotb+P8tvWhR0dxdu+TKYcOE7lpOZ8yg2yMiyCgTXVw9RSLya8uWsD0s7M3VR4/kZ+fOlXkXK8Y71pdQlh8/Hmpv2WJxC/+/ORQuDD+r1fJB06YJ3bWiyI8/kok7d0IPJyfe08BUhYIFYXtYmEIxbx7vKDxRynBtrP4FBQA92LgxkxD2ej0par3H/yrWjx4N48aN451DMBliMdm5fLniQLduQnWpdB80iCinTweVmdx/dihcmCyZNEky7949+Y7582Xexr8HaGwKqVJJQ+bNM5s5/lYqQsirmTOVgf37C9WlfLWXF+iWLoWMz5/pbjkmTJC7BwbyTsFLvrJ//AHRBgOLtmnLD3+o/88PsMzbxkY668ULCM6Xz+gp/C5cUHesX5/FAE2dQtGgAdifOWNdP9Bv0SUvX4pbVKsWcePJE5b9KANr1qTbLl6EFlIp7zF/tTuPHpFGo0erlm/ZwjvKhygUlSpB/NWr4JI/P+8sgiufk0MrurhoBjN6PupvfS6WKZN75sYNFg+EmbzhubmQ2KiRutfFi7yj8KBodOkSVKlTx+gNx795Y9escOHQNTrd+3/8nysA0l41azJZ/AEArlvv5X8aPH++NS7+AABkVKFChvozZrDux9Di55/NevEHAKhUtixN0Wrll7RamTeDncG+1abgYKtc/AEAHtjYkFI//8y6m9zRM2da5eIPALBSIoE1v/7KOwY3LRg9B+CSP3/WjmrV/v3H/ykAyHGGn9A3WGcB0Lt6/fokqE0b3jm4Gt6/fx8vdvcylYnVqpFtXbvyHqaxkHne3lLp+fO+6xo25J3lHd925crBPn4PxJmESC8veUaVKqyal68uWhSC+/blPUyuirVqpdhcrx7vGFwsY7dG6o9+//2//+y/zwAcZTfx4m2nTrFq25QZAr28eGfgbqVEou/apQur5g27unfnPUSjI5UqiZqeOKEM9PbmHQUAgGR7eVnNff9POcHu51l0pWtXVkewmxMqs87fmfochh+SPfNQANAMRlcAxj57Fl75wQNmgzNl6xieqmhGaDl280B+ZHd0NVfB+fLR3MhIxYwBA3hHgeUWOsdfSOTF7vvYUBvnGAA+e4qdpYoi9+7BWDb7TtCozxQA06lIRP+sW5dJ546XLrFo1yxUK1uWdwSTUIbdPNDLpUvzHh4zGWIxNFqzRj5p8GCeMYizBc/xFzBcZvjz3BZ/VwAAkEZWPA8Tr1xh0m6devUA/nkF7x8FwC1lhQqs3p0mSfHxTAZlDjKKFuUdwRRQJ3bPAJBu5rWF8hdTEULSV66Uj+7Zk1uGghY+x3lE+jF8Lz8Hf1cAAMB+C9v06EsMYvNhmfg7OCgDy5R5/8/+eQtgVc2azAb10Ir3/0+3zqf//41IkpOZNW4Nc5whFpP9ERHK8q6uPLqn1jDHeTGd4dbAaTjHAADgY9lnZ3zSNXZrJd1Tq9b7//ufBcCQf/5HYxJNs+JbAOgt78RE3hHMnkv+/LR0ZKR8NX5S5Obho0e8I1i83633dwVpy3CtnPLPD/n/KACoe40aTDodnpsrvn7tGrNBIbNAkw4d4p3BIlQqW5b8uGnTdGpeJ8FZCuqD38fMTbHeOc5ZcuUK2Ov1TBoP/8QVAFKf0RWAPjdv4rGPVq58To7N1ZgY3jEshqxjx4SiAQG8Y1gdJaWSKjt38o5h6ajTjh28M/Ci1WZlQeDt20wad//oFQBC4Hz16kw67YqX/61e/ZCQjSQ9nXcMS0Lbz53rVxwfzBNU+8jI8OS7d3nHsGS0+86dmuirV3nn4DoHuYzWzLO1ar3/JsD/CgDfdmXLstp+khZi9FoDMg/+qak0LTiYdwzL4+ioV1n3CWpCorNev4Y+QUG8c1i0oOxsUeykSbxjcLec0YOADoUL+64rVerd//xfASDq5uzMaizkJyt+BdDa2ev1ZEzv3prBz5/zjmKRevTtK/dk9/Au+puSUug6cKBafecO7yiWjAwNDFQtx+fFRK3ZvQkg2VO58v/6+d+fnmS3v7X4Gd4CsErlc3JIowEDVJfx3j8zniIR8R4/nncMixZtMEDS2LGaaxoN7ygWS0kpdQ4KUh1bs4Z3FFOQW57dmqkf8v8f9v9XAFCf/68KjCr9xYvwyg8fshoMMlEeDx+Kbnl4qPpv3Mg7isVzVih8aIUKvGNYpLHPnhl2dO2q7r94Me8oFss/NZUelMk0s+bM4R3FVFRvdvcunfX6NYu2yYEPFABkGaNbAA63bgFQyqRtZHruPHpE3SdN0sVUqxbx+/HjvONYhZUSifi3/v15x7Aoh5KT6YLgYLLB2Tmy9e7dvONYJIfnz6nPggUQ5uysWfz777zjmJKZxGAg7e/dY9E2Hf3/H/b//9Spfs7OwOBCLV2UkABqFsNAUHTfPnh+/z7vGHRUSgrp8PixYeEff0Sq4+LgD8sp+GjwoUMkKI+v5PhLpaRT8eJ0fblyMKx2bfAU7j192rt3bxg+fbpZFts3YmOh+o0bvGNQn/R0UiQxkSjj4pyfnj49kxgMvDMZzdpTp2CgCTyMvTAjg9olJpLECxd000+e1HZj9L67BSAP7t6lTYy/Oy8Z+/8f9v+/AHhdqRKTQSxm9D4jAjLht99UZUzgneR3BR7/8+qMr8u6deq83vt9Nw9+AH7Fixc3JHbrRi+PHQsdqlVjHZMMrVixd083N3O86kIHh4drBq9dyzvHPz6oWNihx1S3ebNGvXQp7xz4YTDv6Py7d+FPBg17OTu/+3cQAQD08XJyYvYKYNuEBGYzhJCJCk9OTlYdW7MmMaV2bVjy448Qz34jLEO+zp15jxshZBxkD5tbALDd3l4ZWLgwwN8FQK6iXDlWgxCvwgIAWa9jJDdXXXzlSnji7g5xT56w7ItuaN2a93gRQsZB27LbcMpw7+2a//YeZQd2Zy+LpuJ7swipU8+eFY3u2BHuvXrFqg8ysn59hcLRkfdYEULfztCeXQEgule+PMDfBQAd+vZ/GF2sTpc15+lTVoNAyJxEDImPJ+cY7t+fIRbTW99/z3ucCKFvJ1rA6BYAANA6710BEG1mdAtgRWKiVotPeSL0jkqnVoPriROs2ifA/oFDhBB7anVaGg1jc34KXfD+LYAijG4BJOK52Qj9Gyk/YwazxsdhAYCQpSCH2dwGIIfevwUQWbo0i07o4AcP2E0NQuYpZ9PRo+DA5mwESipW5D0+hJBx0HyMbgM0ensg0NsrAPElSrDog/TBLYAR+jetVq+HZQcOsGibUHt73uNDCBkHKcDoQcC4t2v+2wKgOZszxWkI29eeEDJXpCabt2Ooe6FCvMeGEDKS64xuo7u9XfNF7lQigZZOTiz6IEXxCFiEPoQ+Z/N2DElhs6EXQkh4tGVKCpOGFxQrJpOJxaJyNYoVY7VnueEOo/AImbsnbD6p04ycHN5DQwgZB73HaA3NEIuBFikiyo0sWZJZ+v14BQChD6FvypRh0S5pm5nJe2wIISPZwW4NzR9booSINCtalFUHokOpqazaRsickaBGjVi0Sy+x22kQISQsiYjdVXT99GLFRKI6RYqw6iB/mWfPWLWNkLnyXVe6NHRgUwDAAdx7AyFLIf6V4VX0TEdHkaG1gwOTxsvn5KxfiJcjEfo30ZshQ0BFmBw4S2bj8dsIWYqNJCMDYnU6Jo1nOTqKyH1GBcC2lBQAShnODUJmx3dd6dKgGjOGWQdFbt3iPUaEkLFQCrq0NBYtkxMODiI6itHGIYXxAUCE3udOJRLSatMmqFCwIKs+9IPOn+c9ToSQEdmxWUsNGxwcRCSKzRUAGoKvACL0znQqEpVRrlxJgtq0YdZJxaSkqEC8AoCQJaGr2KylZLODgwjmsykAiAELAIQAAJSBhQvfKr1jBwDDo4ABALqdOIG33RCyMGJGV9N1jo4SWM9o57DqeAsAWbfpVCS6pejZk95auBBaffcd8w7v7drFe8wIIeMiJRl9mK5RsKCEetjakqMMGn/54gXTWUFAx5UsqVBUqpSXrzU81+ky7iQnxyRkZ/PObVZOFy+uGJq3OaYhYjFElihBVpYtS1e2bn0rqGtXICVKALMXbd9TPicHtu/YwXm2vgptXaxYXr+PIUivl9ZIStpI3rzhnduckAgnp7zOsV5lMBh6JSVptVlZvHMjAMhgs5ZSd1tbCSlZoACTxku/fs12VhDA6tV5/UpRUQDHogAKn6QkmHn4MBm9deujZdu3HyO5ubxHYcrI0CVLAJYsydvX/v3/1AYgIQLnXHTggIrR08KsiabPnQswd26evjgYQAcAigOpqWB/5AjY7diR1nXzZixsP+Pk1KkAU6fm5UvFSgCxFEChSEuDK7GxELR9u04bFYUFAR+03OvXTN4ZPluggAi629qyaJso8ZvFJN0tUQL6yOU0RastM+3yZcWLLl14R0LfzvD8t994ZxBUWJEisKRnTwjetMnR+a+/lFKFgncky+PoCLW7dYPtYWHShzdvKjb368c7kTUivdispSTM1lYE5xhdAUjEKwAm73b16jAkOlphu3TpdMrmQCgkgKCbN6sW3ruXdwxuinz3HZWpVIpra9bIvG1seMexSJXKloXtYWGK0RqNTMZmzUAfRhowWkvPFCgggl/YXAEABV4BMBteI0bcPLFqFe8Y6CsFL1o0kxgMvGNwFzxwoGRYZCQWswwl+fpKV23dKpOJxbyjWAuDHaO1dJGtrQhc8udn0bboR7wCYE5IyKBBiiI//sg7B/pCe//6y65gWBjvGKaCrO3e/dZvM2bwzmHRAjt0kI5ZsIB3DKuxlNEBX+cKFBDBIomERduGH/GhHLNTfd683tVLleIdA+UduTx+fOgaRnuFm6srkycrA2vW5B3DorUbNap39fr1ecewCm1ycpi0u0giEdElbAoAqK7Xs5wTc0I3sTn4xegqFCxInwQF8Y6B8qjmnj2qMjt3CtbfajP5Ps4Qiw2DZs7kHcOieYpEhtvBwbxjWAVnNmspzRCLRfCC0b2cmlgA/A+rOWbAkKxQBAySSnnnQJ9Gw9LTSdLgwUL2SWRsbhcyydqpW7e+lNFBZyxzL2P0TBYL+du3l8lKluQdw9KRTmxe1SYbJBIRyWG0ONXFAuAdkmU+BQDxd3B4FerqyjsH+ozvhw1TLX/0SNA+h5vR4tRCKs3p2q4d7xhfivYuVIh3hjzzFIkkAR078o5h8ZowWkvTxWIRpLFZnEh9LAD+p4N5PZVs6I33T01a4K+/aq5pNEJ3Sxua2ZWhneb3fUxW5svHO8MX5a1ZowbvDJbOkMFos7blEokI0hl9Om2MBcA7VG0m907fmY8PApoqGnzoUOIPEyZw6fyl+VzJAgAgSjP8PnY3syIronRp3hEsHanGaC3NEIvN6pOpuSJmNstkB54oZ4ro4vPnSZBMxmv7ZmJWn00BYJX5fR/TnuaVmfxqXnnRP4nAgVF1cda8Pi2wRJTm9UNCpj1+zDsD+ie6+Px5m+Jt2qjVHPf7dzSvq3p0rPl9H5MR5nU2hyHU/ObY3NC/GK2l9nq9iNUPNT2PBcA7hu3mtUubocf167wzoPcMOnzYpnibNhtJejrPGHSHee03QDLN8Pt4tnntn0KyzXCOzYzIntGr+oG5uSJqw6iqv4QFwDukuBl9cvJPTX0cevIk7xjobxc2bNCt7NiR9+IPAEBSGO1IxkJQdjYpum8f7xhfiv5pRseo2+v1us27d/OOYfHOMFpLHfR6CRRmtDhdwwLgf8zo0iltrNHgEcEmwCUriw6ZNEljv3QpmMhnLDogO5tc5J0ij2bv3KnSmNFi+k7drCw4xjtEHpXZt08b8uwZ7xiWju6RSFg8RU775eaKyChGv+xvYAHwP+byYI97ZmbuQNzdize64dw5/b0GDTT2S5fyzvIPg83k+3h4bq7eYfp03jEsWrTBYOiCu4YKIoHRq/r2er0IxrIpAEQrzOt9VgRAYydM0GqfPuWdw3qlpdGQUaNylU2bRoXgvdWvRZvNmYPzx9j3v/4aWe/CBd4xrMIhRkdcj83NlUD8mzcs2jassLUFQTcqRd8mNFSj+u033imsEQ1LT4dnK1ZIf1yyZJN9SgrvPGYtaetWDcFzAJjSxsTovCZO5B3DaowsWBDuM2i3UVaWBH56/Ro6MGhcXaAA63lBRrJ92bIqr0aPBjXvIFZmyfXrsHT9etFPoaGq5WZ4v9rkhIbqHAMDAczkVoU52qTR6HQDBmi15vNck7kTZRYowOQbeuzr1xJolJXFom1Sxoz2DbdWZ65do+fHjdOo9+wB89qr0HxVvnED6uzbR45HRqqK//EH7ziWgIbcvStqN2mS6kxUFO8sFsvj4UPaMihII9m0iXcUa0PjbG2hAoOGm2RlSWDb69cwkEFoVYEC+InSBMU9eQL9Dx+mT7dty62/fbvWHit5ZoKys6HEkSP0xKVLxPvSJUOr2NhI9cOHvGNZBIfnzyHtyBHYsmNHbo5Wqz3D6Mx0a+afmkrFx45Bhe3bcydotdr+bD4sok+jmwsUIOMZtOv/+rWEPs3KYvHhjzzGKwDsDR4McPBgXr7S8Fyn0xdOStL+lZMDfHaStz7B+fLBvePHNafmzgUAgP28A5kmw8zJk0XT8/jpPUivl9ZIStpI3nt2aQvvEZgB19mz4eSGDXn62g/NMeKGPGS0ljbOypKQg69fM2l8nL09y0lBAGTh06eqMnfu8M6BPqHCnDkKAFC/KwLQf5DDz56p1Xn8Psaril+F9k5J0QzHOTZLk+3tgcGxVuTY69ci6J+ZyST0KCcnxtOCkHmoMGeOotnkybxjIITMD51apAiThq+/eiUiTowOF7lXtCjLSUHIrGARgBD6CuR6sWJM2o1ITRXRwWz2GKfl8AoAQv+ARQBC6EvJ2KyldEh6uohEZmSwaJyUxCsACP0HFgEIoS/xkM1aSpunp4uoG6NTxpo7OQEQfLscoX/DIgAhlCeEwBtHRyZNJ6WliehLRgVAC6lUGVioEMOZQch8YRGAEPoMhcLBAVZKJCzaFj1PTxfBIUYPAQIATcHbAAh9FBYBCKFPEJVg9ywdTU1PF4l/TU5m1QEZjg8CIvRJWAQghD6C5rL7EE33JieLsg8lJTFLPwSvACD0WVgEIIQ+gM5huIZ2SUoSATx7BtEGA4v2DbfxCgBCeYJFAELoX8gWRmvo8NzcqgGpqSKtVq+Ho2zOIBcNwCsACOUZFgEIofcYGjIqAEo/fz6TGAwiAACaw+Y2gMGjFIMdjBGyYFgEIIT+RrqXLcuk4SFPnwIAiAAAoASbAoB0LleO1cQgZLGwCEAIAQAsrliRRbN089uH/0UAAKKmT54wCb8aCwBk5hg9H/NZFebMke8eN4738BFCHK2sUIFJu5Fv13wRAIAh+uFDFn1Qz/LlWc0LQkKgv0yZQkPu3uXRN1EtWIBXAhCyXnQnmwKA5D54APB3AUCGsykAyMPSpWUysZjZ7CDE2tL790liq1bQ6949Lv3j7QCErJJC4ehI/B0cWLRNNr9d898+A3CITQEAKyUSm5L4ICAyb+qa9+9D1ZYtsQhACAlFdJ7N/X8AAEh99Ajg7wKAlnx7OYAFOrRSJWaDQEggWAQghISk78iwAGh5/z7Au1sAXoyuAAAACXF2ZjYIhASERQBCSCjElmEBAO9dAVCr09LAPTOTRTcGPywAkOXAIgAhJAQ6ns0DgDQsPV21/MULAID/HTNI3W/fJsdcXIzdGSlZuTKzGUKIA3XN+/d9Tnp4iD2OHIGDHF51rTBnjnyKTqfpvHAh77kwBrLaz0+haNSIdw5YmJFB7RITxf3Pn88WnTql1er1vCMZC5H26qVQ1KzJOwc8evGCuCQmwrKLF3N6HT9uSXNsbGQ5mysA5FJCwrv//38FAHmRkABg/AKA5sMrAMjyRLnevu1zslUrcejRo1CJ0W5dn0BUCxbIdwNYRBFQvUULgBYteMeAcQAEAAz5AKRjnz2Tj1u7NnfB/PnaLRkZvKN9s4HNmgE0a8Y7BpQFoCkAoASQRqekKFqEhelK/PyzVpuayjuayQmoWBFGGb9ZWvb27Xf/v+h/f7rk/6sCoyJVqgAQwqRthDiKcr19W7+oZUu48/Z+mtDeFgG4WRATi4oVI0smTZLuun1bmdi1K+84FqmHkxOU/eknScTt23LPXr14xzEl06lIRF989x2LtkXu/7/W/68AIIn/XxUYExlVqJAPZTMQhHiLcr19W9S0VSuuRcClMWN4z4PF6uHkRKtv26b0GDuWdxRLRfwdHMiQyEhF2alTeWcxFTdfVK5MptnasmjbsPIDVwBoL0ZXAABA8rhuXVZtI8RbxOKEBFjv5sbrwUDy8JdflIHe3rznwWJ5ikR09MKFCqlSyTuKxVIRAi1mzZJPGjyYdxRTQFbVqcOqbfGqD10BuHHrFqsODavZDQYhU6Cuef++6FjbtlyuBKgIMUxdv14ZKPyzCFZDRQgEr1njcxIfamaJ0GXL5J61avHOwV1thmvm1Q8UAKrliYl0ycuXLPojA/EKALJ8EYsTEvSnmzfncSWAjCpUyNBr2jTec2DR4gsUEJebO5d3DIv2wMZGtHf+fN4xuPuV0ZqZ/uJFxI23RwEDvP8QIFAKDjduMOl0DRYAyDpEkXv3eF0JICJ/f5msZEnec2DRLstkCgXubsoSlXXu7Fe8dm3eObg6zGjNbHz1KgCl7/6n6B//MezaNSadPq9SRSYrUIBJ2wiZGG5XAlZKJNJanTvzHr9FUxECHt268Y5h6fRHvbx4Z+DFr3jBgmDHqMjsdfXq+//zHwWAaA6jAiBDLJZKTWATCoQEwu1KQIFWrXiP3eLl4hwzl2S9c6yT164NniLRt7f0XyTkEwWAfsM//6MxUX+8DYCsC5crARXxQUDmyuEcM1fCeudYfJHlWvnPD/n/KACkvoyuAAAAGWLFbwI4sNnuUv8XbqP5P4zmGCp/W7vvrgTQiYmJQkwDLefkxKptwmqOzc3MYsWYte2IcwwAAIlFi/KOwE0qw1cAB3ziCkClpPv3Wb0JQA9Y8RWAmORkFs2K1v7/05zWju5gM8dk/rfPccTihARxmDA7BpKHKSnMGn/FZo7NzvRnz1g1TQw4xwAAUOb5c94ReKElWT0AmZa2qd4/P4j8owCYSQwGiL94kUXX5BfjnzNgNqLYfPrT6YT5VGkOyBY2c6FPM067EYsTEui+1q2ZXwm4y67IoAmPHzPNbiZIV3bzQI/jHAMA0NdWPA/HGX1YHnThwr//6D8PGpBu//0io0gvWlRxzTq3BKayo0eN3uiS69e1WrwC8A4JPXbM2G3SeQ8eRLneuWOs9jT2t27Bd61asSwCyKPDh5m1XZLB97E5cj9yhFXTxBHnGAAAVrObY1PmV7xiRejB6DZe/v9+uP/vk4YbGBUAAEC9TeA0Kg5o/I4dxm6TlDF+m+Ysd9z27cZuU9R5x47335k1BqZFwPDcXHIgJsbo7f6NzN6xA6INBlbtmwUlpaQOu589mrZrFwzPzeU9TN7EDazz95v+qJsbs8ZbnD//7z/6TwEgSmVXAMBpV1dmbZuwyHoXLkDQgQNGa9AlK0vf6rffeI/LlEQF3rwJV4z4S6N8Tg7xWLaMRVZmRYDjhg0RN548YZEZAEC1/NEjmhYZyap9s1Bs69aIxezOTdEMfv6cGsLCeA+Tq+V790YMiY/nHYOLsuzWSNohD1cAHg6+ehXi37xhkuAJw+rGxIl6TJxorMqeKhctitz/8CHvMZkaveukSVA+J8cojWlXrmT6i97IRQBd8vKlYdSsWazyviMpHRQELllZrPsxSUHZ2fozkyez7obenDGD1cPYJi9WpxNpJ07kHYMXepVRAeCSlfV4519//fuP/1MAHCO5ubDhyhUWGcjS2rWVgYULMxmgiYu4cf48GIxwpKjriRO5o2bP5j0eUxQVcv06qRoQ8O0txcXp6k+Zwjqv0YoAJaVk5IABQhSF4cl375JHffqA0ri3RswBPf7jj1GBN2+y7idywOPHojY+PmBvha8Enh892lo//felDg6kHaMN8x5dvnyM/PcD6Ad3G6JVz5xhEiJDLAb44QcmbZsBdfNlywC+4aCL0X/+SSXdu2u3GOlTrgVS9d+4kRacPv2rF6iOly7pdF26aLXCfMp9VwSA5iuvNtjr9VB41Ci1RqsVIi8AgGr5li1wavx4q3keQEkpdQ4K0gxeu1aoLlWXY2LoixEjrKkIoKN+/lldfOVK3jl4yXnRrBmrHQCh+enTH/rjD3ZGEhkVAAAACdb5HMA7avXEieDl7w/umZlf8veo+8aNugUtWmgGW+/7sXmlWTNrFpkll4NXRsaX/D26KCpKt8PVVei3KzT2t26BvHFj0H7hA3xbU1LICU/Pt4WlsNRzfvkFtvboAf6pqUL3LSQalp4OET4+mllz5gjdt0b122+GyV26wFh2+w6YArrk5UsI6tNH05j97RVTRuYwvP8/48Nr+oevAJT54w9WQQwrrbsAAABQ99qwgY52dia7f/vtk/f6og0GeHbkiOG5q6tmcL9+Qn0qtQSqM1FRNM3ZGbyWLv1kIaCkFFxPnCCqVq00pXx9tVu+rDAzFrU6LU2t69SJXO7UCeZ+ugCnYenpdNTPP0uznJ1Vl9k99f/ZzG137IAwZ2dYuHAhQFoarxxMeGVk0OpLlkh9nJ2FvLryb5Hl9u4lG5ydyZ9z5sBWhps8cUCXvHxJQ1aulNR2dlbXDA/nnYe7huzWRsnzD6/p5MNfTohiWHIypDPYjvHeq1eJJx0cPnQ/whp1dM6Xr0iB1q1pVIMGsLdECVhjZwc/PnxIq969C9327NG8TkrindHcybxtbGxKtWxJ4xo2JC9LlqS5hQoRfWIiVdy9aygXExM5wPQ2HfG7Xb68fmqHDnRUhQpwoXRp0YoXL2ihp09Befq03cUTJ0LX6HS8M77PnUokZXe5u9N2jRuThqVK0d0lSsAEQr69ZYHMp5R0TkqihZ4+pRfPncvtdOyYqd1qk8nEYpvNzZtTZZMmsL10aXq7RAkyhtElY1YaJSXR9KdP6aC4uIxWR47EJGRn845kCgIGSaUvJ6Snk2m2tkZv/FBysjqpRIkP/aeP/oAqgnfvhmudOrEZbsOGanVcHJu2EUIIIfPhO7JJE9EzRlfefbdvV3ft3v1D/+nj1eMqdrcBYCjeBkAIIYQAAMgAhvf/n3z8luLHC4BJJ04wG22+Fi2YtY0QQgiZEdKb5Zr48bX8owWA7ugff0AQo/sz9zw8AgZJpewGjBBCCJm+gEFSKQS1bMmkcZesrPSF58597D9/tADQarOyoMOffzIJtd3e/mXDJk2YtI0QQgiZicx6bm6w3d6eSeMH//jjUw9afvoJUl/jn7D2DrndsSOrthFCCCGz4MVuLaT7Y2M/9d8/XQD8/um//E1aYQGAEELIutEH7NZC8tunP8R/sgDQfX/yJLOjKQ316vWuXqoUq4EjhBBCpkwZWLYsSa1Vi0nj5XNy7OI/vanYJwsA7ZbMTPj+7Fkm4VSEGGa1b8+kbYQQQsjEGWp36AAqRhtm1T99OjT09etPfclnd5GiBYx4jv2//YC3ARBCCFknspnh/f8rn1+7P18AtN+/n9noJ7Zt604lEmbtI4QQQibInUokMLhNG1bti/Kwdn+2AHiy7+zZLz1VLe8cHct+h68DIoQQsi6lA1xdmb3+B2lpOUvOn//cV322ADhGcnMh/5EjrCbBcKpDB1ZtI4QQQqZItIHh2jfvwAGtVq//bIa8tEWj2D0HQAZ06cJsEhBCCCETROt17syqbeJ38GBevi5PBQCZsns3s1lwqldPvrp6dWbtI4QQQiZEvrp6dahSpw6TxpWUQt2YmLx8aZ4KAHXN+/eh8dWrrCaDNPf2ZtU2QgghZFICfH2ZtS26eFG1/NGjPH1pXtsk46KjmQXu26sXs7YRQgghE0KcZTJmjd/ZtSuvX5rnAsCwguFtgCp16igDa9Zk1j5CCCFkApTSOnWgCbv1znAz72t1nguA3IOnT4PD8+fMQi/DqwAIIYQsXEUfH2ZtH0pOrrbk48f//lueCwCtVq+HyLw9WPA1iBPDeyIIIYSQCaBtGX7Yjdi9eyYxGPL65XkuAAAAaN8dO5gF71Ctmty2bl1m7SOEEEIc+V78/ntIq1KFVfv0zs6dX/L1X1QAFHoVEwP3Xr1iFV5UFm8DIIQQskzkPsMr3e6ZmbkH9+37kr/yRQVAaOjr17Bk715W+ek6vA2AEELIMpEiPXuyapt23b1bq83K+pK/80UFAAAA7ff776wGACGVK/euXr8+s/YRQgghDnxHNmkCIZUrs2qfjPnytfmLCwBRm927ISg7m9Ug9KMVClZtI4QQQjyIXOVyZo27ZGXpdF/+kP4XFwCq5S9eQBuGZwPE9+3b0TlfPlbtI4QQQkLq6JwvHxxVKlm1Txvt3avdkpn5pX/viwsAAAA4tHkzq4FAetGiDjW6dWPWPkIIISSgIvd79oT0okVZtU9Gfd2a/FUFgLjl1q0s3wYghQYNYtU2QgghJCTDaYZr2r1Xr8RPv26r/q8qAMKTX72iv+d9v+EvpmzTpvdoZ2dm7SOEEEIC8DlZuTJJcndn1T65vW1bePLXfSD/ulsAACAyqNWsBgQqQgxSf39m7SOEEEICED0PCAAVIcw6KP71a/FXFwAFZ8TEwNaUFGaDGtq/f8AgqZRZ+wghhBBD7lQiIeP9/Jh1MPbZs4I/HDz4tX/9qwuA0DU6HSRv2cJsYFNKlnzZsHNnZu0jhBBCDJUe060bNChVilX71Hnz5tA1Ot3X/v2vLgAAAEjt8HBWAwMAIM/wYUCEEELmiejZrmGk4qZN3/L3v6kAUD04eRIq37jBbHTzO3RQXPvuO2btI4QQQgz4titXDnQeHsw6aHz1qjr17NlvaeKbCgAAALi9YQOzAXqKRLQGPgyIEELIvIi6DRoEGWIxq/bJrrCwb874rQ3odBs3wvDcXGaDDBoypC/Nn59V+wghhJAxdXTOlw86Mbz8Pzw3N8dRpfrWZr65ANBqnz6FI/v3Mxvo3RIlckfg+QAIIYTMQ5EyffrAlJIlmXUwdPdurfbp029t5ttvAQAATVm/ntlAAYBWHjMGgOF7lAghhJBREEK1o0cz7WLRt1/+BzBSAfD41x07oP7jx8wGe7ZWLXlGx47M2kcIIYSMQJno6QmjatRg1kHckyd2m/fsMUZTRikAjpHcXOrL9ioAOT92LMv2EUIIoW9FPRmvVb+uXv0t7/6/zygFAAAA7R8aCvZ6PbNBr2ndWlGkcWNm7SOEEELfwHddw4ZQvUULZh0Mz82VlFm71ljNGa0AiNz/8CFtvXs3s4EDAE0eOZJl+wghhNDXErUdN45l+zQ7OnpTvcREo+U1argfQkJYDp6c6dULNwZCCCFkanxohQrwsEcPln2Itq1aZdT2jNlYtbL794MmIYHZ6FdKJJAxYgSz9hFCCKGvIC44ejSslEiYdeB465bzsq8/+OdDjFoAzCQGAyQtWcJsAgCAVh40qC91cGDZB0IIIZRXCoWjIyzu359pJ2cXL55JDAZjNmnUAgAAQFx7wwaWxwSTUYUK6XKGDmXVPkIIIfRFYn/8EY7Z2TFr3+H5c7vvN240drNGLwDCk1+9oglsnwWgMH48XgVACCHEW1/q4AAt2G78Q1euXBka+vq1sds1egEAACCpv3w5xL95w2oyiL+Dg24PvhGAEEKIr9xqP/0E4OjIrIOg7OzcXsZ9+O8dJgVAeHJyMmgjIphNCADAq9GjZbIiRZj2gRBCCH1EHy8nJ8MJxg+ml92wwRj7/n8IkwIAAIDY//ILRBv3gYV/2G5vL62IuwMihBDiI3flhAlkVKFCzDpQUqqfsHQpq+aZFQCqMn/9BXbG2a/4o06MHOlXvHhxpn0ghBBC/yKTlSxJs4YPZ9rJpJ07o0KuX2fVPLMCAACAPF60iGX7UKFgwdzZ48cz7QMhhBD6F+nuSZPINFtbln3Q4b/8wrJ95kfsKgLPnIEUhnv4x795I1E5Oxtze0SEEELoY3zXlS4tep6QAPEFCrDqg244d05jw/b8G6ZXAAAA6CG2GwOBS/78er8JE1iPAyGEEAIAIM2nTmW5+AMAiMYuWMB8HKw7cKcSSZmOCQlQhOEe/kHZ2eJ8VauGV37wgPV4EEIIWS8fWqGCeNJff8EDGxtmndA7d3S5VatqtQxP2AUAdvsW/+0Yyc2VT/r5Z3KfzXuMAAAQnC9frvf06QADBrAejzlTKCpVAvDwoCF165KCf79CmfToEdl39ap4565dm7az28ERIWQaZN729jY7Onemo2vVIhvLlwcAgOJpaVDk8mXR9YMHw5Pv3uWd0ZSJN82cyXTxBwDyeN487TG2iz+AAFcAAAACBkmlmZk3bgCpVIlZJ9EGA9n7ww8q13PnhBiTOZE3at2a6KdP/+Q51cNzc2l2dLSo2qRJqjJ//cU7M0LIuBSKSpWgxJw5MKh7dwjOl+9jX0f9jx+HbrNna14fOMA7s6lRKBo0gOizZ8FTxOz2OZ334EHumCpVtFtycliPR5ACAABAfsDfn4StX8+0k7WnTqlt3dwAKBVqXKasL82fX3fwt98gzN8/z38pVqcDmD1b/Sg4GOcRIcugLD9+PFXMnv1Fn1z3hofbeQ8ZwmILWvNEiGJlbCycdHNj28/gwWp1aKggIxKiEwAAmUwslh66ehU6VKvGdEC3fX1VZ6KihBqXqVIGFi5M+xw6BIsbNvyav0/dN24s9OegQaFrdDreY0EIfR2ZTCyWzly1CoIHDvyqBvwuXJB2aN16I0lP5z0W3uS2vXsTr/Bwln0I+ekfAEAsRCcAANeuUVonNy0NavXowbSj9B9+aNpp9eq4OOtduAIGSaXZ87ZvJ0HNm39tG+R+vXo5j2rVauqxbVvceYY7OiKEmJDJxGKb2A0b4FG/fl/dyKVSpQyGpk2rU7X62jX296RNVUCArW3O5q1byUF7e6Yd3ZowISr07FmhxsX8NcD3VcnRaKDx1atMO6lUtmzmHuveIjhz9IQJZGi7dt/cUIkePV4GqtXuVML8YVGEkPG4U4lEWjYigrbp3fubG9vu7i6dZd0brmW6TJxIJv79wCQrqffv594KCxNyXIIWADOJwUBuz5jBvKPASZP8bjP+xzJRctsSJWhR4/2wknne3mXORUYGDJJKeY8NIfR5MplYXLZkWBgk+foardHjEyb0rl6qFO+x8eDbrlw52l6AD5X9Z8wQ6tL/O4IWAAAAquW//w4pFy8y7SS+QAH9lrlzhR6bSVAOHmz0wymW9OyZOUijwSIAIdMmk4nF0oYbNxrlk//7jtnZGbZa52vWIqdFi1hv+Qt7//orUcb4BN0PjU3oDgEoJeunT2feTW2FQr6a9dOapoe06NmTScNYBCBk0t4t/hCvVDLpYDSj3y0mTFne1RV6e3sz72jC5MnHSG6u0OMT7C2Af1Pc++MPmNykCcs+6OLz56sWb9RoJrGOh9j8ihcsqPfIzGTaybzNmxPLKZU8vlkRQh/mTiWS0k8jIshYHx9mnSgptdthZ2ctrwVOpyLRrT/PnPnaN6nybPSff6obNW7M47VrDlcA/taG/VUAMrp+/YSW1nPZyuDDcLvldyb26lWm9LZtAQGML4khhPKko3O+fGXORUYyXfwBAFSEZIgF+B1jIm72HjKE+eIPANR98mRee65wKwDUt/ftg6L79rHuhw5fuLDPxTJleI1TSFRlZydIR626dMlccuCATPb3dsIIIS4UCkdHx5xDh2CJMJfniU3hwrzHLATfdaVLw/o5c9j3dPAgzx0X+V0BAAC9bvTotzvPMbTd3j43Z+lSnuMUCr2bliZYZwObNZOuPHHCWt+2QIi33tVLlaLTjhwBN1dXofoks1JTeY9bCKKlv/1G/B0cmHYyPDeXvhw1ius4eXYeFXL9OoxZvZp5R0t69lTMZ7wBkQmQVHn8GMoL+BrJqBo19MdOnFAG1qzJe+wIWROfoTVq6Pf/8QeZ5eIiWKexOp2kyuPHvMfOmrKJjw/U7taNdT/00IoVmmjG++J8BtcCAAAA/pw2DRyeP2feT6+VKxUKR0few2UpPPnVK9rmzBlBOz1YrhxNOXHCGt+4QIgH5clGjcQDjx1jvjHNv9CwP/4IT371ivf4WZLJihShHgJcMfZPTZWeDw7mPV7uBYBanZYGkTNnMu9oSsmSAPPm8R4va6KdW7cK36ujI7TZt09+3NOT9/gRsmSKF1260HFHjsCiYsWE7ls0f8cO3uNnTRrwyy9wt0QJ1v0Q3ylTTOH4de4FAACArk1ICNy6fJl5R8pBg5TRbdrwHi9Loqg1a+BQcrLQ/ZJptrbkxY4dcjpjBgDh9nopQpZKnjFyJEzYvh0qFCwoeOdbU1LyfS/MCXW8KFa2agU533BuQl41vnr10fO1a3mPF4DjPgD/Jm/UujWpcugQ844cb93SJbu4aLVZWbzHzIrSfdAgWobjD6uLSiUdP3DgRvLmDe+5QMjc9aX58+eWXLPG6Lv7fYlbw4apz4WE8J4LVgICbG0z/S5dgpDKlVn3Rbe3a8fzyf/3mcQVAAAAzbnDh6FldDTzjtKqVJGMmzqV93hZUh1bswYubNjALUC8Uqnbe+oUviGA0LfxXVe6tO7+0aNcF/8SkZGWvPgDAGTemDlTkMV/4LZtprL4A5hQAQAAQH3GjhXiKXaiGzfO9+L33/MeL0u62sOGQRDHb7Tw77/Xbzt92nck290eEbJUviObNBGlnTvHesfUT6Fjjh61e2XZm6n5rmvYEH4W4HW8oOxsQ75x43iP930mVQBo7G/dglPLljHvaKVEIiqtUlnybnZabVaWbnq3bgAHD3ILcb50adHgY8cUmwW4r4aQBVHU9fUVlT5yBM6XLs0rA/U/fjx3nqenJW/9GxBgayu6Fx4OK9kfeU5cFi+Ocr19m/eY32dSBQAAgK54cDDMefqUeUejatR4FbVwIe/xsqTVZmXpdF270mABnq34mOB8+WB7WJjSfuXKvjR/ft5zgpAp60vz55dnrFgBtTUaiC9QgFcOOuboUYmyY0ftFsZni3CWmbl4MdyuXp15R3FPnuQbZXon1JrMQ4DvUzbx8aGVIyPZd0QpqeflpSqzcyfvMbMkkxUoIJXu3Ang4cE1yJlr18QvfXzCk69c4T0nCJkan+VVq4p3RUWBU716XIO4njihO2L5i798tZcXObZtmxB90RLe3prFv//Oe8z/ZnJXAAAAVGeiosB3+3b2HRFCn65d27t6qVK8x8ySSVwJAABoUrOmfuzZswpFQADvOUHIlMjv9OkjtomLw8VfGL7rSpcm8WvWCNLZkV27THHxBzDRAgAAwPBs+HAalp7OvKNFxYrpz2zYYOnvrmu1WVmF5plAERBfoADA6tWKIps2ybwFOrwIIRMlkxUooLBdupQEbdwIxzj/PFjJ4g9AiGjI2rWQXrQo867SX7yQxAwZwnvEHyPmHeBjrux8+bLO3ZcvCXTuzLovsrty5Tq/paZeDhN4G12BxcXpdM2ytmzJnvbDD+RwpUpcwzi7uIgP9OxZZ+OJE5e1AjzzgZCJUQbWrEkC9u8nL0xgB02rWfwBlB5jx0Lj4cMF6Sz/iBERw48e5T3mjzHZAgAAoOeluLgU+9atyUUBzqDu0Lp17bCdO6/okpJ4j5uld0VAzj4XF6hYtSrXMM5Fi0JUv351C+h01dv98ce1a3zOxEZISDKZWOyy8KefIDQyktzn95T/O0S7e7ddqW7dwsMte59/AAC/4rVrGwyRkVCS/VP/cCM2Vr1/xAjeY/4Uk7/s7bO8alXxqvh4cBHgCfLGV6/qTjZqZMm7BL4jk4nFkhEhISRk0CDeWQAAIOXiRRjg76/udfEi7ygIsaIMrFmTQlgYpDRuzDsLAADsDQ+36zlgQOgaxseym4C+NH9+XeOzZ6FKnTrMOwvKztYv//77qJDr13mP+1NM+goAAMDVmJSUOscIgdjWrZl3lli8uDi6UKHLur17eY+btWvXKL0StmtX7UsAZGvLlrzzgG3JkhA6YECdLgULNv3h2LG48wYD70gIGYs7lUhcb40bB2MjI2GfAFc082L7smVVMoYO/bWrXs87ihBqZS5bBjbsbykDAMCm6dMjtwnzhsG3MPkrAABvf3jKdDh3TqgnZOlLHx9N9ObNvMctFLl7YCA5v2QJeIpM4qFQOi0+nr7x94+sd+EC7ywIfSu5Z61a8HtYGOnXqBHvLADw9vXnoRMnqh4sWMA7ilDkNeVyUk+tFqSzjpcu2cU2bGgOV1XMogAAAFAUadwYfE6dggwx+6sW7pmZdNcPP2iir17lPW6hKKUKBR0bFgYPbGx4ZwEAgKDsbBo5e3ahJwsWmMMPEkL/JvO2sZH+MWEC9A4KMpmfq1idDpL8/dU6lYp3FKEopXXq0EanTwtyimK0wUBPurlp6p4+zXvceWESn/jyQp169iwttXy5IJ0ds7Mj5bdtk3nb2/Met1BUOrWauHbqRJe8fMk7CwAABOfLRxKCgzNtrl5V1unYkXcchL6EvFHr1lK38+ehxaxZJrP433v1ijh262ZNi39f6uBAc7duFeoIZfrL4sXmsvgDmFEBAABQ6NGUKVD5xg1BOkurUkXis2mTpe8P8D6V56FDoltt2kBFE3oTIq1KFVpnzx5Fm99/96EVKvCOg9Cn+NAKFeSHt24lVQ4dgrO1avHO8z8Vk5LgRuvWqssxMbyjCGU6FYlybSIiQO7sLEiHS65fLxQ3bRrvcX8Js1vcelevX9/gdfq0YFX1vSlT1KdMbw9nlvpcLFNGV3PbNpO5Z/mOS1YWHb9gQW6v+fOt4U0NZD7ebrc9YgS4BwVx39DnX+i0+HhDNS+vKHLvHu8sQpLTGTOIcvp0QToLys425DRtam7PLZldAQAAoFBMmAAwb54gnUUbDKRCly7WVDkD/P3KjFNoKHTw8+Od5T/uPHpE1VOmaCpt2sQ7CkLy456eUHfpUjK0YkXeWf6DarV2hfr1s+QT/T5Ebtu2LekbEyPIM2MAQJJ/+kl1cNEi3uP+4ty8A3yN6VQkunX0wAFYI8CrgQAA/qmpENaokVp95w7vsQuLEEWzSZPg8uzZpvKGwD88O3KEVJ88WbX8jz94R0HWR36paVPImTuX/GoCr9H+W7TBQEdPm6aZNXcugHVtsOVDK1QQF/jzT+jh5CRIh17HjlWRtW49k5jfq8tmWQAAvL1MnftDfLxQ/8h0Wnx8oV+bNbO2ShoAQJ7RqRPZp1bDdlN9KPLgQcP48ePN7fIbMk/KwJo16fMZM6C3tzeoTPAZIffMTFK4Tx+V3PTfQzc2maxAAcmKEyfI6Pr1hekxLc3w3MUlcv/Dh7zH/jVM71NdHm2ql5hIBgl3yAKZ5eKS6RoSwnvcPGjs9+whrk2awN6//uKd5cM8PETN//xTId+8WZ5RpQrvNMgyKa59951CsXo1zb10CYhMZpKLP71zRzy1aVNrXPwBCJFo1q4VbvEHIJ4DBpjr4g9gxgUAAIBq+ZYt4B8WJliH+/r0kU+bMoX3uHlQlfnrL0kLV1eAgwd5Z/kgT5EIiExGal29Kr8ZEuK7jv8e68gy9LlYpozixapV0O7WLYCAAKHuK3+5gwclWY0bhydfucI7CQ9yOn066aNQCNdjaKi5F1qmV8F+Ib/iBQvqj54/D8ECHWyjpBQq9u2rrhkeznvsfBAizxgxgtRauBBaSKW803xU+ZwcCI2K0vv8/LOp78eNTJNCUakSbB85EqYPGvT2GGsTZa/X09+Cg3N7zZ6t1VrHtr7/Jq8pl5OFKpVgV2U0CQn5f6pff/1CE9k35SuZfQEAAKBQNGgAsadPC7Yglc/JoYc6dtScO3yY99h58R3ZpImoVWQkbDbxd/OjDQaw27OH7ps715w26ED8+F78/ntR69GjwUehMN1P+3+78+gRrFcq1TVjY3lH4UU+yN2djN63D4Lz5ROkw1idztCzefPIpeZ/fLxFFAAAAIprQUEQPHu2YB36p6aSms2aqcqY6n1x9vp4OTnlntmwAVp16cI7S56cOHmSqubP1zTftcvanoxGnydf7eZGZkyYYDbfz0EHDtCGfn6a1ya0cZfAfIbWqCHOOHkSwNFRuF4nTlSr58/nPXZjsJgCQCYTiyVr9+whQ9u1E6xTeueO+FDTpuHJycm8x88PIfJLo0cT9c8/m8yWp59z6/Jl6Lx0qfi3yMjwZMs/Ax19nF/xggX1w3x9YffIkYIcE2sM5XNyqGLSJE3dxYutuZCVyUqWlCpPnxb0KqQ2JqZKTpcu5vjK34dYTAEAACCTFSkilZw7B6RSJaH6pBvOnSv0Y8uW1vh64PsUigYNQBMZKdi2m8aQ/uIFOERGGsavWoWvEFqXt58c+/YF/0GDIKxIEd558iz1/n26QC639ttZMlmBAtKYw4eh6w8/CNZp6v37kvwNGmzanpLCe/zGYlEFAABA71UuLnqPU6fINFtbwTptGR2tO9C9u7U+gPOOzNveXnp4+XKT3D3wc3b+8QfdFhpaSBsVZe3FnKXyK16woMHHx4eODQiAyU2a8M7zxfaGh+taBwZqt2Rk8I7C03QqEt08smULWdu9u2Cdxr95I9K7ukbcOH+e9/iNyeIKAAAAuW3v3sRL4Kf0H/3yizp23DjeYzcFihddukDo6tVw3gxfxfPKyKDtIyJIxU2b1Klnz/KOg74VIb4jGzcWb+jTh65RKk13M6tPqP/4MQQMHqwuvGsX7yimQGG7dCl4jRghaKde/v7qXhs28B67sVlkAQAAIL8ZEkJmCLdREID57gfNgszb3l5qs2ABKAcNMskNU/KAznvwgFTfvp0u1mo1g0+c4J0H5Z3cs1Yt2CmTkR8VCkgz482hqFYryRo61JIuO38L+eqJE8mxn38WtNOGK1aoxwQG8h47C2b5izkvAgZJpZn1Dh+Gk25ugnWqpBRmDB+uPmedOwZ+iKJy+/YwZc0aOFiuHO8s34KG3L1Lhm7eTN03bNAMFuhIavRF/IpXrKj36NULlvTtC6Nq1OCd55vEPXkCK4YOVbfdsYN3FFOhSB4+HEatWCFop1VPn9ZdadlSuyUnh/f4WbDYAgAAoHf1UqUMNC4OGpQqJVinSkrh3KBB6hnr1vEev6mQedvbS1MWLYKf+vc316sB/5By8SIR7dypr7pnT7Ul585ZyhPB5kYmE4ttSjZqBAkdOxoWd+tGZrm48M70zZSUQsV163TTfvrJ2u/1v09+p08f4hLGaNOsAAAf8UlEQVQWJuihZBWTkiSyBg021UtM5D1+Vsz/l/Fn+LZr1kx07ehRQXets9fryXmlUnUmKor3+E2JonL79rBo1SqT3zzoSzg8f06X7d9PCuzZQ1fs26cZ/Pw570iWTOZdrJhkT/v28KJjRzKiXTtIL1qUdyZjoSF374qChgxRLd+/n3cWU6KY36MHuEVFwUqJRLBOh+fmgmObNpa+wZLFFwAAAPJLY8aQeQLfm4/V6eBajx744M4/yWQFCthkTpxIW44bZ9Lbq36NaIMBLp87B1djYkTrDx/OFv35p1ablcU7ljmTyQoUkM5s1IjWaN2ajOjYETY0bGiSR1N/C5esLDp+wYLcXvPn4/fLPykqt28P0Tt2CLbL3ztDR45UN1+2jPf4WbOKAgAAQDFao4EkX19BO3XJygK7zp3Vw48c4T1+U6MMLFuWqubONctXBvNqeG4urIyPh+0nT0K3Eyd0uqNHtVuePeMdy5QpAwsXps8bN6ZqNzeidHWFeDc3cMmfn3cuZo7s2iXWjxgRnnz3Lu8opkYZ3aYNnbhrl+D//i4qlXpC7968xy8EqykA+tL8+XVZhw7BwGbNhOyXznr9WjyhQ4eI348f5z0Hpsj3YYcOopdLlwp2mBNvlW/coN+fOkW2nDlDnC5dynly5Yp2S2Ym71g89B9XqFD2m1q1aErdutS7SRNyoVkzuF29Ou9cgtj711/gOHKk+va+fbyjmCJl4A8/0NoHDsAxOztBO3Y6e9Yuu1Ura9kLxGoKAAAA+eqiRcmlU6cEfy3IKyMDtrdpo1bHxfGeA1MUMEgqffnLsGFEPmsWOBQuzDuP4OKePIEXcXHQ6upVGnztGoyMi8vNf+OGJW0s5buudGlStUEDElKzJuytVQscGzSA5OrVLe5y/mfQWa9fQ+WFC9Or/PxzTEJ2Nu88pqj3KhcXQ+yRI8Lu7w9WubW7VRUAAADKxGrV6LVTpwTf/nPss2cGcfv2uOXsx/W5WKZMbuuffwadUmltC8N/xL95Az3u3YNR9+7RlHv3RB537xpc7t0jJ+/d07W+e9fUbiX4FS9eXJ9boQJ1rVCBFKpYkc6oUIE4VagAVSpWhFLffWfRl/HzItpggBUREYbcSZMiBzx+zDuOqepdvX59Q+t9+wR/uNPh+XP6c7NmGvtbt3jPgZCsrgAAAOjds3lzw+wDB4R+sISGpadTz86dI/efOsV7DkyZX/HatfUlg4OhdrduvLOYKjrr9WtS5fFjuJuSAhEpKfBrSgodlZJCvFNSoG9KCjg/fw6QlgZTc3Ig+O2BR/qmL1+KT+fmimfn5uqn/usc86DChSFYLNY3lUjEpwsVevtnBQvCbBsbAEdHSChaFDY6OdEtTk5kiZMTjHFygt5OTlDRyYneKl1a0K23zYmSUtDv2EHXBAVpoq9e5R3HlClPNmpEX+/dK/iHs/g3b8hLDw/Vg5Mnec+B0KyyAAAAkHv26kWGREYK/l76vVev4DsvL7Xm4EHec2DqFEUaN4YOc+YAeHjwzoLQFztx8qSo0aRJ+PzP58kHubvDnOhoMurv4lMoSkrBy89PrVOpeM8BD1Z7mVUTvXkzVJw2TfCOKxQsCFN37ZKv9vLiPQemTp169qxa3bYtdW/enPrjL1FkJpzOniVyDw/1Azc3XPw/z/dw587EOSZG8MUfAOijyZOtdfEHsOIrAO/wODMAAADK5+SQV0qlavmWLbznwFwo5B4ekLpwITjVq8c7C0L/cebaNWg8Y4Zas2ULAKW845gDuWevXuRCRISgG7W982TdOvWRgQN5zwFPVnsF4J1CC0eMoCEcdt56YGNDcyMj5Qf8/XnPgblQaw4erLK3QQM6tGtXWIvPUSDTQBefP0+D+/bV1a9bV63RanHxzxu5be/eZKJKxWXxL7pvX+JhDh/8TIzVXwEAePs+ctaA48e57CWupBQKjxplDbtOGZt8tZsbmTFhAqzt3NkizhhA5kNJKagOHaJDly3TNI+O5h3H3CgaDR0KN1as4PK2T+OrV3UnXF3xrAUsAP6nz8UyZXLnx8YCqVRJ8M6VlMKp8ePVc375hfc8mCN5RpUqpNSPP0LFgACrf90MsRWr08HryEjqOn8+PtX/dbgc6fvO0Nu3DTdbtMBXMd/CAuA9frfLl9cPj42FIt99xyXA9mXLqrwaPRpPl/s6ysCyZali9GhaedAgHg8UIctFl7x8CdtCQ0VXlixRLX/0iHcecySTicXS3b/+Cl4jRnAJcOfRI/GdFi1w2+X/hwXAv8gzqlQhqbGxMKVkSR7904HbthWK7N3bWraiZKH/uEKF3iTK5XTx4MFkdP36vPMgM3bm2jVosmkTQGioWp2WxjuOufIrXrCgvqRKxW1vj7HPnunXurtHhVy/znsuTAkWAB+glNapQ/ccPSr4hhTvOJ09S9d17ap5nZTEey7MnULRoAFAQAC4KxSC7yuOzFP8mzdQNzoaIDQU9+v4djJZyZIS1c6dpF+jRjz6p2Hp6fR669a4C+t/YQHwEYoijRvTGwcP8rqUTEPu3jVM7NwZK1bjeHdVAPyGDIHw77/nnQeZoL8/7et0a9ZotampvONYAmVgzZq0ze7dsLlCBS4B0l+8IFM8PFSu587xngtThAXAJ/i2a9ZMlLl/P1QoWJBPgrQ0ourRQ0WOHuU9F5ZEUaRxY/qHvz9Z4u0t+J7jyLSMffYM1Fu2kB5hYbhIGJe8UevWcOL334m/gwOXAC5ZWTShY0fNmmPHeM+FqcIC4DPktm3bkoo7d3J7urx8Tg5dNmCA5nVEBO+5sDQymVgs8WjalIyWyaCfQoHFgHWgYenppFR0NN2h1RbatHdv6BqdjncmS6Nc37cvvRkaCg9sbLgEKJ+TQyd1766x37OH91yYMiwA8kBxoFs3sN2yBVZKJFwCKCmlnWbN0pCZM3GTETY6OufL5xDWrh3pJpPRG15e+BaBhXHJyoIlhw5By02b0s7t3IlH8bJCiJxOn072TJvGbW8Oe70e0uXyt5syoU/BAiCPFFKlEvJv2sT1mNq94eG6NoMHa7VZWbznw5IFBNjavnzSpQuE9uhBxrZrJ/i55Mg4/FNTYc/+/fB061Zd7q5d+HPDVkCAre3L39asIX0UCm4hog0GeNOnjzXv7/8lsAD4Aoq6vr6QumkTl60r30m5eBGcevZUq+/c4T0f1kAmE4ul0nr1ADw84ISnJ6Q1bcq1CESfdubaNWgSHQ304EE7u2PH8PK+MPxuly+vT/39d1jcsCG3EPZ6PWk0YICq/8aNvOfDXGAB8IUUL7p0gaZaLdcd5/xTUw3VlcrIcnv38p4PayPzLlZMKm3ZEvZ5ekKHLl3w6gBn7pmZMOPoUWgVHS0Zv3v3pnqJibwjWRt5RqdOZGhEBNefhfI5OeAol6snbN3Kez7MCRYAX0GxslUrkOzcyfW9ciWloFqwoIpq8mTcOZAPdyqRlGrfuDEZ6eoKr93cyJFmzfBBQsbGPnsG/qdOgeeJE9Tn5Mnc2WfParV6Pe9Y1okQhWL8eIieO5fnVTE66/Vr0r5HD/Xtfft4z4i5wQLgK/Xu2by5IWvXLnAoXJhnDqLdvVuS07v3RpKezntOEIBCUakSDXZzI0GurnDGzQ2W1aiBBxV9g7gnT6D+iRN01cmTZOiJE2r1+fP4ICx/ysDChQ3dN2wga7t35xrEPTOTru3WTXPu8GHec2KO8BfTN1AoGjSArfv2QQ8nJ65BNAkJRNKjh0p3+TLvOUH/JLctUQIWN20KAfXqEY86dWCGiwt0qlgRnyP4l2iDATLv3IHWly7Rg5cvQ+jFi5Kpp06FJycn846G/kluW7cu2bd1K4RUrsw3SVoacerUSbX8jz94z4m5wgLgGykDa9ak3x04AOdLl+YaxD0zk6wfOFB1JiqK95ygT5N529hIsqtUgaUNGpCgmjXhSK1a0L9RI7hbogTvbILwysiAn65cAberV2nItWsQGReXe+DiRe2WzEze0dCnyWvK5cRhzRp+m6P9rWJSkqhc+/YRQ+Ljec+JOcMCwAjkq6tXJ2EHDkClsmV5ZyGXly+XXBo/fiN584Z3FvRlFApHR4BKlYBWqkRXlS5NhpYqBbRSJSCVKoF7tWpmc5ZB+ZwcuP/oEZA7dwDu3KGqJ0+I8vFjoHfuALlzR62+excv45sXmaxAAWn5RYvgydChvLOAx8OH+lceHlGBN2/yjmLusAAwEsW1776DWgcPgtzZmXcWOHPtGvysVKp7XbzIOwoyFkJ815UqJZ5ZsSKcdnKCeUWLGjo4OZF6xYrRM05OZIaTEwx2cgKZkxPUcXKCHk5OkCEWG6Vre70etqakwOWUFNCmpMDqlBQ6IyUFxM+fk7PPntGWKSmimikpev/nz8Hn3j08a92yyD1r1SK91GqIqVuXdxbode+evpiHR5Tr7du8o1gCLACMqM/FMmVyB8XEQJU6dXhngfg3b+ipiRM19suW4act6yXztrGR2ry9XGt4bmcnKiqV6ocTIl75z/3Z9cPT08UrKTU81+lERd9eitflvHql3ZKTw3sMiBdClB5jxtBlc+ZAcL58vNNAx0uXDDkdO2KBaTxYABiZzNvOTlovKgquderEO8tbBw8a2vTtiz80CKG8ktuWKEF0YWEg69iRdxYAAAg6cEA3TSbTbsnI4B3FkhjnEiH6n2vXcnKKHNq8uVDd4sXJUY67Yv1PpUokrW/fOnNv3rwc/tdfvNMghEybfLWXFym1dy9UdnHhnQUAgHqvX18oXKEIj3j9mncWS4NXABiSZ4wcScr++qvJvPK1NzxcLBk6NDz51SveURBCpkUmK1BAunvePPAaMYJ3FgB47xC0GTN4R7FUWAAwJq8pl5MtYWEmcQ8NAGDJ9euiFr17R9w4f553FISQafBd17ChaFxEBHSoVo13FgAAiH/zhtj164evNbOFBYAA5JeaNiW6HTtgUbFivLMAAMDw3Fxo+9tvYrvJk/FqAELWSyYrUECyecIEUm7yZK6HnL3PPzUVynTvrq4ZG8s7iqXDAkAgPicrVxY77tkDwVWr8s7yP/TOHYDBg9Wagwd5R0EICUtxrUULcAsNNZlP/QAA9M4d8kunTqoy+LySEEzj3rQViHK9fVsyuVkz6n/8OO8s/0MqVYLe+/crimza1MeL83bGCCFB9KUODgrF6tVw9+hRk1r8d/7xh/hQ06a4+AsHrwAITCYrUEBadv16SPL15Z3lH+KePKGdAgM1i3//nXcUhBAbcs9evUjdZctMbttpF5VKOn7gQNzBVFhYAHCiUAQEQOyKFSZz3+2dI7t2GeoMGxa5/+FD3lEQQsbRu3qpUoYyK1ZAiR49eGf5h+G5ubAyKEitnj+fdxRrhAUAR/JB7u6keFSUyVXjXhkZsH38+CqqtWtnEoOBdxyE0NeZTkWim72HDCFpP//M++jy/5jz9Knop169In43oduiVgafAeBIs+bYMUOl+vWh6unTvLP8w3Z7e4DVq28mnzvXu2fz5rzjIIS+nO/IJk1uFTp5ktCVK01u8T9x8qThcIMGuPjzhVcATIA7lUjKKIODASZM4J3lg47s2qV/HBgYRe7d4x0FIfRpysCyZalq7lwI790bVMQEf8eHhupyAgPxnAn+TPCbw3oppEolvR4aSqbZ2vLO8h8uWVkQv2yZLic4GM9tR8j0BATY2mZmBgaCe1CQSR4dHf/mDWk9fLhq+fr1vKOgt7AAMDGKzfXqwbbffwdSqRLvLB9CJyYmgt3kyZpK4eF4yiBCpoAQhdzbG9IWLoQi333HO82H0HkPHogeenurXM+d450F/T8sAEyQTFakiLSUWg3P27fnneVj6IZz5+DGyJGauib2/AJCVkShaNAATixdCm6urryzfJQ2Jkbn1bu3VpuayjsK+ic8DdAEXbuWlVW9vEYjfpidDZdatIDvxCb370S2lylDJvj716lcsWKd2IsXL6/EYzoREooPrVDBpdLSpfDDihWQUb487zwfVD4nBzKmTKly8ccff+uFJ/mZIrwCYOJ81zVsKGqqUpnUFsL/FqvTQYuwMMn4WbM21UtM5B0HIUulDCxblq4bNw7+HDzYZA4Y+wAacvcu3Fcq8QqhacMCwAyY3DGdH1M+JwcebNggOj9jRsSNJ094x0HIUsi8ixWT2owdCy4jRkB8gQK883zS3vBwXethw/BhYdOHBYAZUWq6d6f+a9ZADxPft//eq1dwcd06CnPnal4nJfGOg5C56uPl5KTbFhgIz8aMIaMKFeKd51NoWHo6aTR0qPpSZCTvLChvsAAwM3LbEiXIww0bILBDB95ZPss9MxOOrVwpVc2bt5Gkp/OOg5C56D+uUKE3icOGgdekSW835jJxgw4fljj26YO3AM0LFgBmaDoViRK+++knqpg9Gx7Y2PDO83lpadR50SKbmStXYiGA0McpFI6OEPvjj/T2mDHE38GBd57PKp+TA+IpU6oE//orbhtufrAAMGN+xWvX1n+nVkOVOnV4Z8kT98xMOKZW05BfftHY37rFOw5CpsKHVqggVg4ZAl5DhpjFJ34AgMo3boi0SmXEjfPneUdBX8fkXi9DeXfpVXJy0y4bN+Yct7WFWo0bQ7bItM92uG9jA9CgAakwdGgd+5o161S6d+/yZXxYEFkv33UNG9ZZ/euvovGrVkF28+ZwI39+3pk+y16vh4AlS+xO+PqG7XvwgHcc9PXwCoCFUGyuVw+2r10L0KAB7yxf5MTJk+C6dKkud+tWrVav5x0HIfYIUcjbtIGjI0dCqy5deKf5IrcuX4bbAweqU8+e5R0FfTssACzI20OFxo6F+BkzwMUMPkm8T5OQQNNXrMgdGBqq1WZl8Y6DkLHJvG1sJAt8fcnO8ePhbK1avPN8kfg3b+iV+fNzZXPn4iE+lgMLAAskX129OlQKDSVhZniU75ynT8mw0FDRinXrwivj5UVk/hTXvvsOjgwYAI8DAuBuiRK883yxG7Gxev9Bg6ICb97kHQUZFxYAFosQ+R0/P3J78WIIK1KEd5ovFm0wgOfhw0BDQ+3stm8PXaPT8Y6EUF7JZGKxVNKqFUBAAPzYvTuslEh4Z/piXhkZtP306VULL1+OT/hbJiwALFzv6qVKGTYtXw5LevbkneWrVUxKgsVRUbRNaKgm+upV3nEQ+hh5RpUqZOiAAVCxXz+z/LT/zpFduwx1hg2L3P/wIe8oiB0sAKyEQi6Twdxly2BKyZK8s3w1JaVge+QIjFyzJu31tm0xCdnZvCMh1Jfmz59r06MHbTlwIIxq2RJUxHx/r8Y9eUI7BQZqFv/+O+8oiD3z/UZFX0zmbW8vqTtjBmnz449meUnyfVtTUujk8HBRG7UazxhHPCiKNG4MqxQKcPTzM8vbbO8Lys6G4CVL8peZM2f9wpcvecdBwsACwAopE6tVo/OXLoXn7dvzzmIMdN6DB6T69u10sVarGXziBO88yHLJPWvVgp0yGflRoYC0KlV45zGKI7t26beMGhXlevs27yhIWFgAWDH5cU9PkrJsGWyuUIF3FqPpde8eKHbuxGIAGcv/Fv1pPj5wu3p13nmMpvKNG2T7mDGqyzExvKMgPrAAsHIBAba2mZmBgZA+eTI4FC7MO49Rnbl2jSZotRAaGakZfOMG7zjIfPgVr1gx91bXrvCmTx8yun593nmMyj81lTaeNetx4ZUrj5HcXN5xED9YACAA+PttgVKzZ0P1fv0gQ2x5W0R3vHSJVtu9m9jt3ZtY49Qp/MWH3udOJZLSAa6uog0dOtB6nTubzfkaXyJWp4Nff/sNts+cqVanpfGOg/jDAgD9g8/QGjUkaxcupLLOnXlnYebeq1dw98gRaBUdbXgeE4OvOlknuW2JEnClfXsypUsXuqFtW7M4fe+rHTwoPjh6dHjylSu8kyDTgQUA+iBF5fbtocmcOWZ3tsDX6HjpEsTExBDV3r0FA06exE2HLFPAIKk0s56bGy3boQMJ7tjRIj/l/9voP/+k7pMna14fOMA7CjI9WACgTyBEIff2hn2zZ0OHatV4pxFE+osXMOTwYXLg2DGqOvV/7d1rUFTnHcfx/3P2AguCu5wslY0QLmuCUN0KKgooiUqCRAVT1xrxtnZKqi2tjtMOM1UY03Sq0QiOncHQkasSCsZIcQgEsBqBCBlihSyaAosgikKPLGtgYYF9+iKN4zTpGC/w7C7/zwwz8Ib5vmDgx+45z6mb9tMrV3AQ2KfEX0gkJpeQEE4THg7pUVG0atkyssvNjXXXpEi/do3+ed++D9LOnAGglHUOsk04ANAjRVGx+Pn9W7eCT2oqVHl7s+6ZTPTtoSEwNjTAjJoabmVdneWlurri0wMDrLvQd22lcrnFFB7OLQgPp+9ERtKQBQtIiosL665Jda+zk+7Zv38sMy8Pn66JHgUHAPrBHjzN7A+pqUD8/Vn3MEMNBqiorYWYmhp6v7YWjydmY8MJlYqrioiAkshIiI+IgNJ582A1x7HuYoEe6OqC6UeOSN3ffz+XDA+z7kH2AQcAemwPhsCtlBTICAhg3cMazTYaySy9HiL1eprR0gKFjY3ifVeu5PcODrJucwTadVKpeGTWLDgaGkr2BgXBP4KDYfuCBXZ91v4zgn/40dPAAYCemHadVCqepdORsORkhzpM6FkotVphS3s7fevqVTjW3Mwta24e82lq+luEwYDvyX6/VMpx1+v8/EQ1Gg38eM4cUM6ZA7lz50JeQMBU/c/+/6IGA7l94IBFmZtbfNpiYZ2D7BMOAPTUUinH/avm9deJc0oKpM2fz7rHpu0dGYE/3roFxGAA+O8HNRiAGAzW5T09hT+/fZt14kTSamUyicTLC6i//zdvI/n7f/s5TX/xxSlzkd4TomlffAGDR4/e9isowLMs0NPCAYCeIULeHFi5EhqSk0n2kiWsa+wRzTYayfmODup04waRdXTAte5u+rIg0BuCIPpIEKzvCILUva8vlxiNrFsf9s0FeEolt5fnx9fyPPHleXKB5+FP3t70r76+5KCfH/27r69j32s/QUqtVpp17pyo8PDhkx9eusQ6BzkOHABoQmx4NTycHN69m2SsXeuQJwuy9quxMSgTBCgUBHpcEIhZECBNEOguQSC1Y2PQbTIRfmSECkNDUDw4COseepm4wWSChf9zhXiDSAQLHzoK+rRUClpXV8K7uFDByQlmurvDbyUS2OThAbt5nsp4nvyS52EDz0Msz9v90yVt0dXhYdDk5ZFDR46cev6rr1jnIMeDAwBNqA0nVCqyPTGRVCUl2f0jUxGaDPEDA7AtN9d67OBBR39LCLGFAwBNiu2/c3MbXqPTwdKkJHhTrWbdg5DNUbS2UmN6uptrTk5m5tAQ6xzk+HAAoEmVSjmudeOyZQCJiXApPh6WSiSsmxBiptRqhdXnzwPNzBwdO3MGD+9BkwkHAGJmU6CXlzVkyxZ6YOdOkuzjw7oHoUnT2NMDoXl5sDcjoyCos5N1DpqacAAg5qKoWKzKXLUKPHQ6ooqNxQvKkEP6dHQUPMrKyMdZWd2qsjK8jQ+xhgMA2RSt1sNDIlm3jqbs3Ene1mhY9yD01AKuX4f2nBx6Nifng6G7d1nnIPQtHADIZiUkLVoEeZs304T168H43HOsexD6wfb09VF1URFNyc8vPFpfzzoHoe+DAwDZPK1WJJKIX3kFKrZsodfj4/G0OGSTNGYz/PPcObozP98tr7wcHyONbB0OAGRXNnu6ulr74+Lo7PXrYfy110Dj7My6CU1hGrMZZldU0OSiIrfIkhK8fQ/ZExwAyG5ptTKZ+DcrVpA4rRZfGUCTRmM2Q3p1Na0tLubSzp49dcxkYp2E0JPAAYAcglYrk0nfiImxmuLiyPzYWHhPqWTdhBxIdW8veJSV0V0lJWNVFRXFxWYz6ySEnhYOAORwUinHtSbMm0dPrV5NElatgoSQEDhF8GcdPZ76lhYIKy0FWlV1q+DCBbxtDzka/KWIHN7mdh+f8X0xMTQ5OpocWL4cQKFg3YRskO7ePciqria3KyvHncrLCz+5eZN1EkITCQcAmlK0WpFIpJo/n3srOho2RUdD7+LFeBzxFOVjsUDIZ5/RLysryV8qK0eXNzbiUbxoKsEBgKa0xEQXl/uhISHkYkQEwIoVoImIgKsyGesuNAE+HR2FpU1NAFVVdEdtLVd08SJewIemMhwACD1kpdrJidcsXGjtWbIEti1aBPvCwmC5pyfrLvQE/O7epT+prydrLl+mOZcujVU2NBSftlhYZyFkK3AAIPQIGzf6+0Px4sUkMCyMvhwWBufnzsXzB2yMxmyGnzU1QVB9PRmtr6fay5cLCgwG1lkI2TIcAAg9Jq1WJBIPBwaSaUFBtCA4mKhCQ+FcWBjeejhJjCYTfNncDP9ubIR4vZ5GtbQYD33++cdtIyOs0xCyJzgAEHpGNm5UKGhUcDC5GBQEZ4ODIT4oCPZoNDgMnpDRZAJ5ayuUt7RAjF5Pd7S0kAy9vqCgowOAUtZ5CNk7HAAITbANJ1QqbrFaTTLUaioLCIBfq9XUSa0Gub8/0cnlrPtYotlGI9G1t9P32tpIWns7jW5rEx1vawN9W9vJ6z09rPsQcmQ4ABBiKCHJ3d16w9sbTr7wAtkxcyZ919ubVPv40PgZM8gKLy8oUirhXaUSBkQi1q2PZfr4OPy+rw9qenvpmjt3yNk7d+Drzk6o7e4mzTdvWn27umSBXV1Zh+7fZ52K0FSFAwAhm0fIZk+lEsDTczxVqYSvFQowKxSkRi635sjl4KZQkONyOXwik8GP3N2pzsmJZLu4QICrK7RJpfDR9OnwBsc9+HYaZ+fv3OqoMZvh6vDwg6/PWK2wdmAA1BYLtA8OUt3QEMkeGYG7JhO8ajaDpL+frjcauW1GI8zu76czjUaY1t9Py3t7xfV9ffm9fX34Mj1Ctu0/aL1Qdm53Fq0AAAAASUVORK5CYII='
        />
      </defs>
    </svg>
  ),
  checked: (props: IconProps) => (
    <svg
      width='30'
      height='30'
      viewBox='0 0 30 30'
      fill='none'
      {...props}
      xmlns='http://www.w3.org/2000/svg'
    >
      <g filter='url(#filter0_d_9133_59896)'>
        <rect
          x='3'
          y='2'
          width='24'
          height='24'
          rx='8'
          fill='url(#paint0_linear_9133_59896)'
        />
        <g clip-path='url(#clip0_9133_59896)'>
          <g filter='url(#filter1_d_9133_59896)'>
            <path
              fill-rule='evenodd'
              clip-rule='evenodd'
              d='M20.3851 7.49657C20.9392 6.8808 21.8877 6.83088 22.5034 7.38507C23.1192 7.93926 23.1691 8.88769 22.6149 9.50346L13.6149 19.5035C13.04 20.1422 12.047 20.1684 11.4393 19.5607L7.43934 15.5607C6.85355 14.9749 6.85355 14.0251 7.43934 13.4394C8.02513 12.8536 8.97487 12.8536 9.56066 13.4394L12.4427 16.3214L20.3851 7.49657Z'
              fill='white'
            />
          </g>
        </g>
      </g>
      <defs>
        <filter
          id='filter0_d_9133_59896'
          x='0'
          y='0'
          width='30'
          height='30'
          filterUnits='userSpaceOnUse'
          color-interpolation-filters='sRGB'
        >
          <feFlood flood-opacity='0' result='BackgroundImageFix' />
          <feColorMatrix
            in='SourceAlpha'
            type='matrix'
            values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0'
            result='hardAlpha'
          />
          <feOffset dy='1' />
          <feGaussianBlur stdDeviation='1.5' />
          <feColorMatrix
            type='matrix'
            values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0'
          />
          <feBlend
            mode='normal'
            in2='BackgroundImageFix'
            result='effect1_dropShadow_9133_59896'
          />
          <feBlend
            mode='normal'
            in='SourceGraphic'
            in2='effect1_dropShadow_9133_59896'
            result='shape'
          />
        </filter>
        <filter
          id='filter1_d_9133_59896'
          x='7'
          y='7'
          width='16'
          height='13.75'
          filterUnits='userSpaceOnUse'
          color-interpolation-filters='sRGB'
        >
          <feFlood flood-opacity='0' result='BackgroundImageFix' />
          <feColorMatrix
            in='SourceAlpha'
            type='matrix'
            values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0'
            result='hardAlpha'
          />
          <feOffset dy='0.75' />
          <feComposite in2='hardAlpha' operator='out' />
          <feColorMatrix
            type='matrix'
            values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0'
          />
          <feBlend
            mode='normal'
            in2='BackgroundImageFix'
            result='effect1_dropShadow_9133_59896'
          />
          <feBlend
            mode='normal'
            in='SourceGraphic'
            in2='effect1_dropShadow_9133_59896'
            result='shape'
          />
        </filter>
        <linearGradient
          id='paint0_linear_9133_59896'
          x1='18.84'
          y1='32.439'
          x2='-12.3205'
          y2='16.351'
          gradientUnits='userSpaceOnUse'
        >
          <stop stop-color='#6600CC' />
          <stop offset='0.8' stop-color='#B098FF' />
        </linearGradient>
        <clipPath id='clip0_9133_59896'>
          <rect
            width='16'
            height='13'
            fill='white'
            transform='translate(7 7)'
          />
        </clipPath>
      </defs>
    </svg>
  ),
  notChecked: (props: IconProps) => (
    <svg
      width='30'
      height='30'
      viewBox='0 0 30 30'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <g filter='url(#filter0_d_9133_59900)'>
        <rect x='3' y='2' width='24' height='24' rx='8' fill='#F4F4F5' />
        <rect
          x='4'
          y='3'
          width='22'
          height='22'
          rx='7'
          stroke='#A1A1AA'
          stroke-width='2'
        />
      </g>
      <defs>
        <filter
          id='filter0_d_9133_59900'
          x='0'
          y='0'
          width='30'
          height='30'
          filterUnits='userSpaceOnUse'
          color-interpolation-filters='sRGB'
        >
          <feFlood flood-opacity='0' result='BackgroundImageFix' />
          <feColorMatrix
            in='SourceAlpha'
            type='matrix'
            values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0'
            result='hardAlpha'
          />
          <feOffset dy='1' />
          <feGaussianBlur stdDeviation='1.5' />
          <feColorMatrix
            type='matrix'
            values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0'
          />
          <feBlend
            mode='normal'
            in2='BackgroundImageFix'
            result='effect1_dropShadow_9133_59900'
          />
          <feBlend
            mode='normal'
            in='SourceGraphic'
            in2='effect1_dropShadow_9133_59900'
            result='shape'
          />
        </filter>
      </defs>
    </svg>
  ),
  heart: (props: IconProps) => (
    <svg
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
      {...props}
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M20.8301 11.4651C19.6571 17.5151 12.0001 21.0001 12.0001 21.0001C12.0001 21.0001 4.34304 17.5151 3.16905 11.4651C2.44904 7.75308 4.0221 4.01906 8.0211 4.00006C11.0001 3.98606 12.0001 6.98803 12.0001 6.98803C12.0001 6.98803 13.0001 3.98506 15.9781 4.00006C19.9861 4.01906 21.5501 7.75408 20.8301 11.4651Z'
        fill='#EF3737'
      />
    </svg>
  ),
  heartLine: (props: IconProps) => (
    <svg
      width='14'
      height='12'
      viewBox='0 0 14 12'
      fill='none'
      {...props}
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M12.8869 5.30982C12.1049 9.34323 7.00018 11.6666 7.00018 11.6666C7.00018 11.6666 1.89538 9.34323 1.1127 5.30982C0.63269 2.83511 1.68141 0.345712 4.34746 0.333045C6.3335 0.323712 7.00018 2.32507 7.00018 2.32507C7.00018 2.32507 7.66688 0.323045 9.65225 0.333045C12.3243 0.345712 13.3669 2.83578 12.8869 5.30982Z'
        stroke='white'
        strokeWidth='0.5'
      />
    </svg>
  ),
  down: (props: IconProps) => (
    <svg
      width='12'
      height='8'
      viewBox='0 0 12 8'
      fill='none'
      {...props}
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M4.69774 7.396L0.317854 2.2019C-0.418146 1.3289 0.205789 0 1.35179 0H10.6477C11.7937 0 12.4176 1.3299 11.6816 2.2019L7.30174 7.396C6.62274 8.201 5.37674 8.201 4.69774 7.396Z'
        fill='#18181B'
      />
    </svg>
  ),
  arrow_back: (props: IconProps) => (
    <svg
      width='18'
      height='24'
      viewBox='0 0 18 24'
      fill='none'
      {...props}
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M2 11.939C2 12.3208 2.146 12.6465 2.44922 12.9385L11.209 21.5073C11.4448 21.7544 11.7593 21.8779 12.1187 21.8779C12.8486 21.8779 13.4214 21.3164 13.4214 20.5752C13.4214 20.2158 13.2754 19.8901 13.0283 19.6431L5.1333 11.939L13.0283 4.23486C13.2754 3.97656 13.4214 3.65088 13.4214 3.2915C13.4214 2.56152 12.8486 2 12.1187 2C11.7593 2 11.4448 2.12354 11.209 2.37061L2.44922 10.9395C2.146 11.2314 2.01123 11.5571 2 11.939Z'
        fill='#18181B'
      />
    </svg>
  ),
  share: (props: IconProps) => (
    <svg
      width='17'
      height='21'
      viewBox='0 0 17 21'
      fill='none'
      {...props}
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M12.25 7.75H14.125C14.6223 7.75 15.0992 7.94754 15.4508 8.29917C15.8025 8.65081 16 9.12772 16 9.625V18.625C16 19.1223 15.8025 19.5992 15.4508 19.9508C15.0992 20.3025 14.6223 20.5 14.125 20.5H2.875C2.37772 20.5 1.90081 20.3025 1.54917 19.9508C1.19754 19.5992 1 19.1223 1 18.625V9.625C1 9.12772 1.19754 8.65081 1.54917 8.29917C1.90081 7.94754 2.37772 7.75 2.875 7.75H4.75M12.25 4.75L8.5 1M8.5 1L4.75 4.75M8.5 1V13.7969'
        stroke='#121212'
        stroke-linecap='round'
        stroke-linejoin='round'
      />
    </svg>
  ),
  download: (props: IconProps) => (
    <svg
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
      {...props}
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M4.125 21C3.42881 21 2.76113 20.7234 2.26884 20.2312C1.77656 19.7389 1.5 19.0712 1.5 18.375V14.625C1.5 14.3266 1.61853 14.0405 1.8295 13.8295C2.04048 13.6185 2.32663 13.5 2.625 13.5C2.92337 13.5 3.20952 13.6185 3.4205 13.8295C3.63147 14.0405 3.75 14.3266 3.75 14.625V18.375C3.75 18.582 3.918 18.75 4.125 18.75H19.875C19.9745 18.75 20.0698 18.7105 20.1402 18.6402C20.2105 18.5698 20.25 18.4745 20.25 18.375V14.625C20.25 14.3266 20.3685 14.0405 20.5795 13.8295C20.7905 13.6185 21.0766 13.5 21.375 13.5C21.6734 13.5 21.9595 13.6185 22.1705 13.8295C22.3815 14.0405 22.5 14.3266 22.5 14.625V18.375C22.5 19.0712 22.2234 19.7389 21.7312 20.2312C21.2389 20.7234 20.5712 21 19.875 21H4.125Z'
        fill='url(#paint0_linear_9091_48643)'
      />
      <path
        d='M10.8743 11.5335V3C10.8743 2.70163 10.9928 2.41548 11.2038 2.2045C11.4148 1.99353 11.7009 1.875 11.9993 1.875C12.2977 1.875 12.5838 1.99353 12.7948 2.2045C13.0058 2.41548 13.1243 2.70163 13.1243 3V11.5335L16.0793 8.58C16.1837 8.4756 16.3076 8.39278 16.444 8.33628C16.5805 8.27978 16.7267 8.2507 16.8743 8.2507C17.0219 8.2507 17.1681 8.27978 17.3046 8.33628C17.441 8.39278 17.5649 8.4756 17.6693 8.58C17.7737 8.6844 17.8565 8.80834 17.913 8.94475C17.9695 9.08116 17.9986 9.22736 17.9986 9.375C17.9986 9.52264 17.9695 9.66884 17.913 9.80525C17.8565 9.94166 17.7737 10.0656 17.6693 10.17L12.7943 15.045C12.69 15.1495 12.566 15.2324 12.4296 15.289C12.2932 15.3455 12.147 15.3746 11.9993 15.3746C11.8516 15.3746 11.7054 15.3455 11.569 15.289C11.4326 15.2324 11.3086 15.1495 11.2043 15.045L6.3293 10.17C6.2249 10.0656 6.14208 9.94166 6.08558 9.80525C6.02908 9.66884 6 9.52264 6 9.375C6 9.22736 6.02908 9.08116 6.08558 8.94475C6.14208 8.80834 6.2249 8.6844 6.3293 8.58C6.4337 8.4756 6.55764 8.39278 6.69405 8.33628C6.83046 8.27978 6.97665 8.2507 7.1243 8.2507C7.27194 8.2507 7.41814 8.27978 7.55455 8.33628C7.69096 8.39278 7.8149 8.4756 7.9193 8.58L10.8743 11.5335Z'
        fill='url(#paint1_linear_9091_48643)'
      />
      <defs>
        <linearGradient
          id='paint0_linear_9091_48643'
          x1='15.36'
          y1='23.0122'
          x2='4.1836'
          y2='6.85526'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='#664FB0' />
          <stop offset='1' stopColor='#A992F5' />
        </linearGradient>
        <linearGradient
          id='paint1_linear_9091_48643'
          x1='13.9191'
          y1='18.9965'
          x2='-2.37976'
          y2='11.5171'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='#664FB0' />
          <stop offset='1' stopColor='#A992F5' />
        </linearGradient>
      </defs>
    </svg>
  ),
  menuClose: (props: IconProps) => (
    <svg
      width='18'
      height='10'
      viewBox='0 0 18 10'
      fill='none'
      {...props}
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M16.3366 1.66699L9.00326 9.00033L1.66992 1.66699'
        stroke='#3F3F46'
        strokeLinecap='square'
      />
    </svg>
  ),
  menuOpen: (props: IconProps) => (
    <svg
      width='18'
      height='10'
      viewBox='0 0 18 10'
      fill='none'
      {...props}
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M1.66927 9L9.0026 1.66667L16.3359 9'
        stroke='#3F3F46'
        strokeLinecap='square'
      />
    </svg>
  ),
  freeIcon: (props: IconProps) => (
    <svg
      width='12'
      height='12'
      viewBox='0 0 12 12'
      fill='none'
      {...props}
      xmlns='http://www.w3.org/2000/svg'
    >
      <g clipPath='url(#clip0_9110_58809)'>
        <path
          d='M8.57927 3.42184C8.38429 3.2268 8.27013 2.96534 8.25961 2.68976C8.24909 2.41417 8.343 2.14477 8.52255 1.93544C8.5374 1.91803 8.54512 1.89565 8.54416 1.87279C8.54321 1.84992 8.53364 1.82827 8.51739 1.81216L7.48309 0.776689C7.466 0.7596 7.44281 0.75 7.41864 0.75C7.39447 0.75 7.37129 0.7596 7.35419 0.776689L5.7056 2.42528C5.64475 2.48608 5.59893 2.56023 5.57177 2.64184C5.54469 2.72364 5.49892 2.79799 5.4381 2.85902C5.37728 2.92004 5.30308 2.96606 5.22138 2.99341C5.13972 3.02062 5.0655 3.06643 5.00458 3.12724L0.776689 7.35419C0.7596 7.37129 0.75 7.39447 0.75 7.41864C0.75 7.44281 0.7596 7.466 0.776689 7.48309L1.81099 8.51739C1.82709 8.53364 1.84875 8.54321 1.87161 8.54416C1.89448 8.54512 1.91686 8.5374 1.93427 8.52255C2.14356 8.34281 2.41301 8.24875 2.68869 8.2592C2.96438 8.26966 3.22594 8.38385 3.42101 8.57893C3.61609 8.774 3.73028 9.03556 3.74074 9.31124C3.75119 9.58693 3.65713 9.85638 3.47739 10.0657C3.46254 10.0831 3.45482 10.1055 3.45578 10.1283C3.45673 10.1512 3.4663 10.1728 3.48255 10.189L4.51684 11.2233C4.53394 11.2403 4.55713 11.2499 4.5813 11.2499C4.60547 11.2499 4.62865 11.2403 4.64575 11.2233L8.87388 6.99536C8.93468 6.93444 8.98049 6.86022 9.0077 6.77856C9.03478 6.69677 9.08055 6.62241 9.14137 6.56139C9.20219 6.50037 9.27639 6.45435 9.35809 6.427C9.43971 6.39984 9.51386 6.35401 9.57466 6.29317L11.2233 4.64458C11.2403 4.62748 11.2499 4.6043 11.2499 4.58013C11.2499 4.55595 11.2403 4.53277 11.2233 4.51567L10.189 3.48138C10.1728 3.46512 10.1512 3.45556 10.1283 3.45461C10.1055 3.45365 10.0831 3.46137 10.0657 3.47622C9.85663 3.65607 9.5874 3.75039 9.31182 3.74031C9.03623 3.73023 8.77461 3.61649 8.57927 3.42184Z'
          stroke='#FDFDFD'
          strokeWidth='1.5'
          strokeMiterlimit='10'
        />
        <path
          d='M5.87133 3.29125L5.48438 2.9043M6.90305 4.32297L6.645 4.06516M7.93477 5.35492L7.67695 5.09687M9.09563 6.51555L8.70867 6.12859'
          stroke='#FDFDFD'
          strokeWidth='1.5'
          strokeMiterlimit='10'
          strokeLinecap='round'
        />
      </g>
      <defs>
        <clipPath id='clip0_9110_58809'>
          <rect width='12' height='12' fill='white' />
        </clipPath>
      </defs>
    </svg>
  ),
  ticket: (props: IconProps) => (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" {...props} xmlns="http://www.w3.org/2000/svg">
      <mask id="path-1-inside-1_10309_26080" fill="white">
        <path d="M19.7582 6.89186L17.9194 5.05304C17.771 4.90668 17.5732 4.82122 17.365 4.8135C17.1567 4.80579 16.9531 4.87639 16.7943 5.01137C16.5492 5.22002 16.2346 5.32896 15.913 5.31661C15.5913 5.30426 15.286 5.17152 15.0576 4.9447C14.8309 4.71631 14.6982 4.4111 14.6859 4.08953C14.6735 3.76796 14.7824 3.45348 14.9909 3.20838C15.1259 3.0496 15.1965 2.84601 15.1888 2.63776C15.1811 2.4295 15.0956 2.23169 14.9493 2.08333L13.1088 0.242427C12.9532 0.0871876 12.7425 0 12.5227 0C12.3029 0 12.0922 0.0871876 11.9366 0.242427L9.00527 3.17338C8.82429 3.35501 8.68781 3.57606 8.60651 3.81924C8.59068 3.86599 8.56428 3.90847 8.52938 3.94337C8.49448 3.97827 8.452 4.00466 8.40525 4.02049C8.16198 4.10179 7.94089 4.23844 7.75939 4.41968L0.242427 11.9366C0.0871876 12.0922 0 12.303 0 12.5227C0 12.7425 0.0871876 12.9532 0.242427 13.1088L2.08333 14.9476C2.23169 15.094 2.4295 15.1794 2.63775 15.1871C2.84601 15.1948 3.0496 15.1242 3.20838 14.9893C3.45292 14.7789 3.76792 14.6688 4.09025 14.6809C4.41258 14.693 4.71843 14.8264 4.94652 15.0545C5.1746 15.2826 5.30806 15.5885 5.32017 15.9108C5.33227 16.2331 5.22212 16.5481 5.01178 16.7927C4.8768 16.9514 4.8062 17.155 4.81392 17.3633C4.82163 17.5715 4.90709 17.7694 5.05345 17.9177L6.89227 19.7565C7.04781 19.9118 7.25858 19.999 7.47834 19.999C7.69809 19.999 7.90886 19.9118 8.0644 19.7565L15.5814 12.2396C15.7625 12.0582 15.8991 11.8372 15.9805 11.5941C15.9963 11.5472 16.0227 11.5046 16.0577 11.4696C16.0927 11.4346 16.1353 11.4082 16.1822 11.3925C16.4253 11.3111 16.6462 11.1747 16.8277 10.9937L19.7586 8.06232C19.9133 7.9068 20.0001 7.69635 20 7.477C19.9999 7.25766 19.913 7.04727 19.7582 6.89186ZM10.2424 5.65639C10.1805 5.71832 10.107 5.76744 10.0261 5.80095C9.9452 5.83447 9.85849 5.85172 9.77093 5.85172C9.68336 5.85172 9.59666 5.83447 9.51576 5.80095C9.43486 5.76744 9.36136 5.71832 9.29945 5.65639L8.81985 5.17679C8.69786 5.05113 8.63022 4.88252 8.63154 4.70739C8.63286 4.53227 8.70303 4.36469 8.82689 4.24089C8.95076 4.11708 9.11836 4.04698 9.29349 4.04574C9.46861 4.0445 9.6372 4.11221 9.7628 4.23425L10.2424 4.71344C10.3043 4.77535 10.3534 4.84885 10.387 4.92975C10.4205 5.01064 10.4377 5.09735 10.4377 5.18492C10.4377 5.27248 10.4205 5.35919 10.387 5.44008C10.3534 5.52098 10.3043 5.59448 10.2424 5.65639ZM12.0758 7.4898C11.9508 7.61473 11.7813 7.68491 11.6045 7.68491C11.4278 7.68491 11.2583 7.61473 11.1333 7.4898L10.6749 7.03145C10.613 6.96953 10.5639 6.89603 10.5304 6.81513C10.4969 6.73424 10.4796 6.64753 10.4796 6.55997C10.4796 6.47241 10.4969 6.38571 10.5304 6.30481C10.5639 6.22391 10.613 6.15041 10.6749 6.08849C10.8 5.96345 10.9696 5.8932 11.1464 5.8932C11.234 5.8932 11.3207 5.91045 11.4016 5.94396C11.4825 5.97746 11.556 6.02658 11.6179 6.08849L12.0762 6.54684C12.1384 6.60875 12.1877 6.68231 12.2213 6.76331C12.255 6.8443 12.2723 6.93115 12.2724 7.01886C12.2724 7.10657 12.2552 7.19343 12.2216 7.27446C12.188 7.35549 12.1387 7.42909 12.0766 7.49105L12.0758 7.4898ZM13.9092 9.32321C13.8473 9.38513 13.7738 9.43425 13.6929 9.46776C13.612 9.50128 13.5253 9.51853 13.4377 9.51853C13.3502 9.51853 13.2635 9.50128 13.1826 9.46776C13.1017 9.43425 13.0282 9.38513 12.9663 9.32321L12.5079 8.86485C12.3859 8.73919 12.3183 8.57058 12.3196 8.39546C12.3209 8.22033 12.3911 8.05276 12.515 7.92895C12.6388 7.80514 12.8064 7.73504 12.9816 7.7338C13.1567 7.73256 13.3253 7.80028 13.4509 7.92232L13.9092 8.38067C13.9715 8.44247 14.021 8.51597 14.0549 8.59695C14.0887 8.67792 14.1062 8.76478 14.1064 8.85255C14.1066 8.94031 14.0895 9.02725 14.056 9.10837C14.0225 9.1895 13.9733 9.26321 13.9113 9.32529L13.9092 9.32321ZM15.7605 11.1791C15.6986 11.241 15.6251 11.2902 15.5442 11.3237C15.4633 11.3572 15.3766 11.3744 15.2891 11.3744C15.2015 11.3744 15.1148 11.3572 15.0339 11.3237C14.953 11.2902 14.8795 11.241 14.8176 11.1791L14.3409 10.6999C14.2777 10.6382 14.2274 10.5645 14.1929 10.4832C14.1584 10.4018 14.1404 10.3144 14.14 10.2261C14.1395 10.1377 14.1567 10.0502 14.1904 9.96848C14.2241 9.88681 14.2737 9.81265 14.3364 9.75032C14.399 9.68799 14.4734 9.63874 14.5552 9.60542C14.6371 9.5721 14.7247 9.55538 14.8131 9.55623C14.9014 9.55708 14.9887 9.57549 15.0699 9.61038C15.1511 9.64527 15.2245 9.69595 15.2859 9.75947L15.763 10.2382C15.8249 10.3002 15.874 10.3737 15.9075 10.4546C15.941 10.5355 15.9582 10.6222 15.9582 10.7098C15.9581 10.7974 15.9408 10.8841 15.9073 10.965C15.8737 11.0458 15.8246 11.1193 15.7626 11.1812L15.7605 11.1791Z"/>
      </mask>
      <path d="M19.7582 6.89186L17.9194 5.05304C17.771 4.90668 17.5732 4.82122 17.365 4.8135C17.1567 4.80579 16.9531 4.87639 16.7943 5.01137C16.5492 5.22002 16.2346 5.32896 15.913 5.31661C15.5913 5.30426 15.286 5.17152 15.0576 4.9447C14.8309 4.71631 14.6982 4.4111 14.6859 4.08953C14.6735 3.76796 14.7824 3.45348 14.9909 3.20838C15.1259 3.0496 15.1965 2.84601 15.1888 2.63776C15.1811 2.4295 15.0956 2.23169 14.9493 2.08333L13.1088 0.242427C12.9532 0.0871876 12.7425 0 12.5227 0C12.3029 0 12.0922 0.0871876 11.9366 0.242427L9.00527 3.17338C8.82429 3.35501 8.68781 3.57606 8.60651 3.81924C8.59068 3.86599 8.56428 3.90847 8.52938 3.94337C8.49448 3.97827 8.452 4.00466 8.40525 4.02049C8.16198 4.10179 7.94089 4.23844 7.75939 4.41968L0.242427 11.9366C0.0871876 12.0922 0 12.303 0 12.5227C0 12.7425 0.0871876 12.9532 0.242427 13.1088L2.08333 14.9476C2.23169 15.094 2.4295 15.1794 2.63775 15.1871C2.84601 15.1948 3.0496 15.1242 3.20838 14.9893C3.45292 14.7789 3.76792 14.6688 4.09025 14.6809C4.41258 14.693 4.71843 14.8264 4.94652 15.0545C5.1746 15.2826 5.30806 15.5885 5.32017 15.9108C5.33227 16.2331 5.22212 16.5481 5.01178 16.7927C4.8768 16.9514 4.8062 17.155 4.81392 17.3633C4.82163 17.5715 4.90709 17.7694 5.05345 17.9177L6.89227 19.7565C7.04781 19.9118 7.25858 19.999 7.47834 19.999C7.69809 19.999 7.90886 19.9118 8.0644 19.7565L15.5814 12.2396C15.7625 12.0582 15.8991 11.8372 15.9805 11.5941C15.9963 11.5472 16.0227 11.5046 16.0577 11.4696C16.0927 11.4346 16.1353 11.4082 16.1822 11.3925C16.4253 11.3111 16.6462 11.1747 16.8277 10.9937L19.7586 8.06232C19.9133 7.9068 20.0001 7.69635 20 7.477C19.9999 7.25766 19.913 7.04727 19.7582 6.89186ZM10.2424 5.65639C10.1805 5.71832 10.107 5.76744 10.0261 5.80095C9.9452 5.83447 9.85849 5.85172 9.77093 5.85172C9.68336 5.85172 9.59666 5.83447 9.51576 5.80095C9.43486 5.76744 9.36136 5.71832 9.29945 5.65639L8.81985 5.17679C8.69786 5.05113 8.63022 4.88252 8.63154 4.70739C8.63286 4.53227 8.70303 4.36469 8.82689 4.24089C8.95076 4.11708 9.11836 4.04698 9.29349 4.04574C9.46861 4.0445 9.6372 4.11221 9.7628 4.23425L10.2424 4.71344C10.3043 4.77535 10.3534 4.84885 10.387 4.92975C10.4205 5.01064 10.4377 5.09735 10.4377 5.18492C10.4377 5.27248 10.4205 5.35919 10.387 5.44008C10.3534 5.52098 10.3043 5.59448 10.2424 5.65639ZM12.0758 7.4898C11.9508 7.61473 11.7813 7.68491 11.6045 7.68491C11.4278 7.68491 11.2583 7.61473 11.1333 7.4898L10.6749 7.03145C10.613 6.96953 10.5639 6.89603 10.5304 6.81513C10.4969 6.73424 10.4796 6.64753 10.4796 6.55997C10.4796 6.47241 10.4969 6.38571 10.5304 6.30481C10.5639 6.22391 10.613 6.15041 10.6749 6.08849C10.8 5.96345 10.9696 5.8932 11.1464 5.8932C11.234 5.8932 11.3207 5.91045 11.4016 5.94396C11.4825 5.97746 11.556 6.02658 11.6179 6.08849L12.0762 6.54684C12.1384 6.60875 12.1877 6.68231 12.2213 6.76331C12.255 6.8443 12.2723 6.93115 12.2724 7.01886C12.2724 7.10657 12.2552 7.19343 12.2216 7.27446C12.188 7.35549 12.1387 7.42909 12.0766 7.49105L12.0758 7.4898ZM13.9092 9.32321C13.8473 9.38513 13.7738 9.43425 13.6929 9.46776C13.612 9.50128 13.5253 9.51853 13.4377 9.51853C13.3502 9.51853 13.2635 9.50128 13.1826 9.46776C13.1017 9.43425 13.0282 9.38513 12.9663 9.32321L12.5079 8.86485C12.3859 8.73919 12.3183 8.57058 12.3196 8.39546C12.3209 8.22033 12.3911 8.05276 12.515 7.92895C12.6388 7.80514 12.8064 7.73504 12.9816 7.7338C13.1567 7.73256 13.3253 7.80028 13.4509 7.92232L13.9092 8.38067C13.9715 8.44247 14.021 8.51597 14.0549 8.59695C14.0887 8.67792 14.1062 8.76478 14.1064 8.85255C14.1066 8.94031 14.0895 9.02725 14.056 9.10837C14.0225 9.1895 13.9733 9.26321 13.9113 9.32529L13.9092 9.32321ZM15.7605 11.1791C15.6986 11.241 15.6251 11.2902 15.5442 11.3237C15.4633 11.3572 15.3766 11.3744 15.2891 11.3744C15.2015 11.3744 15.1148 11.3572 15.0339 11.3237C14.953 11.2902 14.8795 11.241 14.8176 11.1791L14.3409 10.6999C14.2777 10.6382 14.2274 10.5645 14.1929 10.4832C14.1584 10.4018 14.1404 10.3144 14.14 10.2261C14.1395 10.1377 14.1567 10.0502 14.1904 9.96848C14.2241 9.88681 14.2737 9.81265 14.3364 9.75032C14.399 9.68799 14.4734 9.63874 14.5552 9.60542C14.6371 9.5721 14.7247 9.55538 14.8131 9.55623C14.9014 9.55708 14.9887 9.57549 15.0699 9.61038C15.1511 9.64527 15.2245 9.69595 15.2859 9.75947L15.763 10.2382C15.8249 10.3002 15.874 10.3737 15.9075 10.4546C15.941 10.5355 15.9582 10.6222 15.9582 10.7098C15.9581 10.7974 15.9408 10.8841 15.9073 10.965C15.8737 11.0458 15.8246 11.1193 15.7626 11.1812L15.7605 11.1791Z" stroke="#D4D4D8" stroke-width="2" mask="url(#path-1-inside-1_10309_26080)"/>
    </svg>
  ),
  emptyIcon: (props: IconProps) => (
    <svg width="152" height="120" viewBox="0 0 152 120" fill="none" {...props} xmlns="http://www.w3.org/2000/svg">
      <path d="M71.1785 95.3328C97.0856 95.3328 118.087 74.2594 118.087 48.2641C118.087 22.2687 97.0856 1.19531 71.1785 1.19531C45.2714 1.19531 24.2695 22.2687 24.2695 48.2641C24.2695 74.2594 45.2714 95.3328 71.1785 95.3328Z" fill="#E6E6E6"/>
      <path d="M44.0699 5.23529C45.3092 4.83111 45.9873 3.49537 45.5845 2.25183C45.1817 1.00829 43.8505 0.327853 42.6112 0.732033C41.3719 1.13621 40.6937 2.47195 41.0965 3.71549C41.4994 4.95903 42.8306 5.63947 44.0699 5.23529Z" fill="#2A2E37"/>
      <path d="M98.6211 81.4896C97.9202 84.7056 95.0733 86.9988 91.7817 86.9988H20.8704C16.4074 86.9988 13.0841 82.8784 14.0291 78.5166L26.5693 20.635C27.267 17.4149 30.1158 15.1172 33.4106 15.1172H104.398C108.865 15.1172 112.189 19.2438 111.237 23.608L98.6211 81.4896Z" fill="white"/>
      <path d="M98.6211 81.4896C97.9202 84.7056 95.0733 86.9988 91.7817 86.9988H20.8704C16.4074 86.9988 13.0841 82.8784 14.0291 78.5166L26.5693 20.635C27.267 17.4149 30.1158 15.1172 33.4106 15.1172H104.398C108.865 15.1172 112.189 19.2438 111.237 23.608L98.6211 81.4896Z" fill="#B9B9B9"/>
      <path d="M48.2606 77.4986C49.3506 81.9118 46.0106 86.1771 41.4648 86.1771H31.3829C27.9245 86.1771 25.0764 83.4708 25.2798 80.2979L28.2134 17.857C28.2794 16.4514 29.4382 15.3457 30.8454 15.3457C32.0572 15.3457 33.1128 16.1723 33.4034 17.3488L48.2606 77.4986Z" fill="#B9B9B9"/>
      <g filter="url(#filter0_d_10504_27463)">
        <path d="M129.501 78.2384C130.653 82.6719 127.306 86.9988 122.726 86.9988H51.9527C48.7689 86.9988 45.9861 84.8503 45.1805 81.7701L32.1073 31.7854L32.0129 31.4066L30.0513 23.8833C28.895 19.4484 32.2417 15.1172 36.8248 15.1172H107.681C110.866 15.1172 113.65 17.268 114.454 20.3503L116.77 29.2283L117.336 31.4066L117.431 31.7854L129.501 78.2384Z" fill="#956EDE"/>
      </g>
      <path d="M116.77 29.2283C117.056 30.3306 116.225 31.4066 115.086 31.4066H37.4218C34.236 31.4066 31.452 29.2554 30.6482 26.1727L30.0513 23.8833C28.895 19.4484 32.2417 15.1172 36.8248 15.1172H107.681C110.866 15.1172 113.65 17.268 114.454 20.3503L116.77 29.2283Z" fill="url(#paint0_linear_10504_27463)"/>
      <path d="M50.2273 26.7662C51.8954 26.7662 53.2476 25.4094 53.2476 23.7357C53.2476 22.0619 51.8954 20.7051 50.2273 20.7051C48.5593 20.7051 47.207 22.0619 47.207 23.7357C47.207 25.4094 48.5593 26.7662 50.2273 26.7662Z" fill="#2A2E37"/>
      <path d="M100.063 26.7662C101.731 26.7662 103.084 25.4094 103.084 23.7357C103.084 22.0619 101.731 20.7051 100.063 20.7051C98.3952 20.7051 97.043 22.0619 97.043 23.7357C97.043 25.4094 98.3952 26.7662 100.063 26.7662Z" fill="#2A2E37"/>
      <path d="M49.4728 24.9665C48.9065 24.9665 48.4346 24.493 48.4346 23.9248C48.4346 23.3565 48.9065 22.883 49.4728 22.883C51.5493 22.883 53.7201 19.9471 53.7201 15.496C53.7201 15.4013 53.7201 15.2118 53.7201 15.1171C53.6257 10.7607 51.4549 8.10892 49.4728 8.10892C47.7501 8.10892 45.8702 10.2385 45.3395 13.634C45.2512 14.1994 44.7821 14.6436 44.2098 14.6436C43.5829 14.6436 43.0807 14.1144 43.1678 13.4935C43.7767 9.15493 46.3376 6.02539 49.3784 6.02539C52.8706 6.02539 55.6078 10.0977 55.7966 15.3066C55.7966 15.4013 55.7966 15.496 55.7966 15.5907C55.8909 20.7995 53.0594 24.9665 49.4728 24.9665Z" fill="#7B8598"/>
      <path d="M99.0236 24.9665C98.4573 24.9665 97.9854 24.493 97.9854 23.9248C97.9854 23.3565 98.4573 22.883 99.0236 22.883C101.1 22.883 103.271 19.9471 103.271 15.496C103.271 15.4013 103.271 15.2118 103.271 15.1171C103.176 10.7607 101.006 8.10892 99.0236 8.10892C97.3009 8.10892 95.421 10.2385 94.8903 13.634C94.802 14.1994 94.3329 14.6436 93.7606 14.6436C93.1336 14.6436 92.6315 14.1144 92.7186 13.4935C93.3275 9.15493 95.8884 6.02539 98.9292 6.02539C102.421 6.02539 105.159 10.0977 105.347 15.3066C105.347 15.4013 105.347 15.496 105.347 15.5907C105.442 20.7995 102.61 24.9665 99.0236 24.9665Z" fill="#7B8598"/>
      <path d="M1.33594 86.9043H138.665" stroke="#B9B9B9" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
      <path opacity="0.3" d="M100.534 52.7154C99.9674 52.7154 99.5898 52.2419 99.5898 51.7683C99.5898 51.2001 99.9674 50.8213 100.534 50.8213H103.46C104.026 50.8213 104.403 51.2948 104.403 51.7683C104.403 52.3366 103.932 52.7154 103.46 52.7154H100.534Z" fill="#474D5E"/>
      <path opacity="0.3" d="M60.3242 79.4221C60.1475 79.4221 59.9709 79.3599 59.8136 79.2549C59.4454 79.0091 59.3221 78.5439 59.2066 78.1165C58.418 75.1984 55.7706 73.1715 52.7477 73.1715H50.4138C49.8475 73.1715 49.47 72.698 49.47 72.2244C49.47 71.6562 49.9419 71.2774 50.4138 71.2774H51.7932C54.5576 71.2774 56.5557 68.635 55.8027 65.9752C55.2948 64.1815 53.6574 62.9433 51.7932 62.9433H47.7711C47.2048 62.9433 46.8272 62.4698 46.8272 61.9962C46.8272 61.428 47.2048 61.0492 47.7711 61.0492H48.8673C51.6316 61.0492 53.6298 58.4067 52.8767 55.7469C52.3689 53.9532 50.7315 52.7151 48.8673 52.7151H45.2227C44.6564 52.7151 44.2788 52.2415 44.2788 51.768C44.2788 51.1998 44.6564 50.821 45.2227 50.821H46.0358C48.8001 50.821 50.7983 48.1785 50.0452 45.5187C49.5374 43.725 47.9 42.4868 46.0358 42.4868H42.6743C42.108 42.4868 41.7305 42.0133 41.7305 41.5398C41.7305 40.9716 42.108 40.5927 42.6743 40.5927H45.0829C46.8617 40.5927 48.1513 38.8977 47.6767 37.1833C47.6012 36.8046 47.7672 36.3653 48.0781 36.1561C48.2343 36.051 48.4322 36.0469 48.6205 36.0469C48.8691 36.0469 49.1177 36.17 49.3124 36.3623C49.5165 36.5639 49.6048 36.8483 49.6851 37.1237C50.2847 39.1794 52.1692 40.5927 54.3106 40.5927H58.2477C58.814 40.5927 59.1915 40.9716 59.1915 41.5398C59.1915 42.108 58.814 42.4868 58.2477 42.4868H56.7739C54.0096 42.4868 52.0114 45.1293 52.7645 47.7891C53.2723 49.5828 54.9097 50.821 56.7739 50.821H57.9659C61.7621 50.821 64.5139 47.2038 63.5011 43.5452L61.7399 37.1833C61.6644 36.8046 61.8304 36.3653 62.1413 36.1561C62.2976 36.051 62.4954 36.0469 62.6838 36.0469C62.9323 36.0469 63.1809 36.17 63.3756 36.3623C63.5797 36.5639 63.668 36.8483 63.7483 37.1237C64.348 39.1794 66.2324 40.5927 68.3738 40.5927H68.8187C69.385 40.5927 69.7626 40.9716 69.7626 41.5398C69.7626 42.108 69.2906 42.4868 68.8187 42.4868C67.0669 42.4868 65.8006 44.1614 66.2779 45.847L66.8277 47.7891C67.3356 49.5828 68.973 50.821 70.8372 50.821H75.7088C76.2751 50.821 76.6526 51.2945 76.6526 51.768C76.6526 52.3362 76.2751 52.7151 75.7088 52.7151H73.7631C70.9987 52.7151 69.0006 55.3575 69.7536 58.0173C70.2615 59.811 71.8989 61.0492 73.7631 61.0492H74.982C78.8079 61.0492 81.5778 57.3984 80.5474 53.7138L78.094 44.941C77.6883 43.4901 76.366 42.4868 74.8593 42.4868C74.293 42.4868 73.9155 42.0133 73.9155 41.5398C73.9155 40.9716 74.3874 40.5927 74.8593 40.5927C75.856 40.5927 76.5785 39.643 76.3126 38.6825L75.8975 37.1833C75.8292 36.9776 75.8598 36.8215 75.9178 36.6431C75.965 36.4977 76.0192 36.3507 76.1208 36.2363C76.1722 36.1785 76.2334 36.1241 76.3046 36.0762C76.4602 35.9716 76.6538 35.9521 76.8414 35.9521C77.09 35.9521 77.3386 36.0753 77.5333 36.2676C77.7373 36.4692 77.8256 36.7536 77.9059 37.029L78.3628 38.5952C78.6917 39.7228 79.7253 40.498 80.8999 40.498C81.4662 40.498 81.8437 40.8768 81.8437 41.4451C81.8437 42.0133 81.4662 42.3921 80.8999 42.3921C80.1897 42.3921 79.6763 43.071 79.8698 43.7544L80.9853 47.6944C81.4932 49.4881 83.1306 50.7262 84.9948 50.7262H88.3199C91.0842 50.7262 93.0824 48.0838 92.3293 45.424C91.8215 43.6303 90.1841 42.3921 88.3199 42.3921H85.336C84.7697 42.3921 84.3921 41.9186 84.3921 41.4451C84.3921 40.8768 84.864 40.498 85.336 40.498H87.0838C88.8627 40.498 90.1522 38.803 89.6776 37.0886C89.6021 36.7099 89.7681 36.2706 90.0791 36.0614C90.2353 35.9563 90.4332 35.9521 90.6215 35.9521C90.8701 35.9521 91.1187 36.0753 91.3134 36.2676C91.5174 36.4692 91.6057 36.7536 91.686 37.029C92.2857 39.0847 94.1701 40.498 96.3116 40.498H101.147C102.926 40.498 104.215 38.803 103.741 37.0886C103.665 36.7099 103.831 36.2706 104.142 36.0614C104.299 35.9563 104.496 35.9521 104.685 35.9521C104.933 35.9521 105.182 36.0753 105.377 36.2676C105.581 36.4692 105.669 36.7536 105.749 37.029C106.349 39.0847 108.233 40.498 110.375 40.498H114.123C114.689 40.498 115.067 40.8768 115.067 41.4451C115.067 42.0133 114.689 42.3921 114.123 42.3921H112.555C109.791 42.3921 107.792 45.0346 108.546 47.6944C109.053 49.4881 110.691 50.7262 112.555 50.7262H116.483C117.049 50.7262 117.427 51.1998 117.427 51.6733C117.427 52.2415 117.049 52.6204 116.483 52.6204H115.481C112.717 52.6204 110.718 55.2628 111.471 57.9226C111.979 59.7163 113.617 60.9545 115.481 60.9545H119.125C119.692 60.9545 120.069 61.428 120.069 61.9015C120.069 62.4698 119.692 62.8486 119.125 62.8486H118.407C115.642 62.8486 113.644 65.491 114.397 68.1508C114.905 69.9445 116.543 71.1827 118.407 71.1827H121.674C122.24 71.1827 122.618 71.6562 122.618 72.1297C122.618 72.698 122.146 73.0768 121.674 73.0768H120.943C118.326 73.0768 116.433 75.5773 117.143 78.0962C117.219 78.4749 117.053 78.9142 116.742 79.1234C116.586 79.2285 116.388 79.2327 116.2 79.2327C116.016 79.2327 115.833 79.1659 115.672 79.0538C115.319 78.8094 115.198 78.3615 115.085 77.9479L114.904 77.2914C114.22 74.802 111.956 73.0768 109.375 73.0768H106.883C104.317 73.0768 102.466 75.5352 103.175 78.0015C103.243 78.2072 103.212 78.3633 103.154 78.5417C103.107 78.6872 103.053 78.8341 102.951 78.9485C102.9 79.0063 102.839 79.0608 102.767 79.1086C102.612 79.2132 102.418 79.2327 102.231 79.2327C102.041 79.2327 101.852 79.161 101.686 79.0418C101.349 78.7992 101.231 78.3685 101.119 77.9687L100.951 77.3685C100.257 74.8871 97.9953 73.1715 95.4187 73.1715H92.9141C90.3479 73.1715 88.4967 75.6299 89.2057 78.0962C89.2812 78.4749 89.1152 78.9142 88.8043 79.1234C88.648 79.2285 88.4502 79.2327 88.2619 79.2327C88.0723 79.2327 87.8828 79.161 87.7171 79.0418C87.3802 78.7992 87.2617 78.3685 87.1499 77.9687L86.982 77.3685C86.2878 74.8871 84.0264 73.1715 81.4498 73.1715H78.9453C76.3791 73.1715 74.5278 75.6299 75.2369 78.0962C75.3123 78.4749 75.1463 78.9142 74.8354 79.1234C74.6792 79.2285 74.4813 79.2327 74.293 79.2327C74.1035 79.2327 73.9139 79.161 73.7482 79.0418C73.4114 78.7992 73.2929 78.3685 73.181 77.9687L73.017 77.3824C72.3206 74.8928 70.0516 73.1715 67.4665 73.1715H65.0675C62.4502 73.1715 60.5575 75.672 61.268 78.1909C61.3363 78.3967 61.3057 78.5527 61.2478 78.7311C61.2005 78.8766 61.1463 79.0235 61.0447 79.1379C60.9934 79.1957 60.9321 79.2502 60.8609 79.298C60.7053 79.4027 60.5117 79.4221 60.3242 79.4221ZM86.3687 68.2919C86.8748 70.0593 88.4907 71.2774 90.3291 71.2774H107.669C110.433 71.2774 112.431 68.635 111.678 65.9752C111.17 64.1815 109.533 62.9433 107.669 62.9433H101.98C100.4 62.9433 99.2558 64.452 99.6824 65.9739C99.7507 66.1796 99.7201 66.3357 99.6621 66.514C99.6148 66.6595 99.5606 66.8065 99.4591 66.9209C99.4077 66.9787 99.3464 67.0331 99.2753 67.081C99.1196 67.1856 98.926 67.205 98.7385 67.205C98.4861 67.205 98.2338 67.0781 98.0378 66.8808C97.8416 66.6832 97.7582 66.4067 97.6858 66.1379C97.1931 64.3089 95.5346 63.038 93.6404 63.038H93.2642C92.6979 63.038 92.3204 62.5645 92.3204 62.0909C92.3204 61.5227 92.6979 61.1439 93.2642 61.1439C94.7793 61.1439 95.8745 59.6956 95.4617 58.2378L94.7833 55.8416C94.2755 54.0479 92.6381 52.8098 90.7739 52.8098H87.5432C84.7788 52.8098 82.7807 55.4522 83.5337 58.112L83.595 58.3286C84.0666 59.9942 85.587 61.1439 87.318 61.1439C87.8843 61.1439 88.2619 61.6174 88.2619 62.0909C88.2619 62.6592 87.8843 63.038 87.318 63.038C86.0885 63.038 85.2012 64.2152 85.5397 65.3971L86.3687 68.2919ZM72.3964 68.2455C72.9042 70.0392 74.5416 71.2774 76.4058 71.2774H79.7309C82.4953 71.2774 84.4934 68.635 83.7404 65.9752C83.2325 64.1815 81.5951 62.9433 79.7309 62.9433H76.4058C73.6415 62.9433 71.6433 65.5857 72.3964 68.2455ZM58.4504 68.2033C58.9436 70.0179 60.5911 71.2774 62.4716 71.2774H65.7621C68.5264 71.2774 70.5246 68.635 69.7715 65.9752C69.2637 64.1815 67.6263 62.9433 65.7621 62.9433H65.4209C64.8546 62.9433 64.4771 62.4698 64.4771 61.9962C64.4771 61.428 64.949 61.0492 65.4209 61.0492C66.936 61.0492 68.0311 59.6009 67.6184 58.1431L66.94 55.7469C66.4321 53.9532 64.7947 52.7151 62.9305 52.7151H59.6054C56.8411 52.7151 54.8493 55.3799 55.6023 58.0397C56.1043 59.8126 57.7264 61.0492 59.5691 61.0492C60.1354 61.0492 60.5129 61.5227 60.5129 61.9962C60.5129 62.5645 60.041 62.9433 59.5691 62.9433C58.2852 62.9433 57.3524 64.1635 57.6891 65.4024L58.4504 68.2033ZM97.1654 56.8504C97.8729 59.3352 100.143 61.0492 102.727 61.0492C106.555 61.0492 109.326 57.3963 108.295 53.7096L106.342 46.7243C105.641 44.219 103.358 42.4868 100.757 42.4868C96.9058 42.4868 94.1244 46.1708 95.179 49.8744L97.1654 56.8504Z" fill="#1A1A1A" fill-opacity="0.5"/>
      <path d="M71.3746 51.3848C67.4525 51.3848 64.158 48.8755 62.903 45.2683C62.5892 44.4842 63.0598 43.7 63.8443 43.3863C64.6287 43.0727 65.4131 43.5432 65.7268 44.3273C66.5112 46.6798 68.8645 48.405 71.3746 48.405C73.8847 48.405 76.2379 46.8367 77.0223 44.3273C77.336 43.5432 78.1205 43.0727 78.9049 43.3863C79.6893 43.7 80.1599 44.4842 79.8461 45.2683C78.5911 49.0323 75.2966 51.3848 71.3746 51.3848Z" fill="#E4E4E7"/>
      <path d="M95.0621 51.3848C91.14 51.3848 87.8455 48.8755 86.5905 45.2683C86.2767 44.4842 86.7473 43.7 87.5318 43.3863C88.3162 43.0727 89.1006 43.5432 89.4143 44.3273C90.1987 46.6798 92.552 48.405 95.0621 48.405C97.5722 48.405 99.9254 46.8367 100.71 44.3273C101.024 43.5432 101.808 43.0727 102.592 43.3863C103.377 43.7 103.847 44.4842 103.534 45.2683C102.279 49.0323 98.9841 51.3848 95.0621 51.3848Z" fill="#E4E4E7"/>
      <path d="M87.8661 70.5874C89.8589 70.5874 91.4743 68.9724 91.4743 66.9802C91.4743 64.988 89.8589 63.373 87.8661 63.373C85.8733 63.373 84.2578 64.988 84.2578 66.9802C84.2578 68.9724 85.8733 70.5874 87.8661 70.5874Z" fill="#E4E4E7"/>
      <path d="M12.8256 5.52272V0.873047H30.7958V5.52272L18.5338 20.3171H31.0072V24.9668H12.1914V20.3171L24.4534 5.52272H12.8256Z" fill="#B9B9B9"/>
      <path d="M36.0244 34.8671V31.9082H47.6521V34.8671L39.8298 44.3778H47.8635V47.3367H35.6016V44.3778L43.4238 34.8671H36.0244Z" fill="#B9B9B9"/>
      <defs>
        <filter id="filter0_d_10504_27463" x="7.82031" y="4.11719" width="143.91" height="115.882" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
        <feOffset dy="11"/>
        <feGaussianBlur stdDeviation="11"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.27 0"/>
        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_10504_27463"/>
        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_10504_27463" result="shape"/>
        </filter>
        <linearGradient id="paint0_linear_10504_27463" x1="86.8822" y1="35.7769" x2="70.6241" y2="-10.3793" gradientUnits="userSpaceOnUse">
        <stop stop-color="#6600CC"/>
        <stop offset="0.8" stop-color="#B098FF"/>
        </linearGradient>
      </defs>
    </svg>
  ),
  emptyTicketIcon: (props: IconProps) => (
    <svg width="70" height="91" viewBox="0 0 70 91" fill="none" {...props} xmlns="http://www.w3.org/2000/svg">
      <path d="M53.5938 36.9691H59.0625C59.6641 36.9691 60.1562 37.481 60.1562 38.1066C60.1562 38.7323 59.6641 39.2441 59.0625 39.2441H53.5938V43.7941H59.0625C59.6641 43.7941 60.1562 44.306 60.1562 44.9316C60.1562 45.5573 59.6641 46.0691 59.0625 46.0691H53.5938V50.6191H59.0625C59.6641 50.6191 60.1562 51.131 60.1562 51.7566C60.1562 52.3823 59.6641 52.8941 59.0625 52.8941H53.5938V57.4441H59.0625C59.6641 57.4441 60.1562 57.956 60.1562 58.5816C60.1562 59.2073 59.6641 59.7191 59.0625 59.7191H53.2656C52.6203 61.3913 51.0562 62.5629 49.2188 62.5629H32.8125V65.9754C32.8125 67.2266 33.7969 68.2504 35 68.2504H63.4375C64.6406 68.2504 65.625 67.2266 65.625 65.9754V30.7129H53.5938V36.9691Z" fill="#956EDE"/>
      <path d="M37.1875 6.8248C37.1875 5.57355 36.2031 4.5498 35 4.5498H6.5625C5.35938 4.5498 4.375 5.57355 4.375 6.8248V10.2373H37.1875V6.8248Z" fill="url(#paint0_linear_10504_27454)"/>
      <path d="M4.375 12.5127V47.7752C4.375 49.0264 5.35938 50.0502 6.5625 50.0502H16.4062V41.5189H10.9375C10.3359 41.5189 9.84375 41.0071 9.84375 40.3814C9.84375 39.7558 10.3359 39.2439 10.9375 39.2439H16.4062V34.6939H10.9375C10.3359 34.6939 9.84375 34.1821 9.84375 33.5564C9.84375 32.9308 10.3359 32.4189 10.9375 32.4189H16.4062V27.8689H10.9375C10.3359 27.8689 9.84375 27.3571 9.84375 26.7314C9.84375 26.1058 10.3359 25.5939 10.9375 25.5939H16.4062V21.0439H10.9375C10.3359 21.0439 9.84375 20.5321 9.84375 19.9064C9.84375 19.2808 10.3359 18.7689 10.9375 18.7689H16.4062V17.0627C16.4062 14.5488 18.3641 12.5127 20.7812 12.5127H4.375Z" fill="#956EDE"/>
      <path d="M63.4375 22.75H53.5938V28.4375H65.625V25.025C65.625 23.7738 64.6406 22.75 63.4375 22.75Z" fill="url(#paint1_linear_10504_27454)"/>
      <path d="M18.5938 17.0621V20.4746H51.4062V17.0621C51.4062 15.8109 50.4219 14.7871 49.2188 14.7871H20.7812C19.5781 14.7871 18.5938 15.8109 18.5938 17.0621Z" fill="url(#paint2_linear_10504_27454)"/>
      <path d="M51.4062 58.0125V22.75H18.5938V58.0125C18.5938 59.2637 19.5781 60.2875 20.7812 60.2875H49.2188C50.4219 60.2875 51.4062 59.2637 51.4062 58.0125ZM26.5672 35.2739L27.7922 33.9999L26.5672 32.7259C26.1406 32.2823 26.1406 31.5656 26.5672 31.122C26.9938 30.6784 27.6828 30.6784 28.1094 31.122L29.3344 32.396L30.5594 31.122C30.9859 30.6784 31.675 30.6784 32.1016 31.122C32.5281 31.5656 32.5281 32.2823 32.1016 32.7259L30.8766 33.9999L32.1016 35.2739C32.5281 35.7175 32.5281 36.4341 32.1016 36.8777C31.8828 37.1052 31.6094 37.2076 31.325 37.2076C31.0406 37.2076 30.7672 37.0939 30.5484 36.8777L29.3234 35.6037L28.0984 36.8777C27.8797 37.1052 27.6062 37.2076 27.3219 37.2076C27.0375 37.2076 26.7641 37.0939 26.5453 36.8777C26.1188 36.4341 26.1188 35.7175 26.5453 35.2739H26.5672ZM42.5797 48.2414C42.4156 48.3324 42.2406 48.3779 42.0656 48.3779C41.6719 48.3779 41.3 48.1617 41.1031 47.775C39.9 45.4204 37.5594 43.9644 35 43.9644C32.4406 43.9644 30.1 45.4204 28.8969 47.775C28.6125 48.3324 27.9453 48.5371 27.4203 48.2414C26.8844 47.9456 26.6875 47.2518 26.9719 46.7057C28.5578 43.6117 31.6312 41.6894 35.0109 41.6894C38.3906 41.6894 41.4641 43.6117 43.05 46.7057C43.3344 47.2631 43.1375 47.9456 42.6016 48.2414H42.5797ZM43.4219 35.2739C43.8484 35.7175 43.8484 36.4341 43.4219 36.8777C43.2031 37.1052 42.9297 37.2076 42.6453 37.2076C42.3609 37.2076 42.0875 37.0939 41.8687 36.8777L40.6437 35.6037L39.4188 36.8777C39.2 37.1052 38.9266 37.2076 38.6422 37.2076C38.3578 37.2076 38.0844 37.0939 37.8656 36.8777C37.4391 36.4341 37.4391 35.7175 37.8656 35.2739L39.0906 33.9999L37.8656 32.7259C37.4391 32.2823 37.4391 31.5656 37.8656 31.122C38.2922 30.6784 38.9812 30.6784 39.4078 31.122L40.6328 32.396L41.8578 31.122C42.2844 30.6784 42.9734 30.6784 43.4 31.122C43.8266 31.5656 43.8266 32.2823 43.4 32.7259L42.175 33.9999L43.4 35.2739H43.4219Z" fill="#956EDE"/>
      <defs>
        <linearGradient id="paint0_linear_10504_27454" x1="26.0313" y1="11.7632" x2="20.5656" y2="-4.51696" gradientUnits="userSpaceOnUse">
          <stop stop-color="#6600CC"/>
          <stop offset="0.8" stop-color="#B098FF"/>
        </linearGradient>
        <linearGradient id="paint1_linear_10504_27454" x1="61.5344" y1="29.9634" x2="52.5119" y2="20.1094" gradientUnits="userSpaceOnUse">
          <stop stop-color="#6600CC"/>
          <stop offset="0.8" stop-color="#B098FF"/>
        </linearGradient>
        <linearGradient id="paint2_linear_10504_27454" x1="40.25" y1="22.0005" x2="34.7843" y2="5.72034" gradientUnits="userSpaceOnUse">
          <stop stop-color="#6600CC"/>
          <stop offset="0.8" stop-color="#B098FF"/>
        </linearGradient>
      </defs>
    </svg>
  ),

};
