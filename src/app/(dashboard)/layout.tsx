'use client';

import { AppSidebar } from '@/components/layouts';
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from '@/components/ui/sidebar';
import BellIcon from '~/svg/bell.svg';
import { SmBoldLabel } from '@/components/ui/typography';
import { SearchForm } from '@/components/forms/';
import { ThemeToggleSwitch } from '@/components/ui/theme-toggle';
import { AvatarInitials } from '@/components/avatars/initials';

import { redirect, usePathname } from 'next/navigation';
import { Fragment } from 'react';
import { NAV_SECTIONS } from '@/lib/constants/generic';
import { useAuth } from '@/lib/hooks/use-auth';
import { APIProvider } from '@/api';

export default function AppDashboardLayout({
  children,
}: {
  readonly children: React.ReactNode;
}) {
  const { isAuthenticated, logout } = useAuth();

  const pathname = usePathname();

  const allNavItems = NAV_SECTIONS.flatMap((section) => section.items);

  const currentNavItem =
    allNavItems.find((item) => pathname.startsWith(item.href)) ?? null;

  const IconComponent = currentNavItem?.Icon ?? Fragment;
  const title = currentNavItem?.title ?? '';

  if (!isAuthenticated) {
    redirect('/login');
  }

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <header className='flex h-20 py-4 mr-8 shrink-0 items-center justify-between gap-2'>
          <div className='flex items-center gap-2'>
            <SidebarTrigger />
            <div className='flex items-center justify-center rounded-full size-12 bg-bg-subtle-light dark:bg-bg-subtle-dark [&>svg]:fill-accent-moderate'>
              <IconComponent width={24} height={24} />
            </div>
            <SmBoldLabel themed weight='bold'>
              {title}
            </SmBoldLabel>
          </div>
          <SearchForm className='flex-1 max-w-[361px] hidden md:flex' />
          <div className='flex items-center gap-4'>
            <ThemeToggleSwitch />
            <div className='flex items-center justify-center size-8 rounded-full border border-accent-bold-light dark:border-accent-bold-dark'>
              <BellIcon width={16} height={16} />
            </div>
            <AvatarInitials initials='PO' onProfileClick={logout} />
          </div>
        </header>
        <main className='mr-8'>
          <APIProvider>{children}</APIProvider>
        </main>
      </SidebarInset>
    </SidebarProvider>
  );
}
