'use client';
import { DatePicker } from '@/components/ui/date-picker';
import EventCarousel from '@/components/events/carousel';
import EventListing from '@/components/events/listing';
import { useState } from 'react';

export default function Events() {
  const [_selectedDate, setSelectedDate] = useState<Date>(new Date());
  return (
    <section className='flex-1 flex flex-col gap-4 py-4 mr-4 md:mr-8'>
      <EventCarousel />
      <DatePicker onSelected={setSelectedDate} />
      <EventListing />
    </section>
  );
}
