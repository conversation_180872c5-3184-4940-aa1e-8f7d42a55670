import { env } from '@/env.mjs';
import { EventCheckoutPage } from '@/components/events';
import { Metadata } from 'next';
import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';
import { apiGetEventDetails } from '@/api/events/requests';

export async function generateMetadata({
  params: { slug },
}: {
  params: { slug: string };
}): Promise<Metadata> {
  try {
    const { data } = await apiGetEventDetails({ slug });
    const event = data.data;
    if (!event) {
      return {};
    }

    return {
      title: `${event.title} - Popla`,
      description: event.description,
      openGraph: {
        title: event.title,
        description: event.description,
        url: `${env.NEXT_PUBLIC_APP_BASE_URL}/events/${slug}`,
        images: [
          {
            url: event.bannerUrl,
            width: 800,
            height: 600,
            alt: `${event.title} Image`,
          },
        ],
      },
      twitter: {
        card: 'summary_large_image',
        title: event.title,
        description: event.description,
        images: [event.bannerUrl],
      },
    };
  } catch (error) {
    return {};
  }
}

export default async function EventSlugCheckoutPage({
  params: { slug },
  searchParams,
}: {
  params: { slug: string };
  searchParams: {
    [key: string]: string | string[] | undefined;
  };
}) {
  const trxref = searchParams.trxref;
  const cookieStore = cookies();
  const user = cookieStore.get('user')?.value;
  const userId = user ? JSON.parse(user).id : undefined;

  if (trxref) {
    const tranResponse = await fetch(
      `${env.PAYSTACK_BASE_URL}/transaction/verify/${trxref}`,
      {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${env.PAYSTACK_SECRET_KEY}`,
          'Content-Type': 'application/json',
        },
      }
    );

    const tranData = await tranResponse.json();

    if (tranResponse.ok) {
      if (tranData.data.status === 'success') {
        if (userId) {
          redirect('/my-tickets');
        } else {
          const notify = true;
        }
      }
    }
  }

  try {
    const { data } = await apiGetEventDetails({ slug });
    const event = data.data;
    if (!event) {
      return (
        <div className='flex flex-1 justify-center items-center'>
          Event not found
        </div>
      );
    }
    return <EventCheckoutPage event={event} />;
  } catch (error) {
    return (
      <div className='flex flex-1 justify-center items-center'>
        Error loading data
      </div>
    );
  }
}
