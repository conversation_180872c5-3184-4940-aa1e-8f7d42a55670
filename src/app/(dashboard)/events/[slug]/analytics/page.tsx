import { apiGetEventDetails } from '@/api/events/requests';
import { EventAnalyticsPage } from '@/features/event/event-analytics';
import { UserObjectData } from '@/types';
import { cookies } from 'next/headers';

export default async function AnalyticsPage({
  params: { slug },
}: {
  params: { slug: string };
}) {
  try {
    const response = await apiGetEventDetails({ slug });
    const {
      data: { data },
    } = response;

    const cookieStore = cookies();
    const user = cookieStore.get('user')?.value;
    const userData = JSON.parse(user || '') as UserObjectData;

    if (data.organizerId !== userData.id) {
      return (
        <div className='flex flex-1 justify-center items-center'>
          Page not available
        </div>
      );
    }

    return <EventAnalyticsPage event={data} userId={userData.id} />;
  } catch (error) {
    console.log(error);
    return (
      <div className='flex flex-1 justify-center items-center'>
        Error loading data
      </div>
    );
  }
}
