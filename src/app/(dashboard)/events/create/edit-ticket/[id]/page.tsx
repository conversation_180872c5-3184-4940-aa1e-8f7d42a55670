import { Ionicons } from '@expo/vector-icons';
import { BottomSheetView } from '@gorhom/bottom-sheet';
import { useLocalSearchParams, useRouter } from 'expo-router';
import React, { useEffect, useRef, useState } from 'react';
import { useFormContext } from 'react-hook-form';
import DatePicker from 'react-native-modal-datetime-picker';

import { CreateEventLayout } from '@/components/layouts/create-event-layout';
import {
  Button,
  ControlledInput,
  ControlledInput,
  CostSelector,
  FeatureToggle,
  Modal,
  P,
  a,
  ScrollView,
  semanticColors,
  Text,
  a,
  useModal,
  View,
} from '@/components/ui';
import {
  type CreateEventFormType,
  useFieldBlurAndFilled,
} from '@/lib/constants';
import { formatDate, formatDateTime } from '@/lib/utils/formatDateTime';

export default function EditTicket() {
  const { watch, subscribe, control, setValue, getValues } =
    useFormContext<CreateEventFormType>();

  const router = useRouter();
  const { id } = useLocalSearchParams<{ id: string }>();

  const ticketIndex = watch('tickets')?.findIndex((ticket) => ticket.id === id);

  const { handleFieldBlur, handleFieldUnBlur } =
    useFieldBlurAndFilled<CreateEventFormType>([
      `tickets.${ticketIndex}.name`,
      `tickets.${ticketIndex}.price`,
      `tickets.${ticketIndex}.quantity`,
      `tickets.${ticketIndex}.presaleName`,
      `tickets.${ticketIndex}.presalePrice`,
      `tickets.${ticketIndex}.presaleQuantity`,
      `tickets.${ticketIndex}.presaleStartDatetime`,
      `tickets.${ticketIndex}.presaleEndDatetime`,
      `tickets.${ticketIndex}.purchaseLimit`,
      `tickets.${ticketIndex}.description`,
      `tickets.${ticketIndex}.startDatetime`,
      `tickets.${ticketIndex}.endDatetime`,
    ]);

  //reusable date functions
  const isStartDateValid = (date: Date) => {
    const now = new Date();
    return date >= now;
  };

  const isEndDateValid = (startDate: Date, endDate: Date) => {
    return endDate >= startDate;
  };

  // can enable all features
  const name = watch(`tickets.${ticketIndex}.name`);
  const price = watch(`tickets.${ticketIndex}.price`);
  const quantity = watch(`tickets.${ticketIndex}.quantity`);
  const canEnable = name && price && quantity;

  // presale toggle and modal
  const [hasPresale, setHasPresale] = useState(
    watch(`tickets.${ticketIndex}.hasPresale`)
  );
  const presaleModal = useModal();
  function enablePresale() {
    setHasPresale(true);
    setValue(`tickets.${ticketIndex}.hasPresale`, true);
    setValue(`tickets.${ticketIndex}.presalePrice`, 0);
    setValue(`tickets.${ticketIndex}.presaleQuantity`, 0);
    setValue(
      `tickets.${ticketIndex}.presaleName`,
      getValues('tickets.0.name') + ' - '
    );
    setValue(`tickets.${ticketIndex}.presaleDescription`, '');
  }

  function disablePresale() {
    if (watch(`tickets.${ticketIndex}.hasPresale`)) {
      setHasPresale(false);
      setValue(`tickets.${ticketIndex}.hasPresale`, false, {
        shouldValidate: true,
      });
    }
  }

  const togglePresale = (checked: boolean) => {
    if (canEnable && checked) {
      enablePresale();
      presaleModal.present();
    } else {
      disablePresale();
      presaleModal.dismiss();
    }
  };

  const handleAddPresale = () => {
    presaleModal.dismiss();
  };

  const handlePresaleModalDismiss = () => {
    if (
      !watch(`tickets.${ticketIndex}.presaleName`) ||
      !watch(`tickets.${ticketIndex}.presalePrice`) ||
      !watch(`tickets.${ticketIndex}.presaleStartDatetime`) ||
      !watch(`tickets.${ticketIndex}.presaleEndDatetime`) ||
      !watch(`tickets.${ticketIndex}.presaleQuantity`)
    ) {
      disablePresale();
    }
  };

  const presaleNameTouched = useRef(false);

  useEffect(() => {
    const unsub = subscribe({
      name: `tickets.${ticketIndex}.name`,
      callback: ({ values }) => {
        const ticket = values.tickets?.[0];
        if (ticket?.hasPresale) {
          const name = ticket.name;
          const currentPresaleName =
            'presaleName' in ticket ? ticket.presaleName : undefined;
          const generatedPresaleName = name ? `${name} - ` : '';

          if (
            name &&
            !presaleNameTouched.current &&
            currentPresaleName !== generatedPresaleName
          ) {
            setValue(
              `tickets.${ticketIndex}.presaleName`,
              generatedPresaleName
            );
          }
        }
      },
    });

    return () => unsub();
  }, [subscribe, setValue]);

  const eventEnd = watch('endDatetime');

  // presale datetimes
  const [openPresaleStartDatetime, setOpenPresaleStartDatetime] =
    useState(false);
  const [openPresaleEndDatetime, setOpenPresaleEndDatetime] = useState(false);

  const presaleStartDatetime = watch(
    `tickets.${ticketIndex}.presaleStartDatetime`
  );

  const presaleStartDatetimeLabel = presaleStartDatetime
    ? formatDateTime(presaleStartDatetime)
    : 'Start date and time';

  const presaleEndDatetime = watch(`tickets.${ticketIndex}.presaleEndDatetime`);

  const presaleEndDatetimeLabel = presaleEndDatetime
    ? formatDateTime(presaleEndDatetime)
    : 'End date and time';

  const handlePresaleStartDateConfirm = (date: Date) => {
    if (!isStartDateValid(date)) {
      alert('Start date cannot be in the past.');
      return;
    }
    setValue(`tickets.${ticketIndex}.presaleStartDatetime`, date);
    setOpenPresaleStartDatetime(false);
  };

  const handlePresaleEndDateConfirm = (date: Date) => {
    const start = watch(`tickets.${ticketIndex}.presaleStartDatetime`);
    if (!start || !isEndDateValid(new Date(start), date)) {
      alert('End date cannot be before the start date.');
      return;
    }

    if (eventEnd && date > new Date(eventEnd)) {
      alert('Ticket end date cannot be after event end date.');
      return;
    }

    setValue(`tickets.${ticketIndex}.presaleEndDatetime`, date);
    setOpenPresaleEndDatetime(false);
  };

  //timeline
  const timelineModal = useModal();
  const [hasTimeline, setHasTimeline] = useState(
    watch(`tickets.${ticketIndex}.hasTimeline`)
  );

  const handleAddTimeline = () => {
    timelineModal.dismiss();
  };

  const handleTimelineModalDismiss = () => {
    if (
      !watch(`tickets.${ticketIndex}.startDatetime`) ||
      !watch(`tickets.${ticketIndex}.endDatetime`)
    ) {
      setHasTimeline(false);
      setValue(`tickets.${ticketIndex}.hasTimeline`, false);
    }
  };

  const toggleTimeline = (checked: boolean) => {
    if (canEnable && checked) {
      setHasTimeline(true);
      setValue(`tickets.${ticketIndex}.hasTimeline`, true);
      timelineModal.present();
    } else {
      setHasTimeline(false);
      setValue(`tickets.${ticketIndex}.hasTimeline`, false);
    }
  };

  // timeline datetimes
  const [openStartDatetime, setOpenStartDatetime] = useState(false);
  const [openEndDatetime, setOpenEndDatetime] = useState(false);

  const startDatetime = watch(`tickets.${ticketIndex}.startDatetime`);

  const startDatetimeLabel = startDatetime
    ? formatDateTime(startDatetime)
    : 'Start date and time';

  const endDatetime = watch(`tickets.${ticketIndex}.endDatetime`);

  const endDatetimeLabel = endDatetime
    ? formatDateTime(endDatetime)
    : 'End date and time';

  const handleStartDateConfirm = (date: Date) => {
    if (!isStartDateValid(date)) {
      alert('Start date cannot be in the past.');
      return;
    }
    setValue(`tickets.${ticketIndex}.startDatetime`, date);
    setOpenStartDatetime(false);
  };

  const handleEndDateConfirm = (date: Date) => {
    const start = watch(`tickets.${ticketIndex}.startDatetime`);
    if (!start || !isEndDateValid(new Date(start), date)) {
      alert('End date cannot be before the start date.');
      return;
    }
    setValue(`tickets.${ticketIndex}.endDatetime`, date);
    setOpenEndDatetime(false);
  };

  //Purchase limit
  const [localPurchaseLimit, setLocalPurchaseLimit] = useState(
    watch(`tickets.${ticketIndex}.purchaseLimit`) || 10
  );
  const [hasPurchaseLimit, setHasPurchaseLimit] = useState(
    watch(`tickets.${ticketIndex}.hasPurchaseLimit`)
  );
  const purchaseLimitModal = useModal();

  const handleAddPurchaseLimit = () => {
    purchaseLimitModal.dismiss();
  };

  const handlePurchaseLimitModalDismiss = () => {
    if (!watch(`tickets.${ticketIndex}.purchaseLimit`)) {
      setHasPurchaseLimit(false);
      setValue(`tickets.${ticketIndex}.hasPurchaseLimit`, false);
    }
  };

  const togglePurchaseLimit = (checked: boolean) => {
    if (canEnable && checked) {
      setHasPurchaseLimit(true);
      setValue(`tickets.${ticketIndex}.hasPurchaseLimit`, true);
      purchaseLimitModal.present();
    } else {
      setHasPurchaseLimit(false);
      setValue(`tickets.${ticketIndex}.hasPurchaseLimit`, false);
    }
  };

  // Presale purchase limit
  //Purchase limit
  const [localPresalePurchaseLimit, setLocalPresalePurchaseLimit] = useState(
    watch(`tickets.${ticketIndex}.presalePurchaseLimit`) || 10
  );

  if (ticketIndex === -1) return;

  return (
    <CreateEventLayout
      title='Edit Ticket'
      subTitle='Make changes to each ticket category.'
      footer={
        <Button
          testID='ticket-update-button'
          label='Update Ticket'
          className='m-4'
          disabled={
            !watch(`tickets.${ticketIndex}.name`) ||
            !watch(`tickets.${ticketIndex}.price`) ||
            !watch(`tickets.${ticketIndex}.quantity`)
          }
          onPress={() => router.push('/events/create/tickets')}
        />
      }
    >
      <ScrollView showsVerticalScrollIndicator={false}>
        <div className='gap-4'>
          <ControlledInput
            name={`tickets.${ticketIndex}.name`}
            label='Ticket name e.g General admission, VIP'
            handleFieldBlur={() =>
              handleFieldBlur(`tickets.${ticketIndex}.name`)
            }
            handleFieldUnBlur={() =>
              handleFieldUnBlur(`tickets.${ticketIndex}.name`)
            }
            control={control}
          />
          <CostSelector
            control={control}
            setValue={setValue}
            handleFieldBlur={() =>
              handleFieldBlur(`tickets.${ticketIndex}.price`)
            }
            handleFieldUnBlur={() =>
              handleFieldUnBlur(`tickets.${ticketIndex}.price`)
            }
            name={`tickets.${ticketIndex}.price`}
            label='Price'
            costOptions={[0, 10000, 20000, 50000]}
            testID='cost-input'
          />
          <ControlledInput
            name={`tickets.${ticketIndex}.quantity`}
            label='Quantity available'
            handleFieldBlur={() =>
              handleFieldBlur(`tickets.${ticketIndex}.quantity`)
            }
            handleFieldUnBlur={() =>
              handleFieldUnBlur(`tickets.${ticketIndex}.quantity`)
            }
            control={control}
          />
          <ControlledInput
            name={`tickets.${ticketIndex}.description`}
            label='Ticket description (optional)'
            handleFieldBlur={() =>
              handleFieldBlur(`tickets.${ticketIndex}.description`)
            }
            handleFieldUnBlur={() =>
              handleFieldUnBlur(`tickets.${ticketIndex}.description`)
            }
            control={control}
          />
          <FeatureToggle
            title='Enable purchase limit'
            subtitle='Limit ticket per order, default uses a maximum of 10'
            checked={hasPurchaseLimit}
            onChange={togglePurchaseLimit}
            {...(hasPurchaseLimit && {
              editText: `Maximum of ${localPurchaseLimit} ticket(s)`,
              onEditPress: () => purchaseLimitModal.present(),
            })}
            accessibilityLabel='Enable purchase limit'
          />
          <FeatureToggle
            title='Include ticket sale timeline'
            subtitle='Set specific period for ticket sale, default uses event date and time'
            checked={hasTimeline}
            onChange={toggleTimeline}
            {...(watch(`tickets.${ticketIndex}.hasTimeline`) &&
              startDatetime &&
              endDatetime && {
                editText: `${formatDate(startDatetime)} - ${formatDate(
                  endDatetime
                )}`,
                onEditPress: () => timelineModal.present(),
              })}
            accessibilityLabel='Include ticket sale timeline'
          />
          <FeatureToggle
            title='Offer ticket presale'
            checked={hasPresale}
            onChange={togglePresale}
            accessibilityLabel='Offer ticket presale'
            {...(watch(`tickets.${ticketIndex}.hasPresale`) && {
              editText: 'Edit presale ticket',
              onEditPress: () => presaleModal.present(),
            })}
          />
        </div>
      </ScrollView>
      <Modal
        ref={presaleModal.ref}
        enableDynamicSizing
        onDismiss={handlePresaleModalDismiss}
      >
        <BottomSheetView className='min-h-30 pb-6'>
          <div className='gap-4 px-4'>
            <div className='flex-row items-center justify-start gap-2'>
              <a
                onPress={presaleModal.dismiss}
                className=''
                accessibilityLabel='Close select modal'
                accessibilityRole='button'
              >
                <Ionicons
                  name='close'
                  size={32}
                  color={semanticColors.fg.subtle.dark}
                />
              </a>
              <p className='text-lg font-bold dark:text-neutral-100'>
                Set up presale
              </p>
            </div>
            <ControlledInput
              name={`tickets.${ticketIndex}.presaleName`}
              label='Ticket name e.g General admission, VIP'
              onChangeText={(text) => {
                presaleNameTouched.current = true;
                setValue(`tickets.${ticketIndex}.presaleName`, text);
              }}
              control={control}
            />

            <ControlledInput
              name={`tickets.${ticketIndex}.presalePrice`}
              handleFieldBlur={() =>
                handleFieldBlur(`tickets.${ticketIndex}.presalePrice`)
              }
              handleFieldUnBlur={() =>
                handleFieldUnBlur(`tickets.${ticketIndex}.presalePrice`)
              }
              label='Price'
              control={control}
            />
            <a
              testID='edit-presale-start-date-input'
              className='h-12 flex-row items-center justify-between rounded-md border border-border-subtle-light p-3 dark:border-border-subtle-dark'
              onPress={() => setOpenPresaleStartDatetime(true)}
            >
              <p
                className={
                  presaleStartDatetime
                    ? 'dark:text-neutral-100'
                    : 'text-neutral-400 dark:text-neutral-500'
                }
              >
                {presaleStartDatetimeLabel}
              </p>
            </a>
            <DatePicker
              isVisible={openPresaleStartDatetime}
              mode='datetime'
              onConfirm={handlePresaleStartDateConfirm}
              onCancel={() => setOpenPresaleStartDatetime(false)}
              minimumDate={new Date()}
            />
            <a
              testID='edit-presale-end-date-input'
              className={`h-12 flex-row items-center justify-between rounded-md border p-3 ${
                presaleStartDatetime
                  ? 'border-border-subtle-light dark:border-border-subtle-dark'
                  : 'border-neutral-200 opacity-50 dark:border-neutral-700'
              }`}
              disabled={!presaleStartDatetime}
              onPress={() => {
                if (presaleStartDatetime) setOpenPresaleEndDatetime(true);
              }}
            >
              <p
                className={
                  presaleEndDatetime
                    ? 'dark:text-neutral-100'
                    : 'text-neutral-400 dark:text-neutral-500'
                }
              >
                {presaleEndDatetimeLabel}
              </p>
            </a>
            <DatePicker
              isVisible={openPresaleEndDatetime}
              mode='datetime'
              onConfirm={handlePresaleEndDateConfirm}
              onCancel={() => setOpenPresaleEndDatetime(false)}
              disabled={!presaleStartDatetime}
              minimumDate={presaleStartDatetime}
            />

            <ControlledInput
              name={`tickets.${ticketIndex}.presaleQuantity`}
              label='Quantity available'
              control={control}
              handleFieldBlur={() =>
                handleFieldBlur(`tickets.${ticketIndex}.presaleQuantity`)
              }
              handleFieldUnBlur={() =>
                handleFieldUnBlur(`tickets.${ticketIndex}.presaleQuantity`)
              }
            />
            <ControlledInput
              name={`tickets.${ticketIndex}.presaleDescription`}
              label='Ticket description (optional)'
              control={control}
            />
            <div className='flex-row items-center justify-between'>
              <p className='text-base font-medium dark:text-neutral-100'>
                Number of allowed purchase
              </p>

              <div className='flex-row items-center rounded-full border border-gray-300 px-2 py-1 dark:border-gray-600'>
                <a
                  onPress={() => {
                    if (localPresalePurchaseLimit > 1) {
                      setLocalPresalePurchaseLimit(
                        localPresalePurchaseLimit - 1
                      );
                    }
                  }}
                  accessibilityLabel='Decrease purchase limit'
                >
                  <p className='px-2 text-lg font-bold text-accent-moderate dark:text-accent-moderate'>
                    -
                  </p>
                </a>

                <p className='px-2 text-base font-semibold dark:text-neutral-100'>
                  {localPresalePurchaseLimit}
                </p>

                <a
                  onPress={() => {
                    if (localPresalePurchaseLimit < 10) {
                      setLocalPresalePurchaseLimit(
                        localPresalePurchaseLimit + 1
                      );
                    }
                  }}
                  accessibilityLabel='Increase purchase limit'
                >
                  <p className='px-2 text-lg font-bold text-accent-moderate dark:text-accent-moderate'>
                    +
                  </p>
                </a>
              </div>
            </div>
          </div>
          <Button
            label='Save'
            className='m-4'
            disabled={
              !watch(`tickets.${ticketIndex}.presaleEndDatetime`) ||
              !watch(`tickets.${ticketIndex}.presaleStartDatetime`) ||
              !watch(`tickets.${ticketIndex}.presalePrice`) ||
              !watch(`tickets.${ticketIndex}.presaleQuantity`)
            }
            onPress={handleAddPresale}
          />
        </BottomSheetView>
      </Modal>
      <Modal
        ref={purchaseLimitModal.ref}
        enableDynamicSizing
        onDismiss={handlePurchaseLimitModalDismiss}
      >
        <BottomSheetView className='min-h-10 gap-4 px-4 pb-6'>
          <div className='flex-row items-center justify-start gap-2'>
            <a
              onPress={purchaseLimitModal.dismiss}
              className=''
              accessibilityLabel='Close select modal'
              accessibilityRole='button'
            >
              <Ionicons
                name='close'
                size={32}
                color={semanticColors.fg.subtle.dark}
              />
            </a>
            <p className='text-lg font-bold dark:text-neutral-100'>
              Enable purchase limit
            </p>
          </div>
          <P className='text-sm text-neutral-500 dark:text-neutral-400'>
            Set the maximum number of this ticket type a single buyer can
            include in their order
          </P>
          <div className='flex-row items-center justify-between'>
            <p className='text-base font-medium dark:text-neutral-100'>
              Number of allowed purchase
            </p>

            <div className='flex-row items-center rounded-full border border-gray-300 px-2 py-1 dark:border-gray-600'>
              <a
                onPress={() => {
                  if (localPurchaseLimit > 1) {
                    setLocalPurchaseLimit(localPurchaseLimit - 1);
                  }
                }}
                accessibilityLabel='Decrease purchase limit'
              >
                <p className='px-2 text-lg font-bold text-accent-moderate dark:text-accent-moderate'>
                  -
                </p>
              </a>

              <p className='px-2 text-base font-semibold dark:text-neutral-100'>
                {localPurchaseLimit}
              </p>

              <a
                onPress={() => {
                  if (localPurchaseLimit < 10) {
                    setLocalPurchaseLimit(localPurchaseLimit + 1);
                  }
                }}
                accessibilityLabel='Increase purchase limit'
              >
                <p className='px-2 text-lg font-bold text-accent-moderate dark:text-accent-moderate'>
                  +
                </p>
              </a>
            </div>
          </div>
          <Button
            label='Save'
            className='mt-auto'
            onPress={() => {
              setValue(
                `tickets.${ticketIndex}.purchaseLimit`,
                localPurchaseLimit
              );
              handleAddPurchaseLimit();
            }}
          />
        </BottomSheetView>
      </Modal>
      <Modal
        ref={timelineModal.ref}
        enableDynamicSizing
        onDismiss={handleTimelineModalDismiss}
      >
        <BottomSheetView className='min-h-1 gap-4 px-4 pb-6'>
          <div className='flex-row items-center justify-start gap-2'>
            <a
              onPress={timelineModal.dismiss}
              className=''
              accessibilityLabel='Close select modal'
              accessibilityRole='button'
            >
              <Ionicons
                name='close'
                size={32}
                color={semanticColors.fg.subtle.dark}
              />
            </a>
            <p className='text-lg font-bold dark:text-neutral-100'>
              Include ticket sale timeline
            </p>
          </div>
          <P className='text-sm text-neutral-500 dark:text-neutral-400'>
            Set specific period for this ticket type sale
          </P>
          <a
            testID='select-ticket-sale-start-date-time-button'
            className='h-12 flex-row items-center justify-between rounded-md border border-border-subtle-light p-3 dark:border-border-subtle-dark'
            onPress={() => setOpenStartDatetime(true)}
          >
            <p
              className={
                startDatetime
                  ? 'dark:text-neutral-100'
                  : 'text-neutral-400 dark:text-neutral-500'
              }
            >
              {startDatetimeLabel}
            </p>
          </a>
          <DatePicker
            isVisible={openStartDatetime}
            mode='datetime'
            onConfirm={handleStartDateConfirm}
            onCancel={() => setOpenStartDatetime(false)}
            minimumDate={new Date()}
          />
          <a
            testID='select-ticket-sale-end-date-time-button'
            className={`h-12 flex-row items-center justify-between rounded-md border p-3 ${
              startDatetime
                ? 'border-border-subtle-light dark:border-border-subtle-dark'
                : 'border-neutral-200 opacity-50 dark:border-neutral-700'
            }`}
            disabled={!startDatetime}
            onPress={() => {
              if (startDatetime) setOpenEndDatetime(true);
            }}
          >
            <p
              className={
                endDatetime
                  ? 'dark:text-neutral-100'
                  : 'text-neutral-400 dark:text-neutral-500'
              }
            >
              {endDatetimeLabel}
            </p>
          </a>
          <DatePicker
            isVisible={openEndDatetime}
            mode='datetime'
            onConfirm={handleEndDateConfirm}
            onCancel={() => setOpenEndDatetime(false)}
            disabled={!startDatetime}
            minimumDate={startDatetime}
          />
          <Button
            label='Save'
            className='mt-auto'
            onPress={handleAddTimeline}
          />
        </BottomSheetView>
      </Modal>
    </CreateEventLayout>
  );
}
