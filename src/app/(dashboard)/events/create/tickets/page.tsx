'use client';

import { useRouter } from 'next/navigation';
import { useFormContext, useFieldArray } from 'react-hook-form';
import { useState } from 'react';
import { useTheme } from 'next-themes';

import { TicketCard } from '@/components/cards/ticket-card';
import { ConfirmationDialog } from '@/components/dialogs';
import { CreateEventLayout } from '@/components/layouts/create-event-layout';
import { Button } from '@/components/ui';
import { type CreateEventFormType } from '@/lib';

export default function Tickets() {
  const { watch, control } = useFormContext<CreateEventFormType>();
  const router = useRouter();
  const { fields, remove } = useFieldArray({
    control,
    name: 'tickets',
  });

  const [menuVisible, setMenuVisible] = useState<string | number | null>(null);
  const [confirmVisible, setConfirmVisible] = useState(false);
  const [ticketToDelete, setTicketToDelete] = useState<number | null>(null);

  const { resolvedTheme } = useTheme();

  const isDark = resolvedTheme === 'dark';

  return (
    <CreateEventLayout
      title="Set up tickets"
      subTitle="You can create multiple ticket types with varying prices."
      footer={
        <>
          <Button
            label="Previous"
            className="m-4"
            disabled={(watch('tickets')?.length ?? 0) < 2}
            onClick={() => router.push('/events/create/summary')}
          />
          <Button
            label="Next"
            className="m-4"
            disabled={(watch('tickets')?.length ?? 0) < 2}
            onClick={() => router.push('/events/create/summary')}
          />
        </>
      }
    >
      <div onClick={() => setMenuVisible(null)} className="flex-1">
        <div className="flex flex-col gap-4">
          {fields.map((field, index) => {
            if (index === 0) return null;
            const ticket = watch(`tickets.${index}`);
            if (!ticket) return null;
            return (
              <div key={field.id} className="relative gap-4">
                <TicketCard
                  ticket={ticket}
                  fieldId={field.id}
                  isMenuOpen={menuVisible === field.id}
                  setMenuVisible={setMenuVisible}
                  isDark={isDark}
                  onEdit={() => {
                    const id = ticket?.id;
                    router.push(`/events/create/edit-ticket/${id}`);
                    setMenuVisible(null);
                  }}
                  onDelete={() => {
                    setTicketToDelete(index);
                    setConfirmVisible(true);
                    setMenuVisible(null);
                  }}
                />
              </div>
            );
          })}
          <Button
            label="Add new ticket +"
            variant="secondary"
            className="w-[154px] py-2"
            size="sm"
            onClick={() => router.push('/events/create/add-ticket')}
          />
        </div>
      </div>
      <ConfirmationDialog
        visible={confirmVisible}
        message="Are you sure you want to delete this ticket?"
        onCancel={() => {
          setConfirmVisible(false);
          setTicketToDelete(null);
        }}
        onConfirm={() => {
          if (ticketToDelete !== null) {
            remove(ticketToDelete);
          }
          setConfirmVisible(false);
          setTicketToDelete(null);
        }}
      />
    </CreateEventLayout>
  );
}
