'use client';

import { useCallback, useMemo, useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { useQueryClient } from '@tanstack/react-query';
import { useSearchParams, useRouter } from 'next/navigation';
import { toast } from 'react-hot-toast';

import {
  type ISingleEvent,
  useEditEvent,
  useSpotifySearchArtist,
} from '@/api/events';
import { Spinner } from '@/components/ui';
import { type CreateEventFormType, useAuth, useInitializeSpotify } from '@/lib';
import { getSpotifyToken } from '@/lib';
import { SearchPageLayout } from '@/components/layouts';

export default function AddCollaborators() {
  const { watch, setValue } = useFormContext<CreateEventFormType>();
  const queryClient = useQueryClient();
  const {user} = useAuth();

  useInitializeSpotify();
  const spotifyData = getSpotifyToken();

  const [searchQuery, setSearchQuery] = useState('');

  const { data, isLoading } = useSpotifySearchArtist({
    variables: {
      query: searchQuery,
      accessToken: spotifyData?.access_token || '',
    },
    enabled: !!spotifyData?.access_token && !!searchQuery,
  });

  const artists = data?.artists?.items ?? [];
  const selectedCollaborators = useMemo(
    () => watch('collaborators') || [],
    [watch]
  );

  const searchParams = useSearchParams();
  const router = useRouter();

  const isEdit = searchParams.get('isEdit') === 'true';
  const eventSlug = searchParams.get('eventSlug') || undefined;
  const eventId = searchParams.get('eventId') || undefined;

  const cachedEvent: ISingleEvent | undefined = queryClient.getQueryData([
    'getEventWithSlug',
    { slug: eventSlug, targetCurrency: 'NGN', userId: user?.id },
  ]);

  const { mutate: editEvent, isPending } = useEditEvent({
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [
          'getEventWithSlug',
          { slug: eventSlug, targetCurrency: 'NGN', userId: user?.id },
        ],
      });
      toast.success('Event updated successfully');
      router.back();
    },
    onError: (error: any) => toast.error(error.message),
  });

  const selectCollaborator = useCallback(
    (collaborator: { id: string; name: string; avatar: string }) => {
      if (isEdit) {
        const formData = new FormData();
        const artistsList = [
          ...(cachedEvent?.artists || []).map(({ id, avatar, artistName }) => ({
            id,
            name: artistName,
            avatar,
          })),
          collaborator,
        ];
        artistsList
          .map(({ id, avatar, name }) => ({
            spotifyArtistId: id,
            avatar,
            artistName: name,
            eventId: eventId || 'evt_123abc',
          }))
          .forEach((artist, index) => {
            Object.entries(artist).forEach(([key, value]) => {
              formData.append(`artists[${index}][${key}]`, value);
            });
          });

        editEvent({ form: formData, id: eventId || '' });
      } else {
        setValue('collaborators', [...selectedCollaborators, collaborator]);
        router.back();
      }
    },
    [
      isEdit,
      cachedEvent,
      editEvent,
      eventId,
      router,
      selectedCollaborators,
      setValue,
    ]
  );

  const filteredCollaborators = Array.isArray(artists)
    ? artists
        .filter(
          (artist: any) =>
            !selectedCollaborators.some((c) => c.id === artist.id)
        )
        .map((artist: any) => ({
          id: artist.id,
          name: artist.name,
          avatar: artist.images?.[0]?.url || '',
        }))
    : [];

  if (isPending || isLoading) return <Spinner />;

  return (
    <SearchPageLayout
      placeholder="Search collaborators"
      searchValue={searchQuery}
      onSearchChange={setSearchQuery}
    >
      <hr className="border-neutral-200 dark:border-neutral-700" />
      <div className="flex-1 space-y-4 px-4 py-4">
        {filteredCollaborators.map((collaborator) => (
          <button
            key={collaborator.id}
            type="button"
            onClick={() => selectCollaborator(collaborator)}
            className="flex items-center gap-3 w-full p-2 hover:bg-neutral-100 dark:hover:bg-neutral-800 rounded-md transition"
          >
            <img
              src={collaborator.avatar}
              alt={collaborator.name}
              className="w-10 h-10 rounded-full object-cover"
            />
            <span className="flex-1 text-sm text-neutral-900 dark:text-neutral-100">
              {collaborator.name}
            </span>
          </button>
        ))}
      </div>
    </SearchPageLayout>
  );
}