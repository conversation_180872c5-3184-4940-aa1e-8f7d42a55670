'use client';
import { useState, useCallback, useEffect, useRef } from 'react';
import { useFormContext } from 'react-hook-form';
import { useRouter } from 'next/navigation';
import ReactCrop, { Crop } from 'react-image-crop';
import 'react-image-crop/dist/ReactCrop.css';
import { useDropzone } from 'react-dropzone';

import { Button } from '@/components/ui';
import type { CreateEventFormType } from '@/lib/hooks';
import { CreateEventLayout } from '@/components/layouts';
import { cn } from '@/lib';

const MAX_MEDIA_COUNT = 2;

type BannerData = {
  uri: string;
  type?: string;
  name?: string | null;
  size?: number;
};

function getCroppedImg(
  image: HTMLImageElement,
  crop: Crop
): Promise<Blob | null> {
  const canvas = document.createElement('canvas');
  const scaleX = image.naturalWidth / image.width;
  const scaleY = image.naturalHeight / image.height;
  canvas.width = crop.width!;
  canvas.height = crop.height!;
  const ctx = canvas.getContext('2d');

  if (!ctx) return Promise.resolve(null);

  ctx.drawImage(
    image,
    crop.x! * scaleX,
    crop.y! * scaleY,
    crop.width! * scaleX,
    crop.height! * scaleY,
    0,
    0,
    crop.width!,
    crop.height!
  );

  return new Promise((resolve) => {
    canvas.toBlob(
      (blob) => {
        resolve(blob);
      },
      'image/jpeg',
      0.8
    );
  });
}

export default function AddBanner() {
  const { watch, setValue } = useFormContext<CreateEventFormType>();
  const router = useRouter();

  const bannerObj = watch('bannerObj');
  const currentMedia = watch('media') || [];

  const [isCroppingBanner, setIsCroppingBanner] = useState(false);
  const [imageToCrop, setImageToCrop] = useState<File | null>(null);
  const [crop, setCrop] = useState<Crop>({
    unit: '%',
    x: 10,
    y: 10,
    width: 80,
    height: 60,
  });
  const imageRef = useRef<HTMLImageElement | null>(null);

  // This effect is not needed as `currentMedia` is watched and used directly.
  // useEffect(() => {
  //   if (currentMedia) {
  //     setValue('media', currentMedia as any);
  //   }
  // }, [currentMedia, setValue]);

  const onMediaDrop = useCallback(
    (acceptedFiles: File[]) => {
      const newItems = acceptedFiles.map((file) => ({
        uri: URL.createObjectURL(file),
        name: file.name,
        size: file.size,
        type: file.type,
      }));
      const updatedMedia = [...currentMedia, ...newItems].slice(
        0,
        MAX_MEDIA_COUNT
      );
      setValue('media', updatedMedia as any);
    },
    [currentMedia, setValue]
  );

  const onBannerDrop = useCallback(
    (acceptedFiles: File[]) => {
      if (acceptedFiles.length === 0) return;
      setImageToCrop(acceptedFiles[0]);
      setIsCroppingBanner(true);
      // If more files exist, add them to the media gallery
      if (acceptedFiles.length > 1) {
        onMediaDrop(acceptedFiles.slice(1, MAX_MEDIA_COUNT + 1));
      }
    },
    [onMediaDrop]
  );

  const {
    getRootProps: getBannerRootProps,
    getInputProps: getBannerInputProps,
    isDragActive: isBannerDragActive,
  } = useDropzone({
    accept: { 'image/*': [] },
    onDrop: onBannerDrop,
    maxFiles: MAX_MEDIA_COUNT + 1, // Allow more files for initial drop
  });

  const {
    getRootProps: getMediaRootProps,
    getInputProps: getMediaInputProps,
    isDragActive: isMediaDragActive,
  } = useDropzone({
    accept: { 'image/*': [] },
    onDrop: onMediaDrop,
    maxFiles: MAX_MEDIA_COUNT - currentMedia.length,
  });

  const saveBanner = useCallback(async () => {
    if (!imageRef.current || !crop.width || !crop.height || !imageToCrop) return;

    const croppedBlob = await getCroppedImg(imageRef.current, crop);
    if (croppedBlob) {
      const file = new File([croppedBlob], imageToCrop.name || 'banner.jpg', {
        type: 'image/jpeg',
      });
      const bannerData: BannerData = {
        uri: URL.createObjectURL(file),
        name: file.name,
        size: file.size,
        type: file.type,
      };
      setValue('bannerObj', bannerData);
      setIsCroppingBanner(false);
      setImageToCrop(null); // Clear the temp state after success
    }
  }, [crop, imageToCrop, setValue]);

  const cancelCrop = useCallback(() => {
    setIsCroppingBanner(false);
    setImageToCrop(null); // Clear the temp state
  }, []);

  const removeMediaAt = useCallback(
    (index: number) => {
      const newMedia = [...currentMedia];
      newMedia.splice(index, 1);
      setValue('media', newMedia);
    },
    [currentMedia, setValue]
  );

  const continueDisabled = !bannerObj?.uri;

  return (
    <CreateEventLayout
      title="Add event image"
      subTitle="Add an event image"
      footer={
        <Button
          label="Continue"
          className="m-4"
          disabled={continueDisabled}
          onClick={() => router.push('/events/create/choose-format')}
        />
      }
    >
      {isCroppingBanner && imageToCrop ? (
        <div className="flex flex-col gap-4">
          <ReactCrop crop={crop} onChange={(c) => setCrop(c)} aspect={4 / 3}>
            <img
              src={URL.createObjectURL(imageToCrop)}
              ref={imageRef}
              alt="Image to crop"
              className="max-w-full h-auto"
            />
          </ReactCrop>
          <div className="flex gap-2">
            <Button label="Save Banner" onClick={saveBanner} />
            <Button label="Cancel" onClick={cancelCrop} />
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          {bannerObj?.uri ? (
            <div className="relative mb-4">
              <img
                src={bannerObj.uri}
                alt="Banner"
                className="w-full rounded-lg object-cover"
                style={{ aspectRatio: '4 / 3' }}
              />
              <Button onClick={() => setIsCroppingBanner(true)}>
                Change Banner
              </Button>
            </div>
          ) : (
            <div
              {...getBannerRootProps()}
              className={cn(
                'flex flex-col border border-dashed p-6 text-center cursor-pointer rounded-lg h-[360px] w-full items-center justify-center',
                isBannerDragActive && 'border-blue-500 bg-blue-50'
              )}
            >
              <input {...getBannerInputProps()} />
              <p className="text-gray-600">Upload photos of your event image</p>
            </div>
          )}

          <div className="flex flex-wrap gap-2">
            {currentMedia.map((file, index) => (
              <div
                key={file.name || file.uri}
                className="relative w-24 h-24 rounded overflow-hidden border"
              >
                <img
                  src={file.uri}
                  alt={`Media ${index + 1}`}
                  className="w-full h-full object-cover"
                />
                <button
                  type="button"
                  onClick={() => removeMediaAt(index)}
                  className="absolute top-0 right-0 bg-red-600 text-white rounded-full w-5 h-5 flex items-center justify-center"
                >
                  ×
                </button>
              </div>
            ))}
            {currentMedia.length < MAX_MEDIA_COUNT && bannerObj?.uri && (
              <div
                {...getMediaRootProps()}
                className={cn(
                  'w-24 h-24 flex flex-col items-center justify-center border border-dashed rounded cursor-pointer',
                  isMediaDragActive && 'border-blue-500 bg-blue-50'
                )}
              >
                <input {...getMediaInputProps()} />
                <span className="text-3xl">+</span>
                <span className="text-xs text-gray-500">
                  Add {MAX_MEDIA_COUNT - currentMedia.length} more photo
                  {MAX_MEDIA_COUNT - currentMedia.length !== 1 ? 's' : ''}
                </span>
              </div>
            )}
          </div>
        </div>
      )}
    </CreateEventLayout>
  );
}