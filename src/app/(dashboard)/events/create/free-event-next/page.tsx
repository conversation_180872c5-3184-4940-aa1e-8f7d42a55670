'use client';
import React from 'react';
import { useFormContext } from 'react-hook-form';
import { useRouter } from 'next/navigation';

import { CreateEventLayout } from '@/components/layouts/create-event-layout';
import { Button, ControlledInput } from '@/components/ui';
import { type CreateEventFormType, useFieldBlurAndFilled } from '@/lib';

export default function FreeEventDetails() {
  const { control, setValue } = useFormContext<CreateEventFormType>();

  const { fieldStates } = useFieldBlurAndFilled<CreateEventFormType>([
    'maxAttendees',
  ]);

  const router = useRouter();

  return (
    <CreateEventLayout
      title="Set up free event"
      subTitle="Choose between requiring attendees to register for the event or just uploading your event."
      footer={
        <Button
          data-testid="go-to-add-banner-page-button"
          label="Continue"
          disabled={!fieldStates.maxAttendees.isValidOptional}
          onClick={() => router.push('/events/create/summary')}
        />
      }
    >
      <div className="gap-4">
        <ControlledInput
          name="maxAttendees"
          label="Max. number of attendees"
          control={control}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
            setValue('maxAttendees', Number(e.target.value))
          }
          type="number"
          inputMode="numeric"
        />
      </div>
    </CreateEventLayout>
  );
}
