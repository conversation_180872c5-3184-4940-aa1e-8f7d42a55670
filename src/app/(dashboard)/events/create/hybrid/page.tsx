import React, { useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useFormContext } from 'react-hook-form';

import { IconComponent } from '@/components/common/icon-component';
import LocationInput from '@/components/ui/location-input';
import { CreateEventLayout } from '@/components/layouts/create-event-layout';
import {
  Button,
  colors,
  ControlledInput,
  H2,
  Image,
  Input,
  Modal,
  P,
  a,
  View,
} from '@/components/ui';
import { useModal } from '@/components/ui/modal';
import { type CreateEventFormType } from '@/lib/constants';
import { type LocationType } from '@/types';

export default function Hybrid() {
  const { watch, control, setValue } = useFormContext<CreateEventFormType>();
  const navigate = useNavigate();

  const onlineModal = useModal();
  const customLinkModal = useModal();
  const generatingLinkModal = useModal();

  const eventUrl = watch('onlineUrl');

  const onSelectLocation = useCallback(
    (
      location: Omit<LocationType, 'coordinates'>,
      locationCoordinate: { lat: number; lng: number }
    ) => {
      const locationObject = {
        ...location,
        coordinates: { ...locationCoordinate },
      };
      setValue('location', locationObject);
    },
    [setValue]
  );

  const getZoomLink = () => {
    onlineModal.dismiss();
    generatingLinkModal.present();

    setTimeout(() => {
      generatingLinkModal.dismiss();
      setTimeout(() => {
        navigate('/events/create/zoom-generated');
      }, 300);
    }, 2000);
  };

  return (
    <CreateEventLayout
      title="Add event address"
      subTitle="Choose the format that suits your event."
      footer={
        <Button
          data-testid="account-selection-button"
          label="Continue"
          className="my-4"
          disabled={!watch('location') || !watch('onlineUrl')}
          onClick={() => navigate('/events/create/add-ticket')}
        />
      }
    >
      <div className="mt-4 gap-4">
        <LocationInput
          onSelectLocation={onSelectLocation}
          defaultValue={watch('location')}
        />
        <ControlledInput
          name="onlineUrl"
          label="Streaming link"
          disabled
          control={control}
          onClick={() => onlineModal.present()}
          icon={
            <IconComponent
              iconType="feather"
              iconName="link"
              size={24}
              className="text-fg-muted dark:text-fg-muted"
            />
          }
        />
      </div>

      <Modal ref={onlineModal.ref} snapPoints={['35%']}>
        <div className="gap-2 p-4">
          <H2 className="text-start">Online events</H2>
          <a
            className="flex-row items-center justify-start gap-4 py-3 cursor-pointer"
            onClick={() => {
              onlineModal.dismiss();
              customLinkModal.present();
            }}
          >
            <div className="bg-accent-subtle dark:bg-accent-subtle size-10 rounded-full p-2">
              <IconComponent iconType="feather" iconName="globe" size={24} />
            </div>
            <P className="flex-1 items-center justify-center">
              Use your own URL
            </P>
          </a>

          <a
            className="flex-row items-center justify-start gap-4 py-3 cursor-pointer"
            onClick={getZoomLink}
          >
            <div className="bg-accent-subtle dark:bg-accent-subtle size-10 rounded-full p-2">
              <IconComponent iconType="custom-svg" iconName="Zoom" size={24} />
            </div>
            <P className="flex-1 items-center justify-center">
              Generate a Zoom link
            </P>
          </a>

          <Button
            label="Skip for now"
            variant="outline"
            onClick={() => navigate('/events/create/choose-ticket-type')}
          />
        </div>
      </Modal>

      <Modal ref={customLinkModal.ref} snapPoints={['30%']}>
        <div className="w-full justify-start gap-2 p-4">
          <div className="flex flex-row justify-start gap-2 px-2 py-4">
            <a
              className="size-8 cursor-pointer"
              onClick={() => {
                customLinkModal.dismiss();
                onlineModal.present();
              }}
              aria-label="Go back"
            >
              <Ionicons
                name="chevron-back"
                size={24}
                color={colors.brand['60']}
                className="text-accent-moderate dark:text-accent-moderate"
              />
            </a>
            <P className="font-bold">Enter event URL</P>
          </div>
          <div className="gap-6">
            <Input
              label="Enter link (URL)"
              value={eventUrl || 'https://'}
              onChange={(e) => setValue('onlineUrl', e.target.value)}
            />

            <Button
              label="Save"
              disabled={!eventUrl}
              onClick={() => {
                customLinkModal.dismiss();
                navigate('/events/create/choose-ticket-type');
              }}
            />
          </div>
        </div>
      </Modal>

      <Modal ref={generatingLinkModal.ref} snapPoints={['40%']}>
        <div className="items-center justify-center gap-2 p-4">
          <div style={{ width: 125, height: 125 }}>
            <Image
              src="/assets/icons/events/loading.png"
              alt="Loading"
              className="size-[105px] self-center p-2.5"
            />
          </div>
          <P className="items-center justify-center text-fg-muted-light dark:text-fg-muted-dark">
            Generating link...
          </P>
        </div>
      </Modal>
    </CreateEventLayout>
  );
}
