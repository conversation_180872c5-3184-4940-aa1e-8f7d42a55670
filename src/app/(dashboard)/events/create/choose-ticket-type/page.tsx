
'use client';
import React from 'react';
import { useFormContext, useFieldArray } from 'react-hook-form';
import { useRouter } from 'next/navigation';
import { GradientBorderCard } from '@/components/cards/gradient-border-card';
import { CreateEventLayout } from '@/components/layouts/create-event-layout';
import { Button } from '@/components/ui';
import { type CreateEventFormType, TICKET_TYPE_CARDS } from '@/lib';

export default function ChooseEventTicketType() {
  const { watch, control, setValue } = useFormContext<CreateEventFormType>();
  const router = useRouter();
  const { fields } = useFieldArray({
    control,
    name: 'tickets',
  });

  const ticketType = watch('ticketType');

  const handleContinue = () => {
    if (!ticketType) return;
    if (ticketType === 'FREE') {
      router.push('/events/create/free-event');
    } else {
      if (fields.length < 2) {
        router.push('/events/create/add-ticket');
      } else {
        router.push('/events/create/tickets');
      }
    }
  };

  return (
    <CreateEventLayout
      title='Will your event be free or paid?'
      subTitle='Choose the ticket type that works best for your event.'
      footer={
        <Button
          data-testid='go-to-free-event-or-add-ticket-page'
          label='Continue'
          className='m-4'
          disabled={!ticketType}
          onClick={handleContinue}
        />
      }
    >
      {TICKET_TYPE_CARDS.map((card) => (
        <GradientBorderCard
          key={card.id}
          isSelected={ticketType === card.id}
          onClick={() => setValue('ticketType', card.id)}
          icon={card.icon}
          title={card.title}
          description={card.description}
        />
      ))}
    </CreateEventLayout>
  );
}
