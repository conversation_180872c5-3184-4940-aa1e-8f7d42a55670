import dayjs from 'dayjs';
import React from 'react';

import { type ITicketExtension } from '@/api/events';
import { useGetUserTickets } from '@/api/users';
import { Back } from '@/components/common/back';
import LoadingScreen from '@/components/loading';
import { UserTicketsTab } from '@/components/tab-views/my-tickets-tab';
import {
  AppTab,
  FocusAwareStatusBar,
  LgBoldLabel,
  SafeAreaView,
  View,
} from '@/components/ui';
import { useLoggedInUser } from '@/lib/constants';
import { type TabScreenItem } from '@/types';

export default function Tickets() {
  const { data: user, isLoading: userLoading } = useLoggedInUser();
  const [index, setIndex] = React.useState(0);

  const { data: ticketsData, isLoading } = useGetUserTickets({
    variables: { id: user?.id || '' },
    enabled: !!user?.id,
  });

  const ticketsList = React.useMemo(() => {
    if (!ticketsData?.eventsWithTicket || !user?.id) return [];

    const uniqueTransactions = new Set<string>();

    return ticketsData.eventsWithTicket.reduce((tickets, event) => {
      const eventTickets = event.tickets
        .filter((ticket) => ticket.userId === user.id)
        .map((ticket) => ({
          ...ticket,
          title: event.title,
          bannerUrl: event.bannerUrl,
          startTime: event.startTime,
          location: event.location,
          organizer: event.organizer,
          eventId: event.id,
          event,
        }))
        .filter((ticket) => {
          if (uniqueTransactions.has(ticket.transactionId)) return false;
          uniqueTransactions.add(ticket.transactionId);
          return true;
        });

      return [...tickets, ...eventTickets];
    }, [] as ITicketExtension[]);
  }, [ticketsData?.eventsWithTicket, user?.id]);

  const { upcomingTickets, pastTickets } = React.useMemo(() => {
    const now = dayjs();

    const upcoming: ITicketExtension[] = [];
    const past: ITicketExtension[] = [];

    ticketsList.forEach((ticket) => {
      const eventEndTime = dayjs(ticket.event.endTime);

      if (eventEndTime.isAfter(now)) {
        upcoming.push(ticket);
      } else {
        past.push(ticket);
      }
    });

    return { upcomingTickets: upcoming, pastTickets: past };
  }, [ticketsList]);

  const tabItems: TabScreenItem[] = React.useMemo(() => {
    return [
      {
        key: 'Upcoming',
        title: 'Upcoming',
        component: () => <UserTicketsTab tickets={upcomingTickets} index={0} />,
      },
      {
        key: 'Past',
        title: 'Past',
        component: () => <UserTicketsTab tickets={pastTickets} index={1} />,
      },
    ];
  }, [upcomingTickets, pastTickets]);

  if (isLoading || userLoading) return <LoadingScreen />;

  return (
    <SafeAreaView className='flex-1 '>
      <FocusAwareStatusBar />
      <div className='h-16 flex-row items-center gap-2 px-2'>
        <Back />
        <LgBoldLabel>Tickets</LgBoldLabel>
      </div>
      <AppTab
        items={tabItems}
        tabIndex={index}
        tabSetIndex={(i) => setIndex(i)}
      />
    </SafeAreaView>
  );
}
