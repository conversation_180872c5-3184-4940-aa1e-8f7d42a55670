import { apiGetEventById } from '@/api/events/requests';
import { TicketDetailsPage } from '@/features/my-tickets/ticket-details';
import { cookies } from 'next/headers';

export default async function TicketDetails({
  params: { id },
}: {
  params: { id: string };
}) {
  const cookieStore = cookies();
  const token = cookieStore.get('token')?.value;

  try {
    const response = await apiGetEventById(id, token || '');
    const {
      data: { data },
    } = response;
    console.log('data', data);
    return <TicketDetailsPage event={data} />;
  } catch (error) {
    return (
      <div className='flex flex-1 justify-center items-center'>
        Error loading data
      </div>
    );
  }
}
