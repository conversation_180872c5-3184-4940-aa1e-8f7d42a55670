import Footer from '@/components/layouts/footer';
import { Nav } from '@/components/nav';
import * as React from 'react';

export default function MyTicketsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <main className='flex flex-1 h-screen flex-col'>
      <Nav />
      <div className='flex flex-col flex-1'>
        <main className='flex justify-center bg-[url("/banner.svg")] bg-cover bg-center bg-no-repeat'>
          <div className='max-w-[1440px] flex flex-1 flex-col px-[25px] py-[20px] md:py-0 md:px-[100px] text-[#EFE6F0]'>
            <div className='flex justify-center py-[5px] md:py-[100px]'>
              <p className='max-w-[610px] text-[20px] md:text-[45px] lg:text-[40px] font-bold md:leading-[1.2] text-center'>
                Your tickets and favourite events in one place
              </p>
            </div>
          </div>
        </main>
        {children}
      </div>
      <Footer />
    </main>
  );
}
