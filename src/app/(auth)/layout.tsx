'use client';
import NextImage from '@/components/ui/NextImage';
import { ModeToggle } from '@/components/ui/theme-toggle';
import { H2 } from '@/components/ui/typography';
import Link from 'next/link';
import PoplaLogo from '~/svg/PoplaSplash.svg';

export default function AuthLayout({
  children,
}: {
  readonly children: React.ReactNode;
}) {
  return (
    <main className='min-h-svh flex flex-row bg-bg-canvas-light dark:bg-bg-canvas-dark'>
      <div className='relative hidden bg-muted lg:flex max-w-[533px] flex-1'>
        <NextImage
          src={
            process.env.NEXT_PUBLIC_URL
              ? `${process.env.NEXT_PUBLIC_URL}/images/onboarding-image.png`
              : '/images/onboarding-image.png'
          }
          alt='Onboarding Image'
          fill
          className='absolute inset-0 object-contain dark:brightness-[0.2] dark:grayscale'
        />
        <Link
          href='/'
          className='absolute h-full top-8 left-8 max-h-[86px] w-full max-w-[205px] text-white'
        >
          <PoplaLogo />
        </Link>
        <H2 className='absolute bottom-8 left-8' weight='bold'>
          Find Your Vibe, Anywhere, Anytime
        </H2>
      </div>
      {children}
      <ModeToggle className='absolute top-6 right-6 size-9' />
    </main>
  );
}
