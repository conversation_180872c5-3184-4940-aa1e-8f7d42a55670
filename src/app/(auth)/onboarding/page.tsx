'use client';

import { FloatingInput } from '@/components/ui/floating-input';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@/components/ui/form';
import { SmBoldLabel } from '@/components/ui/typography';
import { UsernameField } from '@/components/ui/username-input';
import { SelectAccountTypeDialog } from '@/components/auth/dialog/select-account-type';
import { SelectCountry } from '@/components/auth/form/select-country';
import OnboardingLayout from '@/components/auth/layout/onboarding';
import GenreItem from '@/components/genre/item';
import { GENRES } from '@/lib/constants/generic';
import { useAccountOnboarding } from '@/lib/hooks/use-account-onboarding';
import { useMultistepForm } from '@/lib/hooks/use-multi-step-form';
import { USER_ROLE } from '@/api';
import React from 'react';

export default function OnboardingPage() {
  const [accountTypeDialogOpen, setAccountTypeDialogOpen] =
    React.useState(false);
  const [selectedGenres, setSelectedGenres] = React.useState<string[]>([]);

  const {
    formMethods,
    onboardingSteps,
    isValidatingUsername,
    setIsValidatingUsername,
    usernameRefinement,
  } = useAccountOnboarding();

  const {
    currentStep,
    currentStepData,
    goToNext,
    canContinue,
    skip,
    goToStep,
  } = useMultistepForm({
    steps: onboardingSteps,
    form: formMethods,
    onComplete: (data) => {
      console.log('Onboarding completed:', data);
    },
    onOpenAccountTypeDialog: () => setAccountTypeDialogOpen(true),
  });

  const handleGenreToggle = (genreId: string) => {
    let newSelectedGenres;

    if (selectedGenres.includes(genreId)) {
      newSelectedGenres = selectedGenres.filter((id) => id !== genreId);
    } else {
      newSelectedGenres = [...selectedGenres, genreId];
    }
    setSelectedGenres(newSelectedGenres);
    formMethods.setValue('genres', newSelectedGenres, {
      shouldValidate: true,
      shouldDirty: true,
    });
  };

  const handleAccountTypeSelect = (accountType: USER_ROLE) => {
    formMethods.setValue('role', accountType);
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <div className='flex flex-1 flex-col gap-10'>
            <div className='flex flex-col gap-4'>
              <SmBoldLabel themed weight='bold'>
                What should we call you?
              </SmBoldLabel>
              <div className='flex flex-col sm:flex-row gap-4'>
                <FormField
                  control={formMethods.control}
                  name='firstName'
                  render={({ field }) => (
                    <FormItem className='flex-1'>
                      <FormControl>
                        <FloatingInput label='First name' {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={formMethods.control}
                  name='lastName'
                  render={({ field }) => (
                    <FormItem className='flex-1'>
                      <FormControl>
                        <FloatingInput label='Last name' {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <div className='flex flex-col gap-4'>
              <SmBoldLabel themed weight='bold'>
                What's your location?
              </SmBoldLabel>
              <FormField
                control={formMethods.control}
                name='country'
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <SelectCountry {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className='flex flex-col gap-4'>
              <SmBoldLabel themed weight='bold'>
                Choose your Popla Username
              </SmBoldLabel>
              <UsernameField
                control={formMethods.control}
                name='username'
                isValidating={isValidatingUsername}
                onValidationStart={() => setIsValidatingUsername(true)}
                onUsernameValidationInvalidate={() =>
                  usernameRefinement.invalidate()
                }
              />
            </div>
          </div>
        );

      case 1:
        return (
          <div className='flex flex-1 flex-col gap-6'>
            <div className='flex flex-row flex-wrap gap-x-4 gap-y-6 max-w-[669px]'>
              {GENRES.map(({ id, image, name }) => (
                <GenreItem
                  genre={name}
                  key={id}
                  imageSource={image}
                  isSelected={selectedGenres.includes(id)}
                  onToggle={() => handleGenreToggle(id)}
                />
              ))}
            </div>
            {formMethods.formState.errors.genres && (
              <p className='text-sm text-red-500 mt-2'>
                {formMethods.formState.errors.genres.message}
              </p>
            )}
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Form {...formMethods}>
      <OnboardingLayout
        title={currentStepData.title}
        subTitle={currentStepData.subTitle}
        showSkip={currentStepData.showSkip}
        currentStep={currentStep}
        totalSteps={onboardingSteps.length}
        handleContinue={goToNext}
        handleSkip={skip}
        canContinue={canContinue}
        goToStep={goToStep}
      >
        {renderStepContent()}
      </OnboardingLayout>

      <SelectAccountTypeDialog
        open={accountTypeDialogOpen}
        setOpen={setAccountTypeDialogOpen}
        accountType={formMethods.watch('role')}
        onSelectAccountType={handleAccountTypeSelect}
        onProceed={() => goToStep(1)}
      />
    </Form>
  );
}
