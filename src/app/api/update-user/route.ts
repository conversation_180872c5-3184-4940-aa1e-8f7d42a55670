import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { env } from '@/env.mjs';

export async function GET() {
  const token = cookies().get('token')?.value;

  if (!token) {
    return NextResponse.json({ message: 'Unauthorized: No token found' }, { status: 401 });
  }

  try {
    // Fetch user info from external API
    const apiResponse = await fetch(`${env.NEXT_PUBLIC_API_BASE_URL}/users/me`, {
      headers: { Authorization: `Bearer ${token}` },
    });

    if (!apiResponse.ok) {
      return NextResponse.json({ message: 'Failed to fetch user info' }, { status: apiResponse.status });
    }
    const data = await apiResponse.json();
    const user = data.data

    // Create a response object
    const response = NextResponse.json({ user });

    // Set the user cookie
    response.cookies.set('user', JSON.stringify(user), {
      httpOnly: true,
      path: '/',
      sameSite: 'none',
      secure: true,
    });
    
    return response;
  } catch (error) {
    console.error('Error fetching user:', error);
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
  }
}
