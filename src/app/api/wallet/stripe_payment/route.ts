import { NextRequest, NextResponse } from 'next/server';
import <PERSON>e from 'stripe';
import { env } from '@/env.mjs';

const stripe = new Stripe(process.env.NEXT_PUBLIC_STRIPE_SECRET_KEY!);

export async function POST(req: NextRequest) {
  try {
    const { email, amount, userId, currency } = await req.json(); // Accept multiple tickets

    // Format tickets for Stripe
    const line_items = [{
      price_data: {
        currency: currency,
        product_data: { name: "Wallet top up" },
        unit_amount: amount,
      },
      quantity: 1
    }]

    // Create Stripe Checkout Session
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'], // Supports Apple Pay & Google Pay
      line_items,
      mode: 'payment',
      success_url: `${env.NEXT_PUBLIC_APP_BASE_URL}/finance`,
      cancel_url: `${env.NEXT_PUBLIC_APP_BASE_URL}/finance`,
      metadata: {
        email,
        amount,
        purpose: 'WALLET_TO_UP',
        userId: userId,
      },
    });

    return NextResponse.json({ url: session.url });
  } catch (error: any) {
    console.error('Error creating Stripe session:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
