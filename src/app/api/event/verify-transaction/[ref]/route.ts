import ky from 'ky';
import { NextResponse } from 'next/server';
import { z } from 'zod';

// import { TicketPurchaseTransactionStatus } from '@/app/event/[slug]/event';
import { env } from '@/env.mjs';

export async function GET(
  request: Request,
  { params }: { params: { ref: string } }
) {

  try {
    const response = await fetch(`${env.PAYSTACK_BASE_URL}/transaction/verify/${params.ref}`, {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${env.PAYSTACK_SECRET_KEY}`,
        'Content-Type': 'application/json',
      },
    });

    const result = await response.json();

    if (!result.status) {
      return NextResponse.json({
        status: false,
        message: result.message,
        data: null,
      });
    }

    return NextResponse.json({
      status: true,
      message: 'Successfully fetched paystack transaction status',
      data: result.data,
    });
  } catch (error) {
    return NextResponse.json({
      status: false,
      message: error,
      data: null,
    });
  }
}
