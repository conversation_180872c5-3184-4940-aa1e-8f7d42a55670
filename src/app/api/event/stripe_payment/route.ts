import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { env } from '@/env.mjs';

const stripe = new Stripe(process.env.NEXT_PUBLIC_STRIPE_SECRET_KEY!);

export async function POST(req: NextRequest) {
  try {
    const { email, slug, amount, userId, eventId, tickets } = await req.json(); // Accept multiple tickets

    if (!tickets || tickets.length === 0) {
      return NextResponse.json({ error: 'No tickets selected' }, { status: 400 });
    }

    // Format tickets for Stripe
    const line_items = tickets.map((ticket: { category: string; cost: number; quantity: number }) => ({
      price_data: {
        currency: 'usd',
        product_data: { name: ticket.category },
        unit_amount: ticket.cost * 100, // Convert dollars to cents
      },
      quantity: ticket.quantity,
    }));

    // Create Stripe Checkout Session
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'], // Supports Apple Pay & Google Pay
      line_items,
      mode: 'payment',
      success_url: `${env.NEXT_PUBLIC_APP_BASE_URL}/success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${env.NEXT_PUBLIC_APP_BASE_URL}/event/${slug}`,
      metadata: {
        email,
        amount,
        eventId,
        purpose: 'EVENT_TICKET_PURCHASE',
        userId: userId || "",
        tickets: JSON.stringify(tickets),
      },
    });

    return NextResponse.json({ url: session.url });
  } catch (error: any) {
    console.error('Error creating Stripe session:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
