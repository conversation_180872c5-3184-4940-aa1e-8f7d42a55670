import { NextResponse } from 'next/server';

export async function GET(request: Request) {
  // Your logic here
  return NextResponse.json([
    {
      relation: ['delegate_permission/common.handle_all_urls'],
      target: {
        namespace: 'android_app',
        package_name: 'com.popla.app',
        sha256_cert_fingerprints: [
          '27:85:3D:EE:C2:FE:FD:0F:5A:F4:D8:45:CB:A3:5F:44:6F:B1:BB:FF:C2:93:27:6B:2C:B5:67:34:37:90:B0:8C',
        ],
      },
    },
    {
      relation: ['delegate_permission/common.handle_all_urls'],
      target: {
        namespace: 'android_app',
        package_name: 'com.popla.app.staging',
        sha256_cert_fingerprints: [
          '4E:97:2A:7D:B8:B8:78:FA:A7:19:E2:67:28:4C:07:E6:89:92:B2:27:85:56:D9:62:3C:5C:D4:6C:9D:2D:21:CD',
        ],
      },
    },
    {
      relation: ['delegate_permission/common.handle_all_urls'],
      target: {
        namespace: 'android_app',
        package_name: 'com.popla.app.staging',
        sha256_cert_fingerprints: [
          'FA:C6:17:45:DC:09:03:78:6F:B9:ED:E6:2A:96:2B:39:9F:73:48:F0:BB:6F:89:9B:83:32:66:75:91:03:3B:9C',
        ],
      },
    },
  ]);
}
