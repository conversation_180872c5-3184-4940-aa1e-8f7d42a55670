import Footer from '@/components/landing/footer';
import Navigation from '@/components/landing/navigation';
import React from 'react';

const LayoutOurFeatures = ({ children }: { children: React.ReactNode }) => {
  return (
    <main className='flex flex-1 flex-col bg-black'>
      <Navigation className='mx-1.5 py-4 rounded-t-[32px] bg-[#0A0A0A]' />
      <div className='flex flex-col flex-1  pt-[192px]'>{children}</div>
      <Footer />
    </main>
  );
};

export default LayoutOurFeatures;
