'use client';
import { useEffect } from 'react';

const Redirect: React.FC = () => {
  useEffect(() => {
    const userAgent = navigator.userAgent;

    console.log('User agent detected:', userAgent); // Debugging line

    if (/iPhone|iPad|iPod/.test(userAgent)) {
      console.log('Redirecting to iOS app');
      window.location.href = 'https://apps.apple.com/ng/app/popla/id6474488670';
    } else if (/Android/.test(userAgent)) {
      console.log('Redirecting to Android app');
      window.location.href =
        'https://play.google.com/store/apps/details?id=com.popla.app&pli=1';
    } else {
      console.log('Redirecting to default page');
      window.location.href = 'https://getpopla.com/';
    }
  }, []);

  return <div>Redirecting...</div>; // Optional loading message
};

export default Redirect;
