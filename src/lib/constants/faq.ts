type FaqObject = {
  question: string;
  answer: string;
};
export const general: FaqObject[] = [
  {
    question: 'How do I top up balance?',
    answer:
      'How do I top up balance? You can top up your balance by using the ‘Top-up’ button on the ‘Finance’ tab found in your ‘Profile’ and our payment providers would handle the rest.',
  },
  {
    question: 'How do I make a song request on Popla?',
    answer:
      'You can do this by joining a Live Session and using the ‘Make a song request’ button. Search for your preferred song and send your request. Make sure you have enough balance to cover the request fee. ',
  },
  {
    question: 'How do I buy a ticket to an event?',
    answer:
      'Search for the event you want to attend and select the ‘Get Ticket’ button. You can purchase a ticket to an event either with your wallet balance or direct payment.',
  },
];

export const user: FaqObject[] = [
  {
    question: 'Is there a way to prioritize my song request?',
    answer:
      'Yes, there is. You can prioritize your song request by clicking the “Play Now” button which is represented by a bolt icon in your song request queue. Note: This charges x2 the request cost.',
  },
  {
    question: 'Can I cancel a song request?',
    answer:
      'Yes, you can. A song request can be cancelled if it has not been accepted. This is done by holding down the song in your request queue then a “cancel” button will pop up.',
  },
  {
    question:
      'What happens if my song is not played but it is marked as played?',
    answer:
      'A complaint should be made on the app within 24 hours. This can be done by using ‘Report an issue’ on your profile, select ‘Live Session’ from the dropdown and our support team will swing into action to review and get you a refund.',
  },
];

export const dj_organizer: FaqObject[] = [
  {
    question: 'How do I host an event?',
    answer:
      'This feature is exclusive to an ‘Organizer’ or ‘Band, DJ or Venue’ and can be done by locating the Event tab and the Create Event button. Fill in your event details and you’re ready to host your next event.',
  },
  {
    question: 'How do I verify ticket holders?',
    answer:
      'You can verify ticket holders using the Popla app ‘Verify Ticket’ button, found on the event screen as the creator. Please allow access to camera to enable scanning of ticket QR codes.',
  },
  {
    question: 'How do I get paid from Popla?',
    answer:
      'A request for payment can be made at the end of an event or a live session by clicking the ‘Withdrawal’ button on the ‘Profile’ and payment will be received within 0 to 48 hours.',
  },
];

export const extraContent: FaqObject[] = [
  {
    question: 'What is Popla?',
    answer:
      'Popla is a unique service that helps you request for songs. Whether from Bands or DJs playing around you, to entertainment venues, our unique song request feature is built to help you vibe your way! It doesn’t end there; you can also browse through events happening around you daily and purchase tickets from the app.',
  },
  {
    question: 'How do I receive song requests? ',
    answer:
      'To receive song requests, you must create an account as a ‘Band, DJ, or Venue’. Once created, use the ‘Go Live’ icon top left on the Popla App home screen. Set your Live Session details, price per song request and you’re ready to receive requests. ',
  },
  {
    question: 'How do I create an event?',
    answer:
      'To create an event, you must create an account as an ‘Organizer’ or ‘Band, DJ or Venue’. Once created, locate the Event tab and the Create Event button. Fill in your event details and you’re ready to host your next event.',
  },
];
