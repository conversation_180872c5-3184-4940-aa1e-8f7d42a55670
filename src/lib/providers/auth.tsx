'use client';
import {
  createContext,
  useContext,
  ReactNode,
  useState,
  useEffect,
  Dispatch,
  SetStateAction,
} from 'react';
import { useRouter } from 'next/navigation';
import { signIn, signOut, useSession } from 'next-auth/react';
import type { UserObjectData } from '@/api';

interface SelectedWallet {
  id: string;
  country: string;
  currency: string;
  countryCode: string;
  balance: number;
}

interface AuthContextType {
  isAuthenticated: boolean;
  login: (provider?: 'credentials' | 'google' | 'apple', payload?: any) => Promise<void>;
  logout: () => Promise<void>;
  setSelectedWallet: Dispatch<SetStateAction<SelectedWallet | undefined>>;
  user?: UserObjectData | null;
  selectedWallet?: SelectedWallet;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider = ({ children }: AuthProviderProps) => {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [selectedWallet, setSelectedWallet] = useState<SelectedWallet>();

  // Load wallet from localStorage
  useEffect(() => {
  if (typeof window !== 'undefined') {
    const wallet = localStorage.getItem('selectedWallet');
    if (wallet) {
      setSelectedWallet(JSON.parse(wallet));
    } else if (session?.user?.wallets?.length) {
      setSelectedWallet(session.user.wallets[0]);
    }
  }
}, [session?.user]);

  // Persist wallet to localStorage
  useEffect(() => {
    if (selectedWallet) {
      localStorage.setItem('selectedWallet', JSON.stringify(selectedWallet));
    }
  }, [selectedWallet]);

  const login = async (provider = 'credentials', payload?: any) => {
    const result = await signIn(provider, {
      ...payload,
      redirect: false,
    });
    if (!result?.error) {
      router.push(session?.user?.isAccountSet ? '/' : '/account-update');
    } else {
      console.error(result.error);
    }
  };

  const logout = async () => {
    await signOut({ redirect: false });
    localStorage.removeItem('selectedWallet');
    setSelectedWallet(undefined);
    router.push('/');
  };

  return (
    <AuthContext.Provider
      value={{
        isAuthenticated: status === 'authenticated',
        user: session?.user ? (session.user as UserObjectData) : null,
        login,
        logout,
        selectedWallet,
        setSelectedWallet,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuthContext = () => {
  const context = useContext(AuthContext);
  if (!context) throw new Error('useAuthContext must be used within an AuthProvider');
  return context;
};