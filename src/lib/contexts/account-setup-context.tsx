'use client';
import {
  createContext,
  type Dispatch,
  type SetStateAction,
  useContext,
} from 'react';

import type { Refinement } from '../hooks';

interface AccountSetupContextType {
  usernameRefinement: Refinement<string>;
  isValidatingUsername: boolean;
  setIsValidatingUsername: Dispatch<SetStateAction<boolean>>;
}

const AccountSetupContext = createContext<AccountSetupContextType | undefined>(
  undefined
);
export const AccountSetupProvider = ({
  children,
  value,
}: {
  children: React.ReactNode;
  value: AccountSetupContextType;
}) => {
  return (
    <AccountSetupContext.Provider value={value}>
      {children}
    </AccountSetupContext.Provider>
  );
};

export const useAccountSetup = () => {
  const context = useContext(AccountSetupContext);
  if (context === undefined) {
    throw new Error(
      'useAccountSetup must be used within a AccountSetupProvider'
    );
  }
  return context;
};
