import { useMemo } from 'react';

interface SummaryItem {
  label: string;
  value: number;
  key: string;
}

interface UseWithdrawalSummaryProps {
  amount: number;
  feeRate?: number;
}

export const useWithdrawalSummary = ({
  amount = 0,
  feeRate = 0.01,
}: UseWithdrawalSummaryProps) => {
  return useMemo(() => {
    const transactionFee = amount * feeRate;
    const total = amount + transactionFee;

    const summaryItems: SummaryItem[] = [
      {
        key: 'amount',
        label: 'Withdrawal amount',
        value: amount,
      },
      {
        key: 'fee',
        label: 'Transaction fee',
        value: transactionFee,
      },
      {
        key: 'total',
        label: 'Total',
        value: total,
      },
    ];

    return {
      summaryItems,
      transactionFee,
      withdrawalAmount: amount,
      total,
    };
  }, [amount, feeRate]);
};
