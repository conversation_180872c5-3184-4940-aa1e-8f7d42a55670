'use client';
import { useCallback, useState } from 'react';

export type MimeType =
  | 'application/pdf'
  | 'application/vnd.ms-excel'
  | 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  | 'text/plain'
  | 'application/json'
  | 'text/csv'
  | string;

interface DownloadFileOptions {
  url: string;
  fileName: string;
  mimeType: MimeType;
}

interface UseDownloadFileResult {
  downloadFile: (options: DownloadFileOptions) => Promise<void>;
  progress: number;
  isDownloading: boolean;
  error: string | null;
}

export const useDownloadFile = (): UseDownloadFileResult => {
  const [progress, setProgress] = useState<number>(0);
  const [isDownloading, setIsDownloading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const downloadFile = useCallback(
    async ({ url, fileName, mimeType }: DownloadFileOptions) => {
      setProgress(0);
      setIsDownloading(true);
      setError(null);

      try {
        const response = await fetch(url);
        if (!response.ok || !response.body) {
          throw new Error('Failed to fetch file');
        }

        const contentLength = Number(response.headers.get('Content-Length')) || 0;
        const reader = response.body.getReader();
        const chunks: BlobPart[] = [];

        let received = 0;
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;
          if (value) {
            chunks.push(value);
            received += value.length;
            if (contentLength) {
              const percent = Math.round((received / contentLength) * 100);
              setProgress(percent);
            }
          }
        }

        const blob = new Blob(chunks, { type: mimeType });
        const blobUrl = URL.createObjectURL(blob);

        const link = document.createElement('a');
        link.href = blobUrl;
        link.download = fileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(blobUrl);
      } catch (err: any) {
        console.error('Download error:', err);
        setError(err.message || 'Unknown error');
      } finally {
        setIsDownloading(false);
        setProgress(100);
      }
    },
    []
  );

  return {
    downloadFile,
    progress,
    isDownloading,
    error,
  };
};