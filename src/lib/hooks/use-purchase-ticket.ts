'use client';
import { zodResolver } from '@hookform/resolvers/zod';
import { useSearchParams } from 'next/navigation';
import React from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import {
  type EventDiscount,
  useGetEventCategories,
  useGetEventWithSlug,
} from '@/api/events';

import { useAuth } from './use-auth';
import { isPresaleActive, toAmountInMajor } from '../utils';

export interface SelectedTicket {
  category: string;
  quantity: number;
  cost: number;
  isPresale?: boolean;
}

export interface AdditionalFees {
  fee: number;
  discountAmount?: number;
}

export interface CostBreakdown {
  ticketSubtotal: number;
  totalFees: number;
  fees: AdditionalFees;
  total: number;
}

// Create the schema and form setup
export function usePurchaseEventTicket() {
  const { user } = useAuth();
  const searchParams = useSearchParams();

  const eventId = searchParams.get('id') as string;
  const slug = searchParams.get('slug') as string;
  const [editType, setEditType] = React.useState<
    'desc' | 'date' | 'location'
  >();
  const [activeDiscount, setActiveDiscount] = React.useState<EventDiscount>();

  const { data: slugEvent, isFetching: isEventFetching } = useGetEventWithSlug({
    variables: {
      slug: slug || eventId,
      targetCurrency: 'NGN',
      userId: user?.id,
    },
    enabled: !!slug || !!eventId,
  });

  const event = slugEvent;

  const { data: categories, isFetching: isEventCategoriesFetching } =
    useGetEventCategories();

  const [selectedTickets, setSelectedTickets] = React.useState<
    Record<string, number>
  >({});

  const getSelectedTicketsArray = (
    tickets = selectedTickets
  ): SelectedTicket[] => {
    if (!event?.ticketCategories) return [];

    return Object.entries(tickets)
      .filter(([_, quantity]) => quantity > 0)
      .map(([category, quantity]) => {
        const presaleTicket = event?.presaleConfig?.find(
          (presale) =>
            presale.ticketCategoryId === event.ticketCategories[category].id
        );
        const shouldUsePresale =
          presaleTicket && isPresaleActive(presaleTicket);

        const activeTicket = shouldUsePresale
          ? {
              ...event.ticketCategories[category],
              cost: presaleTicket.price,
              quantity: presaleTicket.quantity,
              purchaseLimit:
                presaleTicket.purchaseLimit ||
                event.ticketCategories[category].purchaseLimit,
              description:
                presaleTicket.description ||
                event.ticketCategories[category].description,
            }
          : event.ticketCategories[category];

        const { convertedCost, cost } = activeTicket;

        return {
          category,
          quantity,
          cost: convertedCost || cost,
          ...(shouldUsePresale && { isPresale: shouldUsePresale }),
        };
      });
  };

  const selectedTicketSchema = z.object({
    category: z.string(),
    quantity: z.coerce.number().min(1, 'Quantity must be at least 1'),
    isPresaleTicket: z.boolean().optional(),
  });
  const schema = z.object({
    discountCode: z.string().optional(),
    paymentMethod: z.enum(['wallet', 'online']),
    selectedTickets: z
      .array(selectedTicketSchema)
      .min(1, 'At least one ticket must be selected'),
  });

  const formMethods = useForm<z.infer<typeof schema>>({
    defaultValues: {
      paymentMethod: 'wallet',
      selectedTickets: [],
    },
    resolver: zodResolver(schema),
    mode: 'onBlur',
  });

  const handleTicketQuantityChange = (category: string, quantity: number) => {
    const updatedSelectedTickets = {
      ...selectedTickets,
      [category]: quantity,
    };
    setSelectedTickets(updatedSelectedTickets);

    const selectedArray = getSelectedTicketsArray(updatedSelectedTickets);

    formMethods.setValue(
      'selectedTickets',
      selectedArray.map(({ quantity, category, isPresale }) => ({
        quantity,
        category,
        isPresaleTicket: !!isPresale,
      })),
      {
        shouldValidate: true,
        shouldDirty: true,
      }
    );
  };

  const calculateTotalCost = React.useCallback(
    (additionalFees: AdditionalFees = { fee: 0 }): CostBreakdown => {
      if (!event?.ticketCategories) {
        return {
          ticketSubtotal: 0,
          totalFees: 0,
          fees: additionalFees,
          total: 0,
        };
      }

      // Calculate ticket subtotal
      const ticketSubtotal = Object.entries(selectedTickets).reduce(
        (total, [category, quantity]) => {
          const presaleTicket = event?.presaleConfig?.find(
            (presale) =>
              presale.ticketCategoryId === event.ticketCategories[category].id
          );
          const shouldUsePresale =
            presaleTicket && isPresaleActive(presaleTicket);

          const activeTicket = shouldUsePresale
            ? {
                ...event.ticketCategories[category],
                cost: presaleTicket.price,
                quantity: presaleTicket.quantity,
                purchaseLimit:
                  presaleTicket.purchaseLimit ||
                  event.ticketCategories[category].purchaseLimit,
                description:
                  presaleTicket.description ||
                  event.ticketCategories[category].description,
              }
            : event.ticketCategories[category];
          if (activeTicket && quantity > 0) {
            const cost = activeTicket.convertedCost || activeTicket.cost;
            return total + toAmountInMajor(cost) * quantity;
          }
          return total;
        },
        0
      );

      const discountAmount = additionalFees?.discountAmount || 0;

      const totalFees = ticketSubtotal * additionalFees?.fee;

      // const totalFees = Object.entries(additionalFees)
      //   .filter(([key]) => key !== 'discountAmount')
      //   .reduce((sum, [, fee]) => sum + (fee || 0), 0);

      // Ensure discount is not more than subtotal + fees
      const maxDiscount = Math.min(discountAmount, ticketSubtotal + totalFees);

      // Calculate final total
      const total = ticketSubtotal + totalFees - maxDiscount;

      return {
        ticketSubtotal,
        totalFees,
        fees: additionalFees,
        total,
      };
    },
    [selectedTickets, event?.ticketCategories]
  );

  const getTotalQuantity = React.useCallback(() => {
    return Object.values(selectedTickets).reduce(
      (total, quantity) => total + quantity,
      0
    );
  }, [selectedTickets]);

  // Reset form and selected tickets
  const resetForm = React.useCallback(() => {
    setSelectedTickets({});
    formMethods.reset();
  }, [formMethods]);

  const hasSelectedTickets = React.useMemo(() => {
    return Object.values(selectedTickets).some((quantity) => quantity > 0);
  }, [selectedTickets]);

  // const ticketSubtotal = React.useMemo(() => {
  //   if (!event?.ticketCategories) return 0;

  //   return Object.entries(selectedTickets).reduce(
  //     (total, [category, quantity]) => {
  //       const ticketCategory = event.ticketCategories[category];
  //       if (!ticketCategory || quantity <= 0) return total;

  //       const cost = ticketCategory.convertedCost || ticketCategory.cost;
  //       return total + toAmountInMajor(cost) * quantity;
  //     },
  //     0
  //   );
  // }, [selectedTickets, event?.ticketCategories]);

  // const discountAmount = React.useMemo(() => {
  //   if (!activeDiscount) return 0;

  //   if (activeDiscount.discountType === 'AMOUNT') {
  //     return activeDiscount.discountValue;
  //   }

  //   // PERCENTAGE
  //   return ((activeDiscount.discountValue || 0) / 100) * ticketSubtotal;
  // }, [activeDiscount, ticketSubtotal]);

  return {
    event,
    categories,
    isEventLoading: isEventCategoriesFetching || isEventFetching,
    selectedTickets,
    handleTicketQuantityChange,
    getSelectedTicketsArray,
    hasSelectedTickets,
    schema,
    formMethods,
    resetForm,
    calculateTotalCost,
    getTotalQuantity,
    eventId,
    editType,
    setEditType,
    activeDiscount,
    setActiveDiscount,
  };
}

export type PurchaseTicketFormType = z.infer<
  ReturnType<typeof usePurchaseEventTicket>['schema']
>;
