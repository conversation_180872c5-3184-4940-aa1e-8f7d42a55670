import { useSession, signIn, signOut } from 'next-auth/react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import type { Session } from 'next-auth';
import type { LoginPayload } from '@/types';

export const useAuth = () => {
  const { data: session, status } = useSession() as {
    data: Session | null;
    status: 'loading' | 'authenticated' | 'unauthenticated';
  };
  const queryClient = useQueryClient();

  const handleAuthError = (result: any) => {
    if (result?.error) throw new Error(result.error);
    return result;
  };

  const loginMutation = useMutation({
    mutationFn: async (credentials: LoginPayload) =>
      handleAuthError(
        await signIn('credentials', { ...credentials, redirect: false })
      ),
    onSuccess: () => queryClient.invalidateQueries(),
  });

  const socialLoginMutation = useMutation({
    mutationFn: async ({ type }: { type: 'google' | 'apple' }) =>
      handleAuthError(await signIn(type, { redirect: false })),
    onSuccess: () => queryClient.invalidateQueries(),
  });

  const logoutMutation = useMutation({
    mutationFn: async () => {
      await signOut({ redirect: false });
    },
    onSuccess: () => queryClient.clear(),
  });

  return {
    session,
    status,
    isLoading: status === 'loading',
    isAuthenticated: status === 'authenticated',
    user: session?.user,
    accessToken: session?.accessToken,
    // login
    login: loginMutation.mutate,
    loginAsync: loginMutation.mutateAsync,
    isLoggingIn: loginMutation.isPending,
    loginError: loginMutation.error,
    // social login
    socialLogin: socialLoginMutation.mutate,
    socialLoginAsync: socialLoginMutation.mutateAsync,
    isSocialLoggingIn: socialLoginMutation.isPending,
    socialLoginError: socialLoginMutation.error,
    // logout
    logout: logoutMutation.mutate,
    logoutAsync: logoutMutation.mutateAsync,
    isLoggingOut: logoutMutation.isPending,
  };
};
