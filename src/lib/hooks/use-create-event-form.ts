import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { eventCreateSchema } from '@/lib/schemas';

export function useCreateEventForm() {
  const defaultTicket = {
    id: Math.random()
      .toString(36)
      .substring(2, 15)
      .concat(Date.now().toString(36)),
    name: '',
    price: 0,
    quantity: 0,
    description: '',
    hasPresale: false,
    hasTimeline: false,
    hasPurchaseLimit: false,
    purchaseLimit: 0,
    startDatetime: undefined,
    endDatetime: undefined,
  };

  const formMethods = useForm<z.input<typeof eventCreateSchema>>({
    defaultValues: {
      tickets: [defaultTicket],
      registrationFields: [],
      collaborators: [],
      onlineUrl: 'https://',
      timezone: 'W. Central Africa Standard Time',
    },
    resolver: zodResolver(eventCreateSchema),
    mode: 'onBlur',
  });

  return {
    formMethods,
    schema: eventCreateSchema,
    defaultTicket,
  };
}

export type CreateEventFormType = z.infer<typeof eventCreateSchema>;