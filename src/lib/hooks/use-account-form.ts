'use client';
import { zodResolver } from '@hookform/resolvers/zod';
import { type AxiosError } from 'axios';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { apiVerifyUsername, USER_ROLE_VALUES } from '@/api/auth';
import { type ErrorResponse } from '@/api/common/types';

import { NO_SPACE_REGEX, PASSWORD_REGEX, USERNAME_REGEX } from '../utils';
import useRefinement, { type RefinementCallback } from './use-refinement';

// Create the schema and form setup
export function useAccountSetupForm() {
  const [isValidatingUsername, setIsValidatingUsername] = useState(false);

  const checkUsernameToBeValid = (): RefinementCallback<string> => {
    return async (username: string, { signal }) => {
      if (!username) return false;

      try {
        const res = await apiVerifyUsername(
          username.trim().toLowerCase(),
          signal
        );
        console.log('🚀 ~ return ~ res:', res.data);

        return true;
      } catch (err: any) {
        if (err.name === 'AbortError') {
          // Request was aborted, don't treat as validation failure
          console.log('Username validation aborted');
          return true;
        }
        const error = err as AxiosError<ErrorResponse>;
        console.log('Username validation error:', error.response?.data.message);
        return false;
      } finally {
        setIsValidatingUsername(false);
      }
    };
  };

  const usernameRefinement = useRefinement<string>(checkUsernameToBeValid(), {
    debounce: 500,
  });

  const schema = z
    .object({
      role: z.enum(USER_ROLE_VALUES),
      email: z
        .email('Invalid email format').
        nonempty('Email is required'),
      password: z
        .string()
        .min(8, 'Password must be at least 8 characters long')
        .regex(
          PASSWORD_REGEX.twoLowerCaseRegex,
          'Must contain at least 2 lowercase letters'
        )
        .regex(
          PASSWORD_REGEX.oneUpperCaseRegex,
          'Must contain at least 1 uppercase letter'
        )
        .regex(PASSWORD_REGEX.twoDigitsRegex, 'Must contain at least 2 digits')
        .regex(
          PASSWORD_REGEX.oneSpecialCharacter,
          'Must contain at least 1 special character'
        ),
      confirmPassword: z.string(),
      firstName: z.string(),
      lastName: z.string(),
      username: z
        .string()
        .min(3, 'Username must be at least 3 characters long')
        .regex(NO_SPACE_REGEX, 'Spaces are not allowed in the username')
        .regex(
          USERNAME_REGEX.noStartEndDotUnderscore,
          'Username cannot start or end with a dot or underscore'
        )
        .regex(
          USERNAME_REGEX.startWithAlphanumeric,
          'Username must start with an alphanumeric character'
        )
        .regex(
          USERNAME_REGEX.validCharacters,
          'Username can only contain alphanumeric characters, dots, or underscores'
        )
        .transform((value) => value.trim().toLowerCase())
        .refine(usernameRefinement, {
          message: 'Username is already taken',
          params: { asyncValidation: true },
        }),
      country: z.string(),
    })
    .refine((data) => data.password === data.confirmPassword, {
      message: 'Passwords do not match',
      path: ['confirmPassword'],
    });

  const formMethods = useForm<z.infer<typeof schema>>({
    defaultValues: {
      email: '',
      password: '',
      confirmPassword: '',
      firstName: '',
      lastName: '',
      username: '',
      country: '',
    },
    resolver: zodResolver(schema),
    mode: 'onChange',
  });

  return {
    formMethods,
    usernameRefinement,
    schema,
    isValidatingUsername,
    setIsValidatingUsername,
  };
}

export type FormType = z.infer<
  ReturnType<typeof useAccountSetupForm>['schema']
>;
