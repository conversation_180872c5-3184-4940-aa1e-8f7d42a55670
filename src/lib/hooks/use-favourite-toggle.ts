import { type QueryKey, useQueryClient } from '@tanstack/react-query';
import _ from 'lodash';
import React from 'react';
import { toast } from 'react-hot-toast';

import { useAddFavourite, useRemoveFavourite } from '@/api/users';

export type AddFavouritePayload = {
  type: 'EVENT' | 'ACCOUNT';
  accountId: string | null;
  eventId: string | null;
};

interface UseFavoriteToggleOptions {
  initialFavoriteState: boolean;
  payload: AddFavouritePayload;
  debounceMs?: number;
  successMessages?: {
    add?: string;
    remove?: string;
  };
  invalidateQueries?: QueryKey[];
}

export const useFavoriteToggle = ({
  initialFavoriteState,
  payload,
  debounceMs = 300,
  successMessages = {
    add: 'Added to favorites',
    remove: 'Removed from favorites',
  },
  invalidateQueries = [['getUserFavourites', { type: payload.type }]],
}: UseFavoriteToggleOptions) => {
  const [favourited, setFavourited] = React.useState(initialFavoriteState);
  const queryClient = useQueryClient();

  const { mutate: addToFavourite } = useAddFavourite();
  const { mutate: removeFromFavourite } = useRemoveFavourite();

  const handleFavToggle = React.useCallback(() => {
    const debouncedToggle = _.debounce(() => {
      const newFavStatus = !favourited;
      setFavourited(newFavStatus); // Optimistically update UI

      if (newFavStatus) {
        addToFavourite(payload, {
          onSuccess: () => {
            toast.success(successMessages.add!);
            invalidateQueries.forEach((queryKey) => {
              queryClient.invalidateQueries({
                queryKey: queryKey,
              });
            });
          },
          onError: (error) => {
            // Revert optimistic update
            setFavourited((prev) => !prev);
            toast.error(error.message);
          },
        });
      } else {
        removeFromFavourite(payload, {
          onSuccess: () => {
            toast.success(successMessages.remove!);
            invalidateQueries.forEach((queryKey) => {
              queryClient.invalidateQueries({
                queryKey: queryKey,
              });
            });
          },
          onError: (error) => {
            // Revert optimistic update
            setFavourited((prev) => !prev);
            toast.error(error.message);
          },
        });
      }
    }, debounceMs);

    return debouncedToggle();
  }, [
    favourited,
    payload,
    addToFavourite,
    removeFromFavourite,
    queryClient,
    successMessages,
    invalidateQueries,
    debounceMs,
  ]);

  // Sync with external state changes
  React.useEffect(() => {
    setFavourited(initialFavoriteState);
  }, [initialFavoriteState]);

  return {
    favourited,
    handleFavToggle,
    setFavourited, // In case you need manual control
  };
};
