'use client';
import { useState, useCallback } from 'react';

const IS_FIRST_TIME = 'IS_FIRST_TIME';

export const useIsFirstTime = () => {
  const [isFirstTime, setIsFirstTimeState] = useState<boolean>(() => {
    const storedValue = localStorage.getItem(IS_FIRST_TIME);
    return storedValue === null ? true : storedValue === 'true';
  });

  const setIsFirstTime = useCallback((value: boolean) => {
    localStorage.setItem(IS_FIRST_TIME, String(value));
    setIsFirstTimeState(value);
  }, []);

  return [isFirstTime, setIsFirstTime] as const;
};