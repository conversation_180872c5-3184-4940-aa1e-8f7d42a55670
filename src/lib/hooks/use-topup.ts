'use client';
import { useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'react-hot-toast';

import { getQueryClient } from '@/api';
import { useInitializeTransaction } from '@/api/transactions';
import { useVerifyPaystackTransaction } from '@/api/transactions/use-verify-paystack-transaction';
import { useAuth } from '@/lib/hooks/use-auth';

declare global {
  interface Window {
    PaystackPop?: {
      setup: (config: Record<string, any>) => { openIframe: () => void };
    };
  }
}

export const useTopup = ({ amount }: { amount: number }) => {
  const router = useRouter();
  const queryClient = getQueryClient();
  const { email, fullName, id: userId } = useAuth().user!;
  const { mutate: initializeTransaction, isPending: initializing } =
    useInitializeTransaction();
  const { mutate: verifyPaystackTransaction } = useVerifyPaystackTransaction();

  const formatMetadata = (metadata: {
    purpose: string;
    user: { fullName: string; email: string };
    userId: string;
  }) => ({
    custom_fields: [
      {
        display_name: 'Purpose',
        variable_name: 'purpose',
        value: metadata.purpose,
      },
      {
        display_name: 'User Details',
        variable_name: 'user',
        value: JSON.stringify(metadata.user),
      },
      {
        display_name: 'User ID',
        variable_name: 'userId',
        value: metadata.userId,
      },
    ],
  });

  const topup = useCallback(
    ({ reference }: { reference: string }) => {
      if (!window.PaystackPop) {
        toast.error('Paystack SDK not loaded');
        return;
      }

      const handler = window.PaystackPop.setup({
        key: process.env.NEXT_PUBLIC_PAYSTACK_PUBLIC_KEY,
        email,
        amount,
        ref: reference,
        metadata: formatMetadata({
          purpose: 'WALLET_TOPUP',
          user: { email, fullName },
          userId,
        }),
        callback: (response: { reference: string }) => {
          verifyPaystackTransaction(
            { transactionRef: response.reference },
            {
              onSuccess: ({ status }) => {
                queryClient.invalidateQueries({ queryKey: ['getUser'] });
                queryClient.invalidateQueries({
                  queryKey: ['getTransactionHistory'],
                });
                if (status === 'success') {
                  toast.success('Transaction Successful');
                } else {
                  toast.error('Transaction could not be verified');
                }
                router.back();
              },
              onError: (error) => toast.error(error.message),
            }
          );
        },
        onClose: () => {
          console.log('User closed payment');
        },
      });

      handler.openIframe();
    },
    [email, fullName, userId, amount, verifyPaystackTransaction, router]
  );

  return { topup, initializeTransaction, initializing };
};