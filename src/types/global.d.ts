declare global {

  interface LocationObject {
    city: string;
    state: string;
    street: string;
    address: string;
    country: string;
    landmark?: string;
    coordinates: Point;
  }
  interface CustomError {
    message: string;
    statusCode: number;
  }

  interface NavigationItem {
    title: string;
    href: string;
    Icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
  }

  interface FollowingUser {
    id: string;
    displayName: string;
    username: string;
    avatar: string;
  }

  type SidebarSection = {
    title?: string;
    items: NavigationItem[];
  };
  interface FormData {
    [key: string]: any;
  }
}

export default global;

export interface Timezone {
  value: string;
  abbr: string;
  offset: number;
  isdst: boolean;
  text: string;
  utc: string[];
}

declare const TIMEZONES_JSON: Timezone[];

export default TIMEZONES_JSON;
