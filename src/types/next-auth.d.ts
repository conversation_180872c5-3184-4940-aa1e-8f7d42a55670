import { UserObjectData } from '@/api';
import NextA<PERSON> from 'next-auth';
import { DefaultUser } from 'next-auth';

declare module 'next-auth' {
  interface Session {
    user: UserObjectData;
    accessToken: string;
  }

  interface User extends DefaultUser, UserObjectData {
    accessToken: string;
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    user: UserObjectData;
    accessToken: string;
  }
}
