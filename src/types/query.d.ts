import '@tanstack/react-query';

type QueryKey = [
  (
    | 'getUser'
    | 'getEventCategories'
    | 'getEventTicket'
    | 'getEvent'
    | 'getEventWithSlug'
    | 'getEvents'
    | 'searchEvents'
    | 'spotifySearchArtist'
    | 'spotifySearchArtistById'
    | 'verifyEventTicket'
    | 'getAgoraRtmToken'
    | 'getAgoraToken'
    | 'getRecentLiveSessions'
    | 'getRequestHistory'
    | 'searchLiveSessions'
    | 'getSessionConnectionStatus'
    | 'spotifySearchMusic'
    | 'getUserFavourites'
    | 'getTransactionHistory'
    | 'getUserTickets'
    | 'getUserTickets'
    | 'users'
    | 'getHomeData'
    | 'searchUsers'
    | 'getSessions'
    | 'getSessionViewers'
    | 'guessNuban'
    | 'verifyNuban'
    | 'nearby-users'
    | 'getUserFavourites'
    | 'nearbySearch'
    | 'textSearch'
    | 'getTrendingCreators'
    | 'getTrendingEvents'
    | 'getEventAccessCodes'
    | 'getEventDiscounts'
    | 'getLiveSession'
    | 'getLiveSessionQueues'
    | 'reserveEventTicket'
    | 'updatedReserveEventTicket'
    | 'getEventGuestList'
    | 'getTransaction'
    | 'auth-session'
  ),
  ...(readonly unknown[]),
];

declare module '@tanstack/react-query' {
  interface Register {
    queryKey: QueryKey;
    mutationKey: QueryKey;
  }
}
