export type NotificationData = {
  id: number | string;
  text: string;
  imgUrl?: string;
  date: Date;
  relativeTimeFromNow?: string;
};

export type GeneralState = {
  loading: boolean;
  socketConnected: boolean;
  searhQuery: string;
  notification: NotificationData[];
};

export type OptionType = { label: string; value: string | number };

import { z } from 'zod';

export type LiveChatComment = {
  id: string | number;
  fullName: string;
  userName: string;
  avatar: string;
  message: string;
};

// Define tabs
export type TabType = 'Request' | 'Queue' | 'History';

// Define mock data interface

export const LIVE_SESSION_TYPES = ['request', 'shoutout'] as const;

export type LIVE_TYPE = (typeof LIVE_SESSION_TYPES)[number];

export const Coordinates = z.object({
  lat: z.number(),
  lng: z.number(),
});

export type CoordinatesType = z.infer<typeof Coordinates>;

export const Location = z.object({
  city: z.string(),
  state: z.string(),
  street: z.string(),
  country: z.string().toUpperCase(),
  address: z.string(),
  landmark: z.string().optional(),
  coordinates: Coordinates,
});

export type LocationType = z.infer<typeof Location>;

export type ReportType =
  | 'spam'
  | 'inappropriate'
  | 'copyright'
  | 'underage'
  | 'none';

export enum NotificationType {
  EVENT = 'EVENT',
  DJ_SESSION = 'DJ_SESSION',
  PRIVATE_CHAT = 'PRIVATE_CHAT',
}

export interface NotificationExtraData {
  type?: NotificationType;
  eventId?: string;
  djSessionId?: string;
  songId?: string;
  userId?: string;
  username?: string;
  userAvatar?: string;
  chatRoomId?: string;
  // [key: string]: string;
}

export type TabScreenItem = {
  key: string;
  title: string;
  component: React.ComponentType;
  notificationCount?: number;
  data?: any;
};

export type Point = {
  lat: number;
  lng: number;
  latitude?: number;
  longitude?: number;
};
