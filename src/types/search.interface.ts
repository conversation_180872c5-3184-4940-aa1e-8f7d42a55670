import { IEvent, UserObjectData } from '@/api';

export interface SearchResultResponse {
  total: number;
  users: UserObjectData[];
}

export interface ISearchResultResponse {
  total: number;
  users: IEvent[];
}

export interface SearchResultInterface {
  loading?: boolean;
  results: UserObjectData[];
  total: number;
  take: number;
  skip: number;
}

export interface ISearchResultInterface {
  loading?: boolean;
  results: IEvent[];
  total: number;
  take: number;
  skip: number;
}
