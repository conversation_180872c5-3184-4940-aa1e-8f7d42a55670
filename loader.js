'use client';

import { env } from '@/env.mjs';

// const POPLA_IMAGE_OPTIMIZATION_DOMAIN = 'https://images.getpopla.com';
// const APP_BASE_URL =
//   process.env.NODE_ENV === 'development'
//     ? 'https://dev.app.getpopla.com'
//     : 'https://app.getpopla.com';

export default function poplaImageLoader({ src, width, quality }) {
  const isLocal = !src.startsWith('http');
  const query = new URLSearchParams();

  const imageOptimizationApi = env.NEXT_PUBLIC_POPLA_IMAGE_OPTIMIZATION_DOMAIN;
  // Your NextJS application URL
  const baseUrl = env.NEXT_PUBLIC_APP_BASE_URL;

  const fullSrc = `${baseUrl}${src}`;

  if (width) query.set('width', width.toString());
  if (quality) query.set('quality', quality.toString());

  if (isLocal && process.env.NODE_ENV === 'development') {
    return src;
  }
  if (isLocal) {
    return `${imageOptimizationApi}/image/${fullSrc}?${query.toString()}`;
  }
  return `${imageOptimizationApi}/image/${src}?${query.toString()}`;
}
